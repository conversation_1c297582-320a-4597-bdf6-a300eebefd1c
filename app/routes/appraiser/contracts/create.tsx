import { useState } from 'react';
import { createFileRout<PERSON>, <PERSON> } from '@tanstack/react-router';
import { useAuth } from '@/lib/auth-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Plus, Trash2, Save, Send, ArrowLeft } from 'lucide-react';
import { mockUsers, mockDepartments } from '@/lib/mock-data';
import { getGradeRange, getContractTypeFromGrade } from '@/lib/types';
import { toast } from 'sonner';

export const Route = createFileRoute('/appraiser/contracts/create')({
  component: CreateContractPage,
});

interface ContractForm {
  title: string;
  appraiseeId: string;
  departmentId: string;
  contractType: 'Self' | 'Other';
  evidence: string;
}

interface ActivityItem {
  id: string;
  title: string;
  unit: string;
  weight: string;
  measure: string;
  target: string;
  allowableVariance: string;
  actions: string;
}

interface OutputItem {
  id: string;
  title: string;
  unit: string;
  weight: string;
  measure: string;
  target: string;
  allowableVariance: string;
  actions: string;
  evidenceOfResults: string;
}

interface ProgrammeItem {
  id: string;
  title: string;
  outcomes: OutcomeItem[];
}

interface OutcomeItem {
  id: string;
  title: string;
  weight: string;
  outputs: OutputItem[];
}

function CreateContractPage() {
  const { user } = useAuth();
  const [contractForm, setContractForm] = useState<ContractForm>({
    title: '',
    appraiseeId: '',
    departmentId: '',
    contractType: 'Other',
    evidence: ''
  });
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [outputs, setOutputs] = useState<OutputItem[]>([]);
  const [programmes, setProgrammes] = useState<ProgrammeItem[]>([]);
  const [selectedAppraisee, setSelectedAppraisee] = useState<any>(null);

  if (!user || user.role !== 'Appraiser') {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-netone-black mb-4">Access Denied</h1>
          <p className="text-gray-600">You don't have permission to access this page.</p>
        </div>
      </div>
    );
  }

  const appraisees = mockUsers.filter(u => u.role === 'Appraisee' || u.role === 'Appraiser');

  const handleAppraiseeChange = (appraiseeId: string) => {
    const appraisee = mockUsers.find(u => u.id === appraiseeId);
    setSelectedAppraisee(appraisee);
    setContractForm({
      ...contractForm,
      appraiseeId,
      departmentId: appraisee?.departmentId || ''
    });
    
    // Clear existing items when changing appraisee
    setActivities([]);
    setOutputs([]);
    setProgrammes([]);
  };

  const addActivity = () => {
    const newActivity: ActivityItem = {
      id: Date.now().toString(),
      title: '',
      unit: '',
      weight: '',
      measure: '',
      target: '',
      allowableVariance: '',
      actions: ''
    };
    setActivities([...activities, newActivity]);
  };

  const addOutput = () => {
    const newOutput: OutputItem = {
      id: Date.now().toString(),
      title: '',
      unit: '',
      weight: '',
      measure: '',
      target: '',
      allowableVariance: '',
      actions: '',
      evidenceOfResults: ''
    };
    setOutputs([...outputs, newOutput]);
  };

  const addProgramme = () => {
    const newProgramme: ProgrammeItem = {
      id: Date.now().toString(),
      title: '',
      outcomes: []
    };
    setProgrammes([...programmes, newProgramme]);
  };

  const addOutcome = (programmeId: string) => {
    const newOutcome: OutcomeItem = {
      id: Date.now().toString(),
      title: '',
      weight: '',
      outputs: []
    };
    setProgrammes(programmes.map(p => 
      p.id === programmeId 
        ? { ...p, outcomes: [...p.outcomes, newOutcome] }
        : p
    ));
  };

  const addOutputToProgramme = (programmeId: string, outcomeId: string) => {
    const newOutput: OutputItem = {
      id: Date.now().toString(),
      title: '',
      unit: '',
      weight: '',
      measure: '',
      target: '',
      allowableVariance: '',
      actions: '',
      evidenceOfResults: ''
    };
    setProgrammes(programmes.map(p => 
      p.id === programmeId 
        ? {
            ...p,
            outcomes: p.outcomes.map(o =>
              o.id === outcomeId
                ? { ...o, outputs: [...o.outputs, newOutput] }
                : o
            )
          }
        : p
    ));
  };

  const updateActivity = (id: string, field: keyof ActivityItem, value: string) => {
    setActivities(activities.map(activity => 
      activity.id === id ? { ...activity, [field]: value } : activity
    ));
  };

  const updateOutput = (id: string, field: keyof OutputItem, value: string) => {
    setOutputs(outputs.map(output => 
      output.id === id ? { ...output, [field]: value } : output
    ));
  };

  const removeActivity = (id: string) => {
    setActivities(activities.filter(activity => activity.id !== id));
  };

  const removeOutput = (id: string) => {
    setOutputs(outputs.filter(output => output.id !== id));
  };

  const removeProgramme = (id: string) => {
    setProgrammes(programmes.filter(programme => programme.id !== id));
  };

  const handleSaveDraft = () => {
    if (!contractForm.title || !contractForm.appraiseeId) {
      toast.error('Please fill in the required fields');
      return;
    }
    
    toast.success('Contract saved as draft');
  };

  const handleSubmit = () => {
    if (!contractForm.title || !contractForm.appraiseeId) {
      toast.error('Please fill in all required fields');
      return;
    }

    const gradeRange = selectedAppraisee ? getGradeRange(selectedAppraisee.grade) : null;
    
    if (gradeRange === '6-10' && activities.length === 0) {
      toast.error('Please add at least one activity for this grade level');
      return;
    }
    
    if (gradeRange === '11-18' && outputs.length === 0) {
      toast.error('Please add at least one output for this grade level');
      return;
    }
    
    if (gradeRange === '19-23' && programmes.length === 0) {
      toast.error('Please add at least one programme for this grade level');
      return;
    }
    
    toast.success('Contract submitted successfully');
  };

  const getContractTypeInfo = () => {
    if (!selectedAppraisee) return null;
    
    const gradeRange = getGradeRange(selectedAppraisee.grade);
    const contractType = getContractTypeFromGrade(selectedAppraisee.grade);
    
    return { gradeRange, contractType };
  };

  const contractTypeInfo = getContractTypeInfo();

  const renderGradeSpecificForm = () => {
    if (!selectedAppraisee) return null;

    const gradeRange = getGradeRange(selectedAppraisee.grade);

    switch (gradeRange) {
      case '6-10':
        return (
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Individual Activities (Grade 6-10)</CardTitle>
                  <CardDescription>Add performance activities for this employee</CardDescription>
                </div>
                <Button onClick={addActivity} className="bg-netone-orange hover:bg-netone-orange/90">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Activity
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {activities.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <p>No activities added yet.</p>
                  <p className="text-sm">Click "Add Activity" to get started.</p>
                </div>
              ) : (
                <div className="space-y-6">
                  {activities.map((activity, index) => (
                    <div key={activity.id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-center mb-4">
                        <h4 className="font-semibold">Activity {index + 1}</h4>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => removeActivity(activity.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label>Activity Title</Label>
                          <Input
                            value={activity.title}
                            onChange={(e) => updateActivity(activity.id, 'title', e.target.value)}
                            placeholder="Activity title"
                          />
                        </div>
                        
                        <div>
                          <Label>Unit of Measure</Label>
                          <Input
                            value={activity.unit}
                            onChange={(e) => updateActivity(activity.id, 'unit', e.target.value)}
                            placeholder="e.g., Percentage, Number, etc."
                          />
                        </div>
                        
                        <div>
                          <Label>Weight (%)</Label>
                          <Input
                            value={activity.weight}
                            onChange={(e) => updateActivity(activity.id, 'weight', e.target.value)}
                            placeholder="e.g., 25"
                            type="number"
                          />
                        </div>
                        
                        <div>
                          <Label>Target</Label>
                          <Input
                            value={activity.target}
                            onChange={(e) => updateActivity(activity.id, 'target', e.target.value)}
                            placeholder="Target value"
                          />
                        </div>
                        
                        <div>
                          <Label>Allowable Variance (%)</Label>
                          <Input
                            value={activity.allowableVariance}
                            onChange={(e) => updateActivity(activity.id, 'allowableVariance', e.target.value)}
                            placeholder="e.g., 5"
                            type="number"
                          />
                        </div>
                        
                        <div>
                          <Label>Measure</Label>
                          <Input
                            value={activity.measure}
                            onChange={(e) => updateActivity(activity.id, 'measure', e.target.value)}
                            placeholder="How this will be measured"
                          />
                        </div>
                      </div>
                      
                      <div className="mt-4">
                        <Label>Actions Required</Label>
                        <Textarea
                          value={activity.actions}
                          onChange={(e) => updateActivity(activity.id, 'actions', e.target.value)}
                          placeholder="Describe the actions required to achieve this target..."
                          rows={2}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        );

      case '11-18':
        return (
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Individual Outputs (Grade 11-18)</CardTitle>
                  <CardDescription>Add performance outputs for this employee</CardDescription>
                </div>
                <Button onClick={addOutput} className="bg-netone-orange hover:bg-netone-orange/90">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Output
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {outputs.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <p>No outputs added yet.</p>
                  <p className="text-sm">Click "Add Output" to get started.</p>
                </div>
              ) : (
                <div className="space-y-6">
                  {outputs.map((output, index) => (
                    <div key={output.id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-center mb-4">
                        <h4 className="font-semibold">Output {index + 1}</h4>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => removeOutput(output.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label>Output Title</Label>
                          <Input
                            value={output.title}
                            onChange={(e) => updateOutput(output.id, 'title', e.target.value)}
                            placeholder="Output title"
                          />
                        </div>
                        
                        <div>
                          <Label>Unit of Measure</Label>
                          <Input
                            value={output.unit}
                            onChange={(e) => updateOutput(output.id, 'unit', e.target.value)}
                            placeholder="e.g., Percentage, Number, etc."
                          />
                        </div>
                        
                        <div>
                          <Label>Weight (%)</Label>
                          <Input
                            value={output.weight}
                            onChange={(e) => updateOutput(output.id, 'weight', e.target.value)}
                            placeholder="e.g., 25"
                            type="number"
                          />
                        </div>
                        
                        <div>
                          <Label>Target</Label>
                          <Input
                            value={output.target}
                            onChange={(e) => updateOutput(output.id, 'target', e.target.value)}
                            placeholder="Target value"
                          />
                        </div>
                        
                        <div>
                          <Label>Allowable Variance (%)</Label>
                          <Input
                            value={output.allowableVariance}
                            onChange={(e) => updateOutput(output.id, 'allowableVariance', e.target.value)}
                            placeholder="e.g., 5"
                            type="number"
                          />
                        </div>
                        
                        <div>
                          <Label>Measure</Label>
                          <Input
                            value={output.measure}
                            onChange={(e) => updateOutput(output.id, 'measure', e.target.value)}
                            placeholder="How this will be measured"
                          />
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        <div>
                          <Label>Actions Required</Label>
                          <Textarea
                            value={output.actions}
                            onChange={(e) => updateOutput(output.id, 'actions', e.target.value)}
                            placeholder="Describe the actions required..."
                            rows={2}
                          />
                        </div>
                        
                        <div>
                          <Label>Evidence of Results</Label>
                          <Textarea
                            value={output.evidenceOfResults}
                            onChange={(e) => updateOutput(output.id, 'evidenceOfResults', e.target.value)}
                            placeholder="What evidence will demonstrate achievement..."
                            rows={2}
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        );

      case '19-23':
        return (
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Programmes (Grade 19-23)</CardTitle>
                  <CardDescription>Add programmes with outcomes and outputs</CardDescription>
                </div>
                <Button onClick={addProgramme} className="bg-netone-orange hover:bg-netone-orange/90">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Programme
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {programmes.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <p>No programmes added yet.</p>
                  <p className="text-sm">Click "Add Programme" to get started.</p>
                </div>
              ) : (
                <div className="space-y-6">
                  {programmes.map((programme, index) => (
                    <div key={programme.id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-center mb-4">
                        <h4 className="font-semibold">Programme {index + 1}</h4>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => removeProgramme(programme.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                      
                      <div className="mb-4">
                        <Label>Programme Title</Label>
                        <Input
                          value={programme.title}
                          onChange={(e) => setProgrammes(programmes.map(p => 
                            p.id === programme.id ? { ...p, title: e.target.value } : p
                          ))}
                          placeholder="Programme title"
                        />
                      </div>

                      <div className="ml-4">
                        <div className="flex justify-between items-center mb-2">
                          <h5 className="font-medium">Outcomes</h5>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => addOutcome(programme.id)}
                          >
                            <Plus className="mr-2 h-3 w-3" />
                            Add Outcome
                          </Button>
                        </div>
                        
                        {programme.outcomes.map((outcome, outcomeIndex) => (
                          <div key={outcome.id} className="border rounded p-3 mb-3">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                              <div>
                                <Label>Outcome Title</Label>
                                <Input
                                  value={outcome.title}
                                  onChange={(e) => setProgrammes(programmes.map(p => 
                                    p.id === programme.id 
                                      ? {
                                          ...p,
                                          outcomes: p.outcomes.map(o =>
                                            o.id === outcome.id ? { ...o, title: e.target.value } : o
                                          )
                                        }
                                      : p
                                  ))}
                                  placeholder="Outcome title"
                                />
                              </div>
                              
                              <div>
                                <Label>Weight (%)</Label>
                                <Input
                                  value={outcome.weight}
                                  onChange={(e) => setProgrammes(programmes.map(p => 
                                    p.id === programme.id 
                                      ? {
                                          ...p,
                                          outcomes: p.outcomes.map(o =>
                                            o.id === outcome.id ? { ...o, weight: e.target.value } : o
                                          )
                                        }
                                      : p
                                  ))}
                                  placeholder="Weight"
                                  type="number"
                                />
                              </div>
                            </div>

                            <div className="ml-4">
                              <div className="flex justify-between items-center mb-2">
                                <h6 className="text-sm font-medium">Outputs</h6>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => addOutputToProgramme(programme.id, outcome.id)}
                                >
                                  <Plus className="mr-2 h-3 w-3" />
                                  Add Output
                                </Button>
                              </div>
                              
                              {outcome.outputs.map((output, outputIndex) => (
                                <div key={output.id} className="bg-gray-50 rounded p-2 mb-2">
                                  <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-sm">
                                    <Input
                                      value={output.title}
                                      placeholder="Output title"
                                      className="text-sm"
                                    />
                                    <Input
                                      value={output.target}
                                      placeholder="Target"
                                      className="text-sm"
                                    />
                                    <Input
                                      value={output.weight}
                                      placeholder="Weight %"
                                      type="number"
                                      className="text-sm"
                                    />
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        );

      default:
        return null;
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center space-x-4 mb-4">
          <Link to="/appraiser">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Dashboard
            </Button>
          </Link>
        </div>
        <h1 className="text-3xl font-bold text-netone-black mb-2">Create Performance Contract</h1>
        <p className="text-gray-600">Create a new performance contract for an employee</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Contract Details */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Contract Information</CardTitle>
              <CardDescription>Basic contract details and assignment</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="title">Contract Title</Label>
                <Input
                  id="title"
                  value={contractForm.title}
                  onChange={(e) => setContractForm({...contractForm, title: e.target.value})}
                  placeholder="Performance Contract 2024"
                />
              </div>
              
              <div>
                <Label htmlFor="appraisee">Appraisee</Label>
                <Select value={contractForm.appraiseeId} onValueChange={handleAppraiseeChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select employee to appraise" />
                  </SelectTrigger>
                  <SelectContent>
                    {appraisees.map(employee => (
                      <SelectItem key={employee.id} value={employee.id}>
                        {employee.name} - Grade {employee.grade} ({employee.title})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="contractType">Contract Type</Label>
                <Select 
                  value={contractForm.contractType} 
                  onValueChange={(value: 'Self' | 'Other') => setContractForm({...contractForm, contractType: value})}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Self">Self Assessment</SelectItem>
                    <SelectItem value="Other">Other Assessment</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="evidence">Evidence Requirements</Label>
                <Textarea
                  id="evidence"
                  value={contractForm.evidence}
                  onChange={(e) => setContractForm({...contractForm, evidence: e.target.value})}
                  placeholder="Describe the evidence required for this contract..."
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Grade-specific form */}
          {renderGradeSpecificForm()}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Contract Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Contract Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <Label className="text-sm font-medium">Appraiser</Label>
                <p className="text-sm text-gray-600">{user?.name}</p>
              </div>
              
              {selectedAppraisee && (
                <>
                  <div>
                    <Label className="text-sm font-medium">Appraisee</Label>
                    <p className="text-sm text-gray-600">{selectedAppraisee.name}</p>
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium">Grade Level</Label>
                    <Badge variant="outline">Grade {selectedAppraisee.grade}</Badge>
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium">Contract Type</Label>
                    <Badge>{contractTypeInfo?.contractType}</Badge>
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium">Department</Label>
                    <p className="text-sm text-gray-600">
                      {mockDepartments.find(d => d.id === selectedAppraisee.departmentId)?.title}
                    </p>
                  </div>
                </>
              )}
              
              <div>
                <Label className="text-sm font-medium">Performance Items</Label>
                <p className="text-sm text-gray-600">
                  {activities.length + outputs.length + programmes.length} items
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button 
                variant="outline" 
                className="w-full"
                onClick={handleSaveDraft}
              >
                <Save className="mr-2 h-4 w-4" />
                Save as Draft
              </Button>
              
              <Button 
                className="w-full bg-netone-orange hover:bg-netone-orange/90"
                onClick={handleSubmit}
              >
                <Send className="mr-2 h-4 w-4" />
                Submit Contract
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

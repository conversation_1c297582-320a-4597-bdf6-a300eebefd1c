import { useState } from "react";
import { createFileRoute } from "@tanstack/react-router";
import { useAuth } from "@/lib/auth-context";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Plus, Edit, Trash2, Building, Users } from "lucide-react";
import { mockDepartments, mockSections, mockUsers } from "@/lib/mock-data";
import { Department, Section } from "@/lib/types";
import { toast } from "sonner";

export const Route = createFileRoute("/departments")({
  component: DepartmentsPage,
});

function DepartmentsPage() {
  const { user } = useAuth();
  const [departments, setDepartments] = useState<Department[]>(mockDepartments);
  const [sections, setSections] = useState<Section[]>(mockSections);
  const [isDeptDialogOpen, setIsDeptDialogOpen] = useState(false);
  const [isSectionDialogOpen, setIsSectionDialogOpen] = useState(false);
  const [isEditDeptDialogOpen, setIsEditDeptDialogOpen] = useState(false);
  const [isEditSectionDialogOpen, setIsEditSectionDialogOpen] = useState(false);
  const [editingDepartment, setEditingDepartment] = useState<Department | null>(
    null
  );
  const [editingSection, setEditingSection] = useState<Section | null>(null);
  const [newDepartment, setNewDepartment] = useState({ title: "", headId: "" });
  const [newSection, setNewSection] = useState({
    title: "",
    headId: "",
    parentId: "",
    parentType: "Department" as const,
  });

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-netone-black mb-4">
            Loading...
          </h1>
        </div>
      </div>
    );
  }

  const handleCreateDepartment = () => {
    if (!newDepartment.title || !newDepartment.headId) {
      toast.error("Please fill in all required fields");
      return;
    }

    const department: Department = {
      id: Date.now().toString(),
      title: newDepartment.title,
      headId: newDepartment.headId,
      sections: [],
    };

    setDepartments([...departments, department]);
    setIsDeptDialogOpen(false);
    setNewDepartment({ title: "", headId: "" });
    toast.success("Department created successfully");
  };

  const handleCreateSection = () => {
    if (!newSection.title || !newSection.headId || !newSection.parentId) {
      toast.error("Please fill in all required fields");
      return;
    }

    const section: Section = {
      id: Date.now().toString(),
      title: newSection.title,
      headId: newSection.headId,
      parentId: newSection.parentId,
      parentType: newSection.parentType,
    };

    setSections([...sections, section]);
    setIsSectionDialogOpen(false);
    setNewSection({
      title: "",
      headId: "",
      parentId: "",
      parentType: "Department",
    });
    toast.success("Section created successfully");
  };

  const handleDeleteDepartment = (deptId: string) => {
    setDepartments(departments.filter((d) => d.id !== deptId));
    setSections(sections.filter((s) => s.parentId !== deptId));
    toast.success("Department deleted successfully");
  };

  const handleDeleteSection = (sectionId: string) => {
    setSections(sections.filter((s) => s.id !== sectionId));
    toast.success("Section deleted successfully");
  };

  const getUserName = (userId: string) => {
    const user = mockUsers.find((u) => u.id === userId);
    return user?.name || "Unknown";
  };

  const getDepartmentSections = (deptId: string) => {
    return sections.filter(
      (s) => s.parentId === deptId && s.parentType === "Department"
    );
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-netone-black mb-2">
          Department Management
        </h1>
        <p className="text-gray-600">Manage organizational structure</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Departments */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle className="flex items-center">
                  <Building className="mr-2 h-5 w-5" />
                  Departments
                </CardTitle>
                <CardDescription>
                  Total departments: {departments.length}
                </CardDescription>
              </div>
              <Dialog
                open={isDeptDialogOpen}
                onOpenChange={setIsDeptDialogOpen}>
                <DialogTrigger asChild>
                  <Button className="bg-netone-orange hover:bg-netone-orange/90">
                    <Plus className="mr-2 h-4 w-4" />
                    Add Department
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Create New Department</DialogTitle>
                    <DialogDescription>
                      Add a new department to the organization
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="deptTitle">Department Name</Label>
                      <Input
                        id="deptTitle"
                        value={newDepartment.title}
                        onChange={(e) =>
                          setNewDepartment({
                            ...newDepartment,
                            title: e.target.value,
                          })
                        }
                        placeholder="Information Technology"
                      />
                    </div>

                    <div>
                      <Label htmlFor="deptHead">Department Head</Label>
                      <Select
                        value={newDepartment.headId}
                        onValueChange={(value) =>
                          setNewDepartment({ ...newDepartment, headId: value })
                        }>
                        <SelectTrigger>
                          <SelectValue placeholder="Select department head" />
                        </SelectTrigger>
                        <SelectContent>
                          {mockUsers
                            .filter(
                              (u) =>
                                u.role === "Admin" || u.role === "Appraiser"
                            )
                            .map((user) => (
                              <SelectItem key={user.id} value={user.id}>
                                {user.name}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex justify-end space-x-2">
                      <Button
                        variant="outline"
                        onClick={() => setIsDeptDialogOpen(false)}>
                        Cancel
                      </Button>
                      <Button
                        onClick={handleCreateDepartment}
                        className="bg-netone-orange hover:bg-netone-orange/90">
                        Create Department
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {departments.map((dept) => (
                <div key={dept.id} className="border rounded-lg p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-semibold text-lg">{dept.title}</h3>
                      <p className="text-sm text-gray-600">
                        Head: {getUserName(dept.headId)}
                      </p>
                      <p className="text-sm text-gray-600">
                        Sections: {getDepartmentSections(dept.id).length}
                      </p>
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteDepartment(dept.id)}
                        className="text-red-600 hover:text-red-700">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {/* Show sections under this department */}
                  <div className="mt-3 space-y-2">
                    {getDepartmentSections(dept.id).map((section) => (
                      <div
                        key={section.id}
                        className="bg-gray-50 rounded p-2 text-sm">
                        <div className="flex justify-between items-center">
                          <span>{section.title}</span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteSection(section.id)}
                            className="text-red-600 hover:text-red-700 h-6 w-6 p-0">
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Sections */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle className="flex items-center">
                  <Users className="mr-2 h-5 w-5" />
                  Sections
                </CardTitle>
                <CardDescription>
                  Total sections: {sections.length}
                </CardDescription>
              </div>
              <Dialog
                open={isSectionDialogOpen}
                onOpenChange={setIsSectionDialogOpen}>
                <DialogTrigger asChild>
                  <Button className="bg-netone-orange hover:bg-netone-orange/90">
                    <Plus className="mr-2 h-4 w-4" />
                    Add Section
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Create New Section</DialogTitle>
                    <DialogDescription>
                      Add a new section to a department
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="sectionTitle">Section Name</Label>
                      <Input
                        id="sectionTitle"
                        value={newSection.title}
                        onChange={(e) =>
                          setNewSection({
                            ...newSection,
                            title: e.target.value,
                          })
                        }
                        placeholder="Operations Section"
                      />
                    </div>

                    <div>
                      <Label htmlFor="sectionHead">Section Head</Label>
                      <Select
                        value={newSection.headId}
                        onValueChange={(value) =>
                          setNewSection({ ...newSection, headId: value })
                        }>
                        <SelectTrigger>
                          <SelectValue placeholder="Select section head" />
                        </SelectTrigger>
                        <SelectContent>
                          {mockUsers
                            .filter(
                              (u) =>
                                u.role === "Admin" || u.role === "Appraiser"
                            )
                            .map((user) => (
                              <SelectItem key={user.id} value={user.id}>
                                {user.name}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="parentDept">Parent Department</Label>
                      <Select
                        value={newSection.parentId}
                        onValueChange={(value) =>
                          setNewSection({ ...newSection, parentId: value })
                        }>
                        <SelectTrigger>
                          <SelectValue placeholder="Select parent department" />
                        </SelectTrigger>
                        <SelectContent>
                          {departments.map((dept) => (
                            <SelectItem key={dept.id} value={dept.id}>
                              {dept.title}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex justify-end space-x-2">
                      <Button
                        variant="outline"
                        onClick={() => setIsSectionDialogOpen(false)}>
                        Cancel
                      </Button>
                      <Button
                        onClick={handleCreateSection}
                        className="bg-netone-orange hover:bg-netone-orange/90">
                        Create Section
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Section Name</TableHead>
                    <TableHead>Head</TableHead>
                    <TableHead>Department</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sections.map((section) => (
                    <TableRow key={section.id}>
                      <TableCell className="font-medium">
                        {section.title}
                      </TableCell>
                      <TableCell>{getUserName(section.headId)}</TableCell>
                      <TableCell>
                        {departments.find((d) => d.id === section.parentId)
                          ?.title || "Unknown"}
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteSection(section.id)}
                            className="text-red-600 hover:text-red-700">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

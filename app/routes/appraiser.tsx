import { createFile<PERSON><PERSON><PERSON>, <PERSON> } from '@tanstack/react-router';
import { useAuth } from '@/lib/auth-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  FileText, 
  Clock,
  CheckCircle,
  AlertCircle,
  Plus,
  Edit,
  Eye
} from 'lucide-react';
import { getContractsByAppraiserId, getContractsByAppraiseeId } from '@/lib/mock-data';

export const Route = createFileRoute('/appraiser')({
  component: AppraiserDashboard,
});

function AppraiserDashboard() {
  const { user } = useAuth();

  if (!user || user.role !== 'Appraiser') {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-netone-black mb-4">Access Denied</h1>
          <p className="text-gray-600">You don't have permission to access this page.</p>
        </div>
      </div>
    );
  }

  const createdContracts = getContractsByAppraiserId(user.id);
  const myContracts = getContractsByAppraiseeId(user.id);

  const stats = {
    createdContracts: createdContracts.length,
    pendingReview: createdContracts.filter(c => c.status === 'Filled').length,
    completed: createdContracts.filter(c => c.status === 'Completed').length,
    myContracts: myContracts.length,
    myPending: myContracts.filter(c => c.status === 'Submitted').length,
    myCompleted: myContracts.filter(c => c.status === 'Completed').length
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-netone-black mb-2">
          Appraiser Dashboard
        </h1>
        <p className="text-gray-600">
          Welcome back, {user.name} • Grade {user.grade} • {user.title}
        </p>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Contracts Created</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.createdContracts}</div>
            <p className="text-xs text-muted-foreground">
              For team members
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Review</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pendingReview}</div>
            <p className="text-xs text-muted-foreground">
              Awaiting your review
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">My Contracts</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.myContracts}</div>
            <p className="text-xs text-muted-foreground">
              Assigned to me
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.completed}</div>
            <p className="text-xs text-muted-foreground">
              Successfully completed
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle>Contract Creation</CardTitle>
            <CardDescription>Create and manage contracts for your team</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Link to="/appraiser/contracts/create">
              <Button className="w-full justify-start bg-netone-orange hover:bg-netone-orange/90 text-white">
                <Plus className="mr-2 h-4 w-4" />
                Create New Contract
              </Button>
            </Link>
            <Link to="/appraiser/contracts/created">
              <Button className="w-full justify-start" variant="outline">
                <Eye className="mr-2 h-4 w-4" />
                View Contracts I Created
              </Button>
            </Link>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>My Performance</CardTitle>
            <CardDescription>Manage your own performance contracts</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Link to="/appraiser/contracts/my">
              <Button className="w-full justify-start" variant="outline">
                <FileText className="mr-2 h-4 w-4" />
                My Contracts ({stats.myContracts})
              </Button>
            </Link>
            {stats.myPending > 0 && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <div className="flex items-center">
                  <AlertCircle className="h-4 w-4 text-blue-500 mr-2" />
                  <span className="text-sm font-medium text-blue-800">Action Required</span>
                </div>
                <p className="text-sm text-blue-700 mt-1">
                  You have {stats.myPending} contract(s) waiting for your input.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
          <CardDescription>Latest updates on your contracts</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {stats.pendingReview > 0 && (
              <div className="flex items-center justify-between p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-center space-x-3">
                  <Clock className="h-4 w-4 text-yellow-500" />
                  <div>
                    <p className="text-sm font-medium">Contracts Awaiting Review</p>
                    <p className="text-xs text-gray-600">{stats.pendingReview} contract(s) submitted by team members</p>
                  </div>
                </div>
                <Link to="/appraiser/contracts/created">
                  <Button size="sm" variant="outline">
                    Review
                  </Button>
                </Link>
              </div>
            )}
            
            {stats.myPending > 0 && (
              <div className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center space-x-3">
                  <AlertCircle className="h-4 w-4 text-blue-500" />
                  <div>
                    <p className="text-sm font-medium">Your Contracts Need Attention</p>
                    <p className="text-xs text-gray-600">{stats.myPending} contract(s) waiting for your input</p>
                  </div>
                </div>
                <Link to="/appraiser/contracts/my">
                  <Button size="sm" variant="outline">
                    Fill Now
                  </Button>
                </Link>
              </div>
            )}

            {stats.pendingReview === 0 && stats.myPending === 0 && (
              <div className="text-center py-8 text-gray-500">
                <CheckCircle className="mx-auto h-12 w-12 text-green-500 mb-4" />
                <h3 className="text-sm font-semibold text-gray-900">All caught up!</h3>
                <p className="text-sm">No pending actions at the moment.</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// app/routes/__root.tsx
import type { ReactNode } from "react";
import {
  Outlet,
  createRoot<PERSON>oute,
  HeadContent,
  Scripts,
  useLocation,
} from "@tanstack/react-router";

import appCss from "@/styles/app.css?url";
import globalCss from "@/styles/global.css?url";
import { Toaster } from "@/components/ui/sonner";
import { AuthProvider, useAuth } from "@/lib/auth-context";
import { Navigation } from "@/components/navigation";

export const Route = createRootRoute({
  head: () => ({
    meta: [
      {
        charSet: "utf-8",
      },
      {
        name: "viewport",
        content: "width=device-width, initial-scale=1",
      },
      {
        title: "IRBM - Individual Results-Based Management",
      },
    ],
    links: [
      {
        rel: "stylesheet",
        href: appCss,
      },
      {
        rel: "stylesheet",
        href: globalCss,
      },
    ],
  }),
  component: RootComponent,
});

function AppContent() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-netone-orange"></div>
      </div>
    );
  }

  // If not authenticated and not on login page, redirect to login
  if (!isAuthenticated && location.pathname !== "/login") {
    window.location.href = "/login";
    return null;
  }

  // If authenticated and on login page, redirect to dashboard
  if (isAuthenticated && location.pathname === "/login") {
    window.location.href = "/";
    return null;
  }

  return (
    <div className="min-h-screen bg-background">
      {isAuthenticated && <Navigation />}
      <main className={isAuthenticated ? "flex-1" : ""}>
        <Outlet />
      </main>
    </div>
  );
}

function RootComponent() {
  return (
    <RootDocument>
      <AuthProvider>
        <AppContent />
      </AuthProvider>
    </RootDocument>
  );
}

function RootDocument({ children }: Readonly<{ children: ReactNode }>) {
  return (
    <html>
      <head>
        <HeadContent />
      </head>
      <body>
        {children}
        <Toaster />
        <Scripts />
      </body>
    </html>
  );
}

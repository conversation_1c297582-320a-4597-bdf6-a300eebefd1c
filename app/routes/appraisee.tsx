import { createFile<PERSON>out<PERSON>, <PERSON> } from '@tanstack/react-router';
import { useAuth } from '@/lib/auth-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  FileText, 
  Clock,
  CheckCircle,
  AlertCircle,
  Edit,
  Eye
} from 'lucide-react';
import { getContractsByAppraiseeId, getUserById } from '@/lib/mock-data';

export const Route = createFileRoute('/appraisee')({
  component: AppraiseesDashboard,
});

function AppraiseesDashboard() {
  const { user } = useAuth();

  if (!user || user.role !== 'Appraisee') {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-netone-black mb-4">Access Denied</h1>
          <p className="text-gray-600">You don't have permission to access this page.</p>
        </div>
      </div>
    );
  }

  const assignedContracts = getContractsByAppraiseeId(user.id);

  const stats = {
    assignedContracts: assignedContracts.length,
    pending: assignedContracts.filter(c => c.status === 'Submitted').length,
    inProgress: assignedContracts.filter(c => c.status === 'Filled').length,
    completed: assignedContracts.filter(c => c.status === 'Completed').length,
    draft: assignedContracts.filter(c => c.status === 'Draft').length
  };

  const getProgressValue = (status: string) => {
    switch (status) {
      case 'Draft': return 0;
      case 'Submitted': return 25;
      case 'Filled': return 75;
      case 'Completed': return 100;
      default: return 0;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Draft': return 'text-gray-500';
      case 'Submitted': return 'text-blue-500';
      case 'Filled': return 'text-yellow-500';
      case 'Completed': return 'text-green-500';
      default: return 'text-gray-500';
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-netone-black mb-2">
          My Performance Dashboard
        </h1>
        <p className="text-gray-600">
          Welcome back, {user.name} • Grade {user.grade} • {user.title}
        </p>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Contracts</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.assignedContracts}</div>
            <p className="text-xs text-muted-foreground">
              Assigned to you
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Action</CardTitle>
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pending}</div>
            <p className="text-xs text-muted-foreground">
              Need your input
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Under Review</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.inProgress}</div>
            <p className="text-xs text-muted-foreground">
              Being reviewed
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.completed}</div>
            <p className="text-xs text-muted-foreground">
              Successfully finished
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle>My Contracts</CardTitle>
            <CardDescription>View and manage your performance contracts</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Link to="/appraisee/contracts">
              <Button className="w-full justify-start bg-netone-orange hover:bg-netone-orange/90 text-white">
                <FileText className="mr-2 h-4 w-4" />
                View All My Contracts
              </Button>
            </Link>
            {stats.pending > 0 && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <div className="flex items-center">
                  <AlertCircle className="h-4 w-4 text-blue-500 mr-2" />
                  <span className="text-sm font-medium text-blue-800">Action Required</span>
                </div>
                <p className="text-sm text-blue-700 mt-1">
                  You have {stats.pending} contract(s) waiting for your input.
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Performance Overview</CardTitle>
            <CardDescription>Your current performance status</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span className="text-gray-600">Overall Progress</span>
                  <span className="font-medium">
                    {stats.assignedContracts > 0 
                      ? Math.round(((stats.completed + stats.inProgress * 0.75) / stats.assignedContracts) * 100)
                      : 0}%
                  </span>
                </div>
                <Progress 
                  value={stats.assignedContracts > 0 
                    ? ((stats.completed + stats.inProgress * 0.75) / stats.assignedContracts) * 100
                    : 0} 
                  className="h-2" 
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="text-center">
                  <div className="text-lg font-bold text-green-600">{stats.completed}</div>
                  <div className="text-gray-600">Completed</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-blue-600">{stats.pending}</div>
                  <div className="text-gray-600">Pending</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Contracts */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Contracts</CardTitle>
          <CardDescription>Your latest performance contracts</CardDescription>
        </CardHeader>
        <CardContent>
          {assignedContracts.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No contracts assigned</h3>
              <p className="text-gray-600">
                You don't have any performance contracts assigned to you yet.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {assignedContracts.slice(0, 3).map((contract) => {
                const appraiser = getUserById(contract.appraiserId);
                return (
                  <div key={contract.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <h4 className="font-semibold">{contract.title}</h4>
                      <p className="text-sm text-gray-600">
                        Assigned by {appraiser?.name} • {contract.contractType}
                      </p>
                      <div className="mt-2">
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-gray-600">Progress</span>
                          <span className={`font-medium ${getStatusColor(contract.status)}`}>
                            {contract.status}
                          </span>
                        </div>
                        <Progress value={getProgressValue(contract.status)} className="h-1" />
                      </div>
                    </div>
                    <div className="ml-4">
                      <Link to={`/appraisee/contracts/${contract.id}`}>
                        <Button size="sm" variant="outline">
                          {contract.status === 'Submitted' ? (
                            <>
                              <Edit className="mr-2 h-3 w-3" />
                              Fill
                            </>
                          ) : (
                            <>
                              <Eye className="mr-2 h-3 w-3" />
                              View
                            </>
                          )}
                        </Button>
                      </Link>
                    </div>
                  </div>
                );
              })}
              
              {assignedContracts.length > 3 && (
                <div className="text-center pt-4">
                  <Link to="/appraisee/contracts">
                    <Button variant="outline">
                      View All Contracts ({assignedContracts.length})
                    </Button>
                  </Link>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

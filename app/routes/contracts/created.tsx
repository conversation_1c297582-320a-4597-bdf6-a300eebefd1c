import { useState } from "react";
import { createFileRoute } from "@tanstack/react-router";
import { useAuth } from "@/lib/auth-context";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Eye,
  Edit,
  Search,
  Filter,
  FileText,
  Clock,
  CheckCircle,
  AlertCircle,
} from "lucide-react";
import {
  getContractsByAppraiserId,
  getUserById,
  getDepartmentById,
} from "@/lib/mock-data";
import { getContractTypeFromGrade } from "@/lib/types";

export const Route = createFileRoute("/contracts/created")({
  component: CreatedContractsPage,
});

function CreatedContractsPage() {
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-netone-black mb-4">
            Loading...
          </h1>
        </div>
      </div>
    );
  }

  const createdContracts = getContractsByAppraiserId(user.id);

  const filteredContracts = createdContracts.filter((contract) => {
    const appraisee = getUserById(contract.appraiseeId);
    const department = getDepartmentById(contract.departmentId);

    const matchesSearch =
      contract.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      appraisee?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      department?.title.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus =
      statusFilter === "all" || contract.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "Draft":
        return "bg-gray-100 text-gray-800";
      case "Submitted":
        return "bg-blue-100 text-blue-800";
      case "Filled":
        return "bg-yellow-100 text-yellow-800";
      case "Completed":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Draft":
        return <Clock className="h-4 w-4 text-gray-500" />;
      case "Submitted":
        return <AlertCircle className="h-4 w-4 text-blue-500" />;
      case "Filled":
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case "Completed":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getActionButton = (contract: any) => {
    switch (contract.status) {
      case "Draft":
        return (
          <Button variant="outline" size="sm">
            <Edit className="h-4 w-4" />
          </Button>
        );
      case "Filled":
        return (
          <Button
            className="bg-netone-orange hover:bg-netone-orange/90"
            size="sm">
            Review
          </Button>
        );
      default:
        return (
          <Button variant="outline" size="sm">
            <Eye className="h-4 w-4" />
          </Button>
        );
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-netone-black mb-2">
          Contracts I Created
        </h1>
        <p className="text-gray-600">
          Manage and track contracts you've created for your team
        </p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Total Created
                </p>
                <p className="text-2xl font-bold">{createdContracts.length}</p>
              </div>
              <FileText className="h-8 w-8 text-gray-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Awaiting Response
                </p>
                <p className="text-2xl font-bold">
                  {
                    createdContracts.filter((c) => c.status === "Submitted")
                      .length
                  }
                </p>
              </div>
              <AlertCircle className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Pending Review
                </p>
                <p className="text-2xl font-bold">
                  {createdContracts.filter((c) => c.status === "Filled").length}
                </p>
              </div>
              <Clock className="h-8 w-8 text-yellow-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Completed</p>
                <p className="text-2xl font-bold">
                  {
                    createdContracts.filter((c) => c.status === "Completed")
                      .length
                  }
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Contracts Table */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="flex items-center">
                <FileText className="mr-2 h-5 w-5" />
                Created Contracts
              </CardTitle>
              <CardDescription>
                Total: {createdContracts.length} | Filtered:{" "}
                {filteredContracts.length}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="mb-6 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search contracts..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="Draft">Draft</SelectItem>
                <SelectItem value="Submitted">Submitted</SelectItem>
                <SelectItem value="Filled">Filled</SelectItem>
                <SelectItem value="Completed">Completed</SelectItem>
              </SelectContent>
            </Select>

            <Button variant="outline" className="flex items-center">
              <Filter className="mr-2 h-4 w-4" />
              More Filters
            </Button>
          </div>

          {filteredContracts.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-semibold text-gray-900">
                No contracts found
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                {createdContracts.length === 0
                  ? "You haven't created any contracts yet."
                  : "Try adjusting your search or filter criteria."}
              </p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Contract Title</TableHead>
                    <TableHead>Appraisee</TableHead>
                    <TableHead>Department</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Grade Level</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredContracts.map((contract) => {
                    const appraisee = getUserById(contract.appraiseeId);
                    const department = getDepartmentById(contract.departmentId);
                    const contractType = appraisee
                      ? getContractTypeFromGrade(appraisee.grade)
                      : "Unknown";

                    return (
                      <TableRow key={contract.id}>
                        <TableCell className="font-medium">
                          <div className="flex items-center">
                            {getStatusIcon(contract.status)}
                            <div className="ml-2">
                              <div className="font-semibold">
                                {contract.title}
                              </div>
                              <div className="text-sm text-gray-500">
                                {contractType}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">
                              {appraisee?.name || "Unknown"}
                            </div>
                            <div className="text-sm text-gray-500">
                              {appraisee?.title}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{department?.title || "Unknown"}</TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              contract.contractType === "Self"
                                ? "secondary"
                                : "default"
                            }>
                            {contract.contractType}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            Grade {appraisee?.grade}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge
                            className={getStatusBadgeColor(contract.status)}>
                            {contract.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button variant="outline" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                            {getActionButton(contract)}
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

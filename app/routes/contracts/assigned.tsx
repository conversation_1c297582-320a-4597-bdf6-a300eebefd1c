import { useState } from 'react';
import { createFileRoute } from '@tanstack/react-router';
import { useAuth } from '@/lib/auth-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Eye, Edit, Clock, CheckCircle, AlertCircle, FileText } from 'lucide-react';
import { getContractsByAppraiseeId, getUserById, getDepartmentById } from '@/lib/mock-data';
import { getContractTypeFromGrade } from '@/lib/types';

export const Route = createFileRoute('/contracts/assigned')({
  component: AssignedContractsPage,
});

function AssignedContractsPage() {
  const { user } = useAuth();

  // Check if user is appraisee
  if (user?.role !== 'Appraisee') {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-netone-black mb-4">Access Denied</h1>
          <p className="text-gray-600">You don't have permission to access this page.</p>
        </div>
      </div>
    );
  }

  const assignedContracts = getContractsByAppraiseeId(user.id);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Draft':
        return <Clock className="h-4 w-4 text-gray-500" />;
      case 'Submitted':
        return <AlertCircle className="h-4 w-4 text-blue-500" />;
      case 'Filled':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'Completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'Draft': return 'bg-gray-100 text-gray-800';
      case 'Submitted': return 'bg-blue-100 text-blue-800';
      case 'Filled': return 'bg-yellow-100 text-yellow-800';
      case 'Completed': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getProgressValue = (status: string) => {
    switch (status) {
      case 'Draft': return 0;
      case 'Submitted': return 25;
      case 'Filled': return 75;
      case 'Completed': return 100;
      default: return 0;
    }
  };

  const getActionButton = (contract: any) => {
    switch (contract.status) {
      case 'Submitted':
        return (
          <Button className="bg-netone-orange hover:bg-netone-orange/90">
            <Edit className="mr-2 h-4 w-4" />
            Fill Contract
          </Button>
        );
      case 'Filled':
        return (
          <Button variant="outline">
            <Eye className="mr-2 h-4 w-4" />
            View Submission
          </Button>
        );
      case 'Completed':
        return (
          <Button variant="outline">
            <Eye className="mr-2 h-4 w-4" />
            View Results
          </Button>
        );
      default:
        return (
          <Button variant="outline" disabled>
            <Clock className="mr-2 h-4 w-4" />
            Waiting
          </Button>
        );
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-netone-black mb-2">My Assigned Contracts</h1>
        <p className="text-gray-600">View and complete your performance contracts</p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Assigned</p>
                <p className="text-2xl font-bold">{assignedContracts.length}</p>
              </div>
              <FileText className="h-8 w-8 text-gray-400" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending Action</p>
                <p className="text-2xl font-bold">
                  {assignedContracts.filter(c => c.status === 'Submitted').length}
                </p>
              </div>
              <AlertCircle className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">In Review</p>
                <p className="text-2xl font-bold">
                  {assignedContracts.filter(c => c.status === 'Filled').length}
                </p>
              </div>
              <Clock className="h-8 w-8 text-yellow-400" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Completed</p>
                <p className="text-2xl font-bold">
                  {assignedContracts.filter(c => c.status === 'Completed').length}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Contracts List */}
      {assignedContracts.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No contracts assigned</h3>
            <p className="text-gray-600">
              You don't have any performance contracts assigned to you yet.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          {assignedContracts.map((contract) => {
            const appraiser = getUserById(contract.appraiserId);
            const department = getDepartmentById(contract.departmentId);
            const contractType = getContractTypeFromGrade(user.grade);
            
            return (
              <Card key={contract.id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <CardTitle className="flex items-center gap-2">
                        {getStatusIcon(contract.status)}
                        {contract.title}
                      </CardTitle>
                      <CardDescription className="mt-1">
                        {contractType} • Assigned by {appraiser?.name} • {department?.title}
                      </CardDescription>
                    </div>
                    <Badge className={getStatusBadgeColor(contract.status)}>
                      {contract.status}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Progress Bar */}
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span className="text-gray-600">Progress</span>
                        <span className="font-medium">{getProgressValue(contract.status)}%</span>
                      </div>
                      <Progress value={getProgressValue(contract.status)} className="h-2" />
                    </div>
                    
                    {/* Contract Details */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-gray-600">Appraiser:</span>
                        <p>{appraiser?.name}</p>
                        <p className="text-gray-500">{appraiser?.title}</p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-600">Contract Type:</span>
                        <p>{contract.contractType}</p>
                        <p className="text-gray-500">{contractType}</p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-600">Department:</span>
                        <p>{department?.title}</p>
                      </div>
                    </div>
                    
                    {/* Evidence Requirements */}
                    {contract.evidence && (
                      <div>
                        <span className="font-medium text-gray-600 text-sm">Evidence Requirements:</span>
                        <p className="text-sm text-gray-700 mt-1">{contract.evidence}</p>
                      </div>
                    )}
                    
                    {/* Status-specific Information */}
                    {contract.status === 'Submitted' && (
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                        <div className="flex items-center">
                          <AlertCircle className="h-4 w-4 text-blue-500 mr-2" />
                          <span className="text-sm font-medium text-blue-800">Action Required</span>
                        </div>
                        <p className="text-sm text-blue-700 mt-1">
                          This contract is ready for you to fill in your performance data.
                        </p>
                      </div>
                    )}
                    
                    {contract.status === 'Filled' && (
                      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                        <div className="flex items-center">
                          <Clock className="h-4 w-4 text-yellow-500 mr-2" />
                          <span className="text-sm font-medium text-yellow-800">Under Review</span>
                        </div>
                        <p className="text-sm text-yellow-700 mt-1">
                          Your submission is being reviewed by your appraiser.
                        </p>
                      </div>
                    )}
                    
                    {contract.status === 'Completed' && (
                      <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                        <div className="flex items-center">
                          <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                          <span className="text-sm font-medium text-green-800">Completed</span>
                        </div>
                        <p className="text-sm text-green-700 mt-1">
                          This contract has been completed and finalized.
                        </p>
                      </div>
                    )}
                    
                    {/* Action Buttons */}
                    <div className="flex justify-end space-x-2 pt-2">
                      <Button variant="outline">
                        <Eye className="mr-2 h-4 w-4" />
                        View Details
                      </Button>
                      {getActionButton(contract)}
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}
    </div>
  );
}

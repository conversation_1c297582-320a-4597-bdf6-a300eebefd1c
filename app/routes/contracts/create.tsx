import { useState } from "react";
import { createFileRoute } from "@tanstack/react-router";
import { useAuth } from "@/lib/auth-context";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Plus, Trash2, Save, Send } from "lucide-react";
import { mockUsers, mockDepartments } from "@/lib/mock-data";
import { getGradeRange, getContractTypeFromGrade } from "@/lib/types";
import { toast } from "sonner";

export const Route = createFileRoute("/contracts/create")({
  component: CreateContractPage,
});

interface ContractForm {
  title: string;
  appraiseeId: string;
  departmentId: string;
  contractType: "Self" | "Other";
  evidence: string;
}

interface ActivityItem {
  id: string;
  title: string;
  unit: string;
  weight: string;
  measure: string;
  target: string;
  allowableVariance: string;
  actions: string;
}

function CreateContractPage() {
  const { user } = useAuth();
  const [contractForm, setContractForm] = useState<ContractForm>({
    title: "",
    appraiseeId: "",
    departmentId: "",
    contractType: "Other",
    evidence: "",
  });
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [selectedAppraisee, setSelectedAppraisee] = useState<any>(null);

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-netone-black mb-4">
            Loading...
          </h1>
        </div>
      </div>
    );
  }

  const appraisees = mockUsers.filter((u) => u.role === "Appraisee");

  const handleAppraiseeChange = (appraiseeId: string) => {
    const appraisee = mockUsers.find((u) => u.id === appraiseeId);
    setSelectedAppraisee(appraisee);
    setContractForm({
      ...contractForm,
      appraiseeId,
      departmentId: appraisee?.departmentId || "",
    });

    // Clear existing activities when changing appraisee
    setActivities([]);
  };

  const addActivity = () => {
    const newActivity: ActivityItem = {
      id: Date.now().toString(),
      title: "",
      unit: "",
      weight: "",
      measure: "",
      target: "",
      allowableVariance: "",
      actions: "",
    };
    setActivities([...activities, newActivity]);
  };

  const updateActivity = (
    id: string,
    field: keyof ActivityItem,
    value: string
  ) => {
    setActivities(
      activities.map((activity) =>
        activity.id === id ? { ...activity, [field]: value } : activity
      )
    );
  };

  const removeActivity = (id: string) => {
    setActivities(activities.filter((activity) => activity.id !== id));
  };

  const handleSaveDraft = () => {
    if (!contractForm.title || !contractForm.appraiseeId) {
      toast.error("Please fill in the required fields");
      return;
    }

    // In a real app, this would save to backend
    toast.success("Contract saved as draft");
  };

  const handleSubmit = () => {
    if (
      !contractForm.title ||
      !contractForm.appraiseeId ||
      activities.length === 0
    ) {
      toast.error(
        "Please fill in all required fields and add at least one activity"
      );
      return;
    }

    // In a real app, this would submit to backend
    toast.success("Contract submitted successfully");
  };

  const getContractTypeInfo = () => {
    if (!selectedAppraisee) return null;

    const gradeRange = getGradeRange(selectedAppraisee.grade);
    const contractType = getContractTypeFromGrade(selectedAppraisee.grade);

    return { gradeRange, contractType };
  };

  const contractTypeInfo = getContractTypeInfo();

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-netone-black mb-2">
          Create Performance Contract
        </h1>
        <p className="text-gray-600">
          Create a new performance contract for an employee
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Contract Details */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Contract Information</CardTitle>
              <CardDescription>
                Basic contract details and assignment
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="title">Contract Title</Label>
                <Input
                  id="title"
                  value={contractForm.title}
                  onChange={(e) =>
                    setContractForm({ ...contractForm, title: e.target.value })
                  }
                  placeholder="Performance Contract 2024"
                />
              </div>

              <div>
                <Label htmlFor="appraisee">Appraisee</Label>
                <Select
                  value={contractForm.appraiseeId}
                  onValueChange={handleAppraiseeChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select employee to appraise" />
                  </SelectTrigger>
                  <SelectContent>
                    {appraisees.map((employee) => (
                      <SelectItem key={employee.id} value={employee.id}>
                        {employee.name} - Grade {employee.grade} (
                        {employee.title})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="contractType">Contract Type</Label>
                <Select
                  value={contractForm.contractType}
                  onValueChange={(value: "Self" | "Other") =>
                    setContractForm({ ...contractForm, contractType: value })
                  }>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Self">Self Assessment</SelectItem>
                    <SelectItem value="Other">Other Assessment</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="evidence">Evidence Requirements</Label>
                <Textarea
                  id="evidence"
                  value={contractForm.evidence}
                  onChange={(e) =>
                    setContractForm({
                      ...contractForm,
                      evidence: e.target.value,
                    })
                  }
                  placeholder="Describe the evidence required for this contract..."
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Activities/Outputs Section */}
          {selectedAppraisee && (
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>
                      {contractTypeInfo?.contractType || "Performance Items"}
                    </CardTitle>
                    <CardDescription>
                      Add performance items for Grade {selectedAppraisee.grade}{" "}
                      ({contractTypeInfo?.gradeRange})
                    </CardDescription>
                  </div>
                  <Button
                    onClick={addActivity}
                    className="bg-netone-orange hover:bg-netone-orange/90">
                    <Plus className="mr-2 h-4 w-4" />
                    Add Item
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {activities.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <p>No performance items added yet.</p>
                    <p className="text-sm">Click "Add Item" to get started.</p>
                  </div>
                ) : (
                  <div className="space-y-6">
                    {activities.map((activity, index) => (
                      <div key={activity.id} className="border rounded-lg p-4">
                        <div className="flex justify-between items-center mb-4">
                          <h4 className="font-semibold">Item {index + 1}</h4>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => removeActivity(activity.id)}
                            className="text-red-600 hover:text-red-700">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <Label>Title</Label>
                            <Input
                              value={activity.title}
                              onChange={(e) =>
                                updateActivity(
                                  activity.id,
                                  "title",
                                  e.target.value
                                )
                              }
                              placeholder="Activity/Output title"
                            />
                          </div>

                          <div>
                            <Label>Unit of Measure</Label>
                            <Input
                              value={activity.unit}
                              onChange={(e) =>
                                updateActivity(
                                  activity.id,
                                  "unit",
                                  e.target.value
                                )
                              }
                              placeholder="e.g., Percentage, Number, etc."
                            />
                          </div>

                          <div>
                            <Label>Weight (%)</Label>
                            <Input
                              value={activity.weight}
                              onChange={(e) =>
                                updateActivity(
                                  activity.id,
                                  "weight",
                                  e.target.value
                                )
                              }
                              placeholder="e.g., 25"
                              type="number"
                            />
                          </div>

                          <div>
                            <Label>Target</Label>
                            <Input
                              value={activity.target}
                              onChange={(e) =>
                                updateActivity(
                                  activity.id,
                                  "target",
                                  e.target.value
                                )
                              }
                              placeholder="Target value"
                            />
                          </div>

                          <div>
                            <Label>Allowable Variance (%)</Label>
                            <Input
                              value={activity.allowableVariance}
                              onChange={(e) =>
                                updateActivity(
                                  activity.id,
                                  "allowableVariance",
                                  e.target.value
                                )
                              }
                              placeholder="e.g., 5"
                              type="number"
                            />
                          </div>

                          <div>
                            <Label>Measure</Label>
                            <Input
                              value={activity.measure}
                              onChange={(e) =>
                                updateActivity(
                                  activity.id,
                                  "measure",
                                  e.target.value
                                )
                              }
                              placeholder="How this will be measured"
                            />
                          </div>
                        </div>

                        <div className="mt-4">
                          <Label>Actions Required</Label>
                          <Textarea
                            value={activity.actions}
                            onChange={(e) =>
                              updateActivity(
                                activity.id,
                                "actions",
                                e.target.value
                              )
                            }
                            placeholder="Describe the actions required to achieve this target..."
                            rows={2}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Contract Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Contract Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <Label className="text-sm font-medium">Appraiser</Label>
                <p className="text-sm text-gray-600">{user?.name}</p>
              </div>

              {selectedAppraisee && (
                <>
                  <div>
                    <Label className="text-sm font-medium">Appraisee</Label>
                    <p className="text-sm text-gray-600">
                      {selectedAppraisee.name}
                    </p>
                  </div>

                  <div>
                    <Label className="text-sm font-medium">Grade Level</Label>
                    <Badge variant="outline">
                      Grade {selectedAppraisee.grade}
                    </Badge>
                  </div>

                  <div>
                    <Label className="text-sm font-medium">Contract Type</Label>
                    <Badge>{contractTypeInfo?.contractType}</Badge>
                  </div>

                  <div>
                    <Label className="text-sm font-medium">Department</Label>
                    <p className="text-sm text-gray-600">
                      {
                        mockDepartments.find(
                          (d) => d.id === selectedAppraisee.departmentId
                        )?.title
                      }
                    </p>
                  </div>
                </>
              )}

              <div>
                <Label className="text-sm font-medium">Performance Items</Label>
                <p className="text-sm text-gray-600">
                  {activities.length} items
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button
                variant="outline"
                className="w-full"
                onClick={handleSaveDraft}>
                <Save className="mr-2 h-4 w-4" />
                Save as Draft
              </Button>

              <Button
                className="w-full bg-netone-orange hover:bg-netone-orange/90"
                onClick={handleSubmit}>
                <Send className="mr-2 h-4 w-4" />
                Submit Contract
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

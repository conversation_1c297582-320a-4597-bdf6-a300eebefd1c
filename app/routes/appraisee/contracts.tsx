import { createFile<PERSON>out<PERSON>, <PERSON> } from '@tanstack/react-router';
import { useAuth } from '@/lib/auth-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Eye, Edit, Clock, CheckCircle, AlertCircle, FileText, ArrowLeft } from 'lucide-react';
import { getContractsByAppraiseeId, getUserById, getDepartmentById } from '@/lib/mock-data';
import { getContractTypeFromGrade } from '@/lib/types';

export const Route = createFileRoute('/appraisee/contracts')({
  component: AppraiseeContractsPage,
});

function AppraiseeContractsPage() {
  const { user } = useAuth();

  if (!user || user.role !== 'Appraisee') {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-netone-black mb-4">Access Denied</h1>
          <p className="text-gray-600">You don't have permission to access this page.</p>
        </div>
      </div>
    );
  }

  const assignedContracts = getContractsByAppraiseeId(user.id);

  const getProgressValue = (status: string) => {
    switch (status) {
      case 'Draft': return 0;
      case 'Submitted': return 25;
      case 'Filled': return 75;
      case 'Completed': return 100;
      default: return 0;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Draft': return 'text-gray-500';
      case 'Submitted': return 'text-blue-500';
      case 'Filled': return 'text-yellow-500';
      case 'Completed': return 'text-green-500';
      default: return 'text-gray-500';
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'Draft': return 'bg-gray-100 text-gray-800';
      case 'Submitted': return 'bg-blue-100 text-blue-800';
      case 'Filled': return 'bg-yellow-100 text-yellow-800';
      case 'Completed': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Draft':
        return <Clock className="h-4 w-4 text-gray-500" />;
      case 'Submitted':
        return <AlertCircle className="h-4 w-4 text-blue-500" />;
      case 'Filled':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'Completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const stats = {
    total: assignedContracts.length,
    pending: assignedContracts.filter(c => c.status === 'Submitted').length,
    inProgress: assignedContracts.filter(c => c.status === 'Filled').length,
    completed: assignedContracts.filter(c => c.status === 'Completed').length,
    draft: assignedContracts.filter(c => c.status === 'Draft').length
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center space-x-4 mb-4">
          <Link to="/appraisee">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Dashboard
            </Button>
          </Link>
        </div>
        <h1 className="text-3xl font-bold text-netone-black mb-2">My Performance Contracts</h1>
        <p className="text-gray-600">View and complete your assigned performance contracts</p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Contracts</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
              <FileText className="h-8 w-8 text-gray-400" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending Action</p>
                <p className="text-2xl font-bold">{stats.pending}</p>
              </div>
              <AlertCircle className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Under Review</p>
                <p className="text-2xl font-bold">{stats.inProgress}</p>
              </div>
              <Clock className="h-8 w-8 text-yellow-400" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Completed</p>
                <p className="text-2xl font-bold">{stats.completed}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Overview */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Performance Overview</CardTitle>
          <CardDescription>Your overall performance contract progress</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-2">
                <span className="text-gray-600">Overall Progress</span>
                <span className="font-medium">
                  {stats.total > 0 
                    ? Math.round(((stats.completed + stats.inProgress * 0.75) / stats.total) * 100)
                    : 0}%
                </span>
              </div>
              <Progress 
                value={stats.total > 0 
                  ? ((stats.completed + stats.inProgress * 0.75) / stats.total) * 100
                  : 0} 
                className="h-3" 
              />
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div className="text-center">
                <div className="text-lg font-bold text-green-600">{stats.completed}</div>
                <div className="text-gray-600">Completed</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-yellow-600">{stats.inProgress}</div>
                <div className="text-gray-600">Under Review</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-blue-600">{stats.pending}</div>
                <div className="text-gray-600">Pending</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-gray-600">{stats.total}</div>
                <div className="text-gray-600">Total</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Contracts List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="mr-2 h-5 w-5" />
            My Contracts
          </CardTitle>
          <CardDescription>
            Performance contracts assigned to you
          </CardDescription>
        </CardHeader>
        <CardContent>
          {assignedContracts.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No contracts assigned</h3>
              <p className="text-gray-600">
                You don't have any performance contracts assigned to you yet.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {assignedContracts.map((contract) => {
                const appraiser = getUserById(contract.appraiserId);
                const department = getDepartmentById(contract.departmentId);
                const contractType = getContractTypeFromGrade(user.grade);
                
                return (
                  <div key={contract.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center mb-2">
                          {getStatusIcon(contract.status)}
                          <h4 className="font-semibold ml-2">{contract.title}</h4>
                          <Badge className={`ml-2 ${getStatusBadgeColor(contract.status)}`}>
                            {contract.status}
                          </Badge>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 mb-3">
                          <div>
                            <span className="font-medium">Appraiser:</span> {appraiser?.name}
                          </div>
                          <div>
                            <span className="font-medium">Department:</span> {department?.title}
                          </div>
                          <div>
                            <span className="font-medium">Type:</span> {contractType}
                          </div>
                        </div>
                        
                        <div className="mb-3">
                          <div className="flex justify-between text-sm mb-1">
                            <span className="text-gray-600">Progress</span>
                            <span className={`font-medium ${getStatusColor(contract.status)}`}>
                              {getProgressValue(contract.status)}%
                            </span>
                          </div>
                          <Progress value={getProgressValue(contract.status)} className="h-2" />
                        </div>
                        
                        {contract.status === 'Submitted' && (
                          <div className="bg-blue-50 border border-blue-200 rounded p-2 text-sm text-blue-800">
                            <AlertCircle className="inline h-4 w-4 mr-1" />
                            Action required: Please fill in your performance data
                          </div>
                        )}
                        
                        {contract.status === 'Filled' && (
                          <div className="bg-yellow-50 border border-yellow-200 rounded p-2 text-sm text-yellow-800">
                            <Clock className="inline h-4 w-4 mr-1" />
                            Under review by your appraiser
                          </div>
                        )}
                        
                        {contract.status === 'Completed' && (
                          <div className="bg-green-50 border border-green-200 rounded p-2 text-sm text-green-800">
                            <CheckCircle className="inline h-4 w-4 mr-1" />
                            Contract completed successfully
                          </div>
                        )}
                        
                        {contract.status === 'Draft' && (
                          <div className="bg-gray-50 border border-gray-200 rounded p-2 text-sm text-gray-600">
                            <Clock className="inline h-4 w-4 mr-1" />
                            Contract is being prepared by your appraiser
                          </div>
                        )}
                      </div>
                      
                      <div className="ml-4 flex space-x-2">
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                        {contract.status === 'Submitted' && (
                          <Button className="bg-netone-orange hover:bg-netone-orange/90" size="sm">
                            <Edit className="mr-2 h-4 w-4" />
                            Fill Contract
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

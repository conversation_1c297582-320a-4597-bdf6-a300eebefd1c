import { useState } from 'react';
import { createFileRoute } from '@tanstack/react-router';
import { useAuth } from '@/lib/auth-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Plus, Edit, Trash2, Search, ArrowLeft } from 'lucide-react';
import { mockUsers, mockDepartments, mockSections } from '@/lib/mock-data';
import { User, CreateUserForm } from '@/lib/types';
import { toast } from 'sonner';
import { Link } from '@tanstack/react-router';

export const Route = createFileRoute('/admin/users')({
  component: AdminUsersPage,
});

function AdminUsersPage() {
  const { user } = useAuth();
  const [users, setUsers] = useState<User[]>(mockUsers);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [departmentFilter, setDepartmentFilter] = useState<string>('all');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [newUser, setNewUser] = useState<CreateUserForm>({
    ecNumber: '',
    name: '',
    email: '',
    role: 'Appraisee',
    departmentId: '',
    sectionId: '',
    grade: '',
    title: '',
    reportsTo: '',
    password: ''
  });

  if (!user || user.role !== 'Admin') {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-netone-black mb-4">Access Denied</h1>
          <p className="text-gray-600">You don't have permission to access this page.</p>
        </div>
      </div>
    );
  }

  const filteredUsers = users.filter(u => {
    const matchesSearch = 
      u.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      u.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      u.ecNumber.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesRole = roleFilter === 'all' || u.role === roleFilter;
    const matchesDepartment = departmentFilter === 'all' || u.departmentId === departmentFilter;
    
    return matchesSearch && matchesRole && matchesDepartment;
  });

  const handleCreateUser = () => {
    if (!newUser.name || !newUser.email || !newUser.ecNumber) {
      toast.error('Please fill in all required fields');
      return;
    }

    const user: User = {
      id: Date.now().toString(),
      ecNumber: newUser.ecNumber,
      name: newUser.name,
      email: newUser.email,
      role: newUser.role,
      departmentId: newUser.departmentId,
      sectionId: newUser.sectionId,
      grade: newUser.grade,
      title: newUser.title,
      reportsTo: newUser.reportsTo
    };

    setUsers([...users, user]);
    setIsCreateDialogOpen(false);
    resetForm();
    toast.success('User created successfully');
  };

  const handleEditUser = (user: User) => {
    setEditingUser(user);
    setNewUser({
      ecNumber: user.ecNumber,
      name: user.name,
      email: user.email,
      role: user.role,
      departmentId: user.departmentId,
      sectionId: user.sectionId,
      grade: user.grade,
      title: user.title,
      reportsTo: user.reportsTo,
      password: ''
    });
    setIsEditDialogOpen(true);
  };

  const handleUpdateUser = () => {
    if (!editingUser || !newUser.name || !newUser.email || !newUser.ecNumber) {
      toast.error('Please fill in all required fields');
      return;
    }

    const updatedUser: User = {
      ...editingUser,
      ecNumber: newUser.ecNumber,
      name: newUser.name,
      email: newUser.email,
      role: newUser.role,
      departmentId: newUser.departmentId,
      sectionId: newUser.sectionId,
      grade: newUser.grade,
      title: newUser.title,
      reportsTo: newUser.reportsTo
    };

    setUsers(users.map(u => u.id === editingUser.id ? updatedUser : u));
    setIsEditDialogOpen(false);
    setEditingUser(null);
    resetForm();
    toast.success('User updated successfully');
  };

  const handleDeleteUser = (userId: string) => {
    if (window.confirm('Are you sure you want to delete this user?')) {
      setUsers(users.filter(u => u.id !== userId));
      toast.success('User deleted successfully');
    }
  };

  const resetForm = () => {
    setNewUser({
      ecNumber: '',
      name: '',
      email: '',
      role: 'Appraisee',
      departmentId: '',
      sectionId: '',
      grade: '',
      title: '',
      reportsTo: '',
      password: ''
    });
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'Admin': return 'bg-red-100 text-red-800';
      case 'Appraiser': return 'bg-blue-100 text-blue-800';
      case 'Appraisee': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getDepartmentName = (departmentId: string) => {
    const dept = mockDepartments.find(d => d.id === departmentId);
    return dept?.title || 'Unknown';
  };

  const getSectionName = (sectionId: string) => {
    const section = mockSections.find(s => s.id === sectionId);
    return section?.title || 'Unknown';
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center space-x-4 mb-4">
          <Link to="/admin">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Admin Dashboard
            </Button>
          </Link>
        </div>
        <h1 className="text-3xl font-bold text-netone-black mb-2">User Management</h1>
        <p className="text-gray-600">Manage system users and their roles</p>
      </div>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Users</CardTitle>
              <CardDescription>
                Total users: {users.length} | Filtered: {filteredUsers.length}
              </CardDescription>
            </div>
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-netone-orange hover:bg-netone-orange/90">
                  <Plus className="mr-2 h-4 w-4" />
                  Add User
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Create New User</DialogTitle>
                  <DialogDescription>
                    Add a new user to the system
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="ecNumber">EC Number</Label>
                      <Input
                        id="ecNumber"
                        value={newUser.ecNumber}
                        onChange={(e) => setNewUser({...newUser, ecNumber: e.target.value})}
                        placeholder="EC001"
                      />
                    </div>
                    <div>
                      <Label htmlFor="grade">Grade</Label>
                      <Input
                        id="grade"
                        value={newUser.grade}
                        onChange={(e) => setNewUser({...newUser, grade: e.target.value})}
                        placeholder="12"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="name">Full Name</Label>
                    <Input
                      id="name"
                      value={newUser.name}
                      onChange={(e) => setNewUser({...newUser, name: e.target.value})}
                      placeholder="John Doe"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={newUser.email}
                      onChange={(e) => setNewUser({...newUser, email: e.target.value})}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="title">Job Title</Label>
                    <Input
                      id="title"
                      value={newUser.title}
                      onChange={(e) => setNewUser({...newUser, title: e.target.value})}
                      placeholder="Senior Officer"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="role">Role</Label>
                    <Select value={newUser.role} onValueChange={(value: any) => setNewUser({...newUser, role: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Admin">Admin</SelectItem>
                        <SelectItem value="Appraiser">Appraiser</SelectItem>
                        <SelectItem value="Appraisee">Appraisee</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="department">Department</Label>
                    <Select value={newUser.departmentId} onValueChange={(value) => setNewUser({...newUser, departmentId: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select department" />
                      </SelectTrigger>
                      <SelectContent>
                        {mockDepartments.map(dept => (
                          <SelectItem key={dept.id} value={dept.id}>{dept.title}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="section">Section</Label>
                    <Select value={newUser.sectionId} onValueChange={(value) => setNewUser({...newUser, sectionId: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select section" />
                      </SelectTrigger>
                      <SelectContent>
                        {mockSections.map(section => (
                          <SelectItem key={section.id} value={section.id}>{section.title}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="flex justify-end space-x-2">
                    <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleCreateUser} className="bg-netone-orange hover:bg-netone-orange/90">
                      Create User
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="mb-6 grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search users..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={roleFilter} onValueChange={setRoleFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Roles</SelectItem>
                <SelectItem value="Admin">Admin</SelectItem>
                <SelectItem value="Appraiser">Appraiser</SelectItem>
                <SelectItem value="Appraisee">Appraisee</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by department" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Departments</SelectItem>
                {mockDepartments.map(dept => (
                  <SelectItem key={dept.id} value={dept.id}>{dept.title}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Button 
              variant="outline" 
              onClick={() => {
                setSearchTerm('');
                setRoleFilter('all');
                setDepartmentFilter('all');
              }}
            >
              Clear Filters
            </Button>
          </div>
          
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>EC Number</TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Grade</TableHead>
                  <TableHead>Department</TableHead>
                  <TableHead>Section</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredUsers.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell className="font-medium">{user.ecNumber}</TableCell>
                    <TableCell>{user.name}</TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell>
                      <Badge className={getRoleBadgeColor(user.role)}>
                        {user.role}
                      </Badge>
                    </TableCell>
                    <TableCell>{user.grade}</TableCell>
                    <TableCell>{getDepartmentName(user.departmentId)}</TableCell>
                    <TableCell>{getSectionName(user.sectionId)}</TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleEditUser(user)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleDeleteUser(user.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Edit User Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-md max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
            <DialogDescription>
              Update user information
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="editEcNumber">EC Number</Label>
                <Input
                  id="editEcNumber"
                  value={newUser.ecNumber}
                  onChange={(e) => setNewUser({...newUser, ecNumber: e.target.value})}
                  placeholder="EC001"
                />
              </div>
              <div>
                <Label htmlFor="editGrade">Grade</Label>
                <Input
                  id="editGrade"
                  value={newUser.grade}
                  onChange={(e) => setNewUser({...newUser, grade: e.target.value})}
                  placeholder="12"
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="editName">Full Name</Label>
              <Input
                id="editName"
                value={newUser.name}
                onChange={(e) => setNewUser({...newUser, name: e.target.value})}
                placeholder="John Doe"
              />
            </div>
            
            <div>
              <Label htmlFor="editEmail">Email</Label>
              <Input
                id="editEmail"
                type="email"
                value={newUser.email}
                onChange={(e) => setNewUser({...newUser, email: e.target.value})}
                placeholder="<EMAIL>"
              />
            </div>
            
            <div>
              <Label htmlFor="editTitle">Job Title</Label>
              <Input
                id="editTitle"
                value={newUser.title}
                onChange={(e) => setNewUser({...newUser, title: e.target.value})}
                placeholder="Senior Officer"
              />
            </div>
            
            <div>
              <Label htmlFor="editRole">Role</Label>
              <Select value={newUser.role} onValueChange={(value: any) => setNewUser({...newUser, role: value})}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Admin">Admin</SelectItem>
                  <SelectItem value="Appraiser">Appraiser</SelectItem>
                  <SelectItem value="Appraisee">Appraisee</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="editDepartment">Department</Label>
              <Select value={newUser.departmentId} onValueChange={(value) => setNewUser({...newUser, departmentId: value})}>
                <SelectTrigger>
                  <SelectValue placeholder="Select department" />
                </SelectTrigger>
                <SelectContent>
                  {mockDepartments.map(dept => (
                    <SelectItem key={dept.id} value={dept.id}>{dept.title}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="editSection">Section</Label>
              <Select value={newUser.sectionId} onValueChange={(value) => setNewUser({...newUser, sectionId: value})}>
                <SelectTrigger>
                  <SelectValue placeholder="Select section" />
                </SelectTrigger>
                <SelectContent>
                  {mockSections.map(section => (
                    <SelectItem key={section.id} value={section.id}>{section.title}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleUpdateUser} className="bg-netone-orange hover:bg-netone-orange/90">
                Update User
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

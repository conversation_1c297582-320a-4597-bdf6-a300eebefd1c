import { useState } from 'react';
import { createFileRoute, Link } from '@tanstack/react-router';
import { useAuth } from '@/lib/auth-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Save, Send } from 'lucide-react';
import { mockUsers, mockDepartments } from '@/lib/mock-data';
import { getGradeRange, getContractTypeFromGrade } from '@/lib/types';
import { toast } from 'sonner';

export const Route = createFileRoute('/admin/contracts/create')({
  component: AdminCreateContractPage,
});

interface ContractForm {
  title: string;
  appraiserId: string;
  appraiseeId: string;
  departmentId: string;
  contractType: 'Self' | 'Other';
  evidence: string;
}

function AdminCreateContractPage() {
  const { user } = useAuth();
  const [contractForm, setContractForm] = useState<ContractForm>({
    title: '',
    appraiserId: '',
    appraiseeId: '',
    departmentId: '',
    contractType: 'Other',
    evidence: ''
  });
  const [selectedAppraisee, setSelectedAppraisee] = useState<any>(null);
  const [selectedAppraiser, setSelectedAppraiser] = useState<any>(null);

  if (!user || user.role !== 'Admin') {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-netone-black mb-4">Access Denied</h1>
          <p className="text-gray-600">You don't have permission to access this page.</p>
        </div>
      </div>
    );
  }

  const appraisers = mockUsers.filter(u => u.role === 'Appraiser' || u.role === 'Admin');
  const appraisees = mockUsers.filter(u => u.role === 'Appraisee' || u.role === 'Appraiser');

  const handleAppraiseeChange = (appraiseeId: string) => {
    const appraisee = mockUsers.find(u => u.id === appraiseeId);
    setSelectedAppraisee(appraisee);
    setContractForm({
      ...contractForm,
      appraiseeId,
      departmentId: appraisee?.departmentId || ''
    });
  };

  const handleAppraiserChange = (appraiserId: string) => {
    const appraiser = mockUsers.find(u => u.id === appraiserId);
    setSelectedAppraiser(appraiser);
    setContractForm({
      ...contractForm,
      appraiserId
    });
  };

  const handleSaveDraft = () => {
    if (!contractForm.title || !contractForm.appraiseeId || !contractForm.appraiserId) {
      toast.error('Please fill in the required fields');
      return;
    }
    
    toast.success('Contract saved as draft');
  };

  const handleSubmit = () => {
    if (!contractForm.title || !contractForm.appraiseeId || !contractForm.appraiserId) {
      toast.error('Please fill in all required fields');
      return;
    }
    
    toast.success('Contract created and assigned successfully');
  };

  const getContractTypeInfo = () => {
    if (!selectedAppraisee) return null;
    
    const gradeRange = getGradeRange(selectedAppraisee.grade);
    const contractType = getContractTypeFromGrade(selectedAppraisee.grade);
    
    return { gradeRange, contractType };
  };

  const contractTypeInfo = getContractTypeInfo();

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center space-x-4 mb-4">
          <Link to="/admin/contracts">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Contracts
            </Button>
          </Link>
        </div>
        <h1 className="text-3xl font-bold text-netone-black mb-2">Create Performance Contract</h1>
        <p className="text-gray-600">Create a new performance contract and assign it to an employee</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Contract Details */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Contract Information</CardTitle>
              <CardDescription>Basic contract details and assignment</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="title">Contract Title</Label>
                <Input
                  id="title"
                  value={contractForm.title}
                  onChange={(e) => setContractForm({...contractForm, title: e.target.value})}
                  placeholder="Performance Contract 2024"
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="appraiser">Appraiser</Label>
                  <Select value={contractForm.appraiserId} onValueChange={handleAppraiserChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select appraiser" />
                    </SelectTrigger>
                    <SelectContent>
                      {appraisers.map(appraiser => (
                        <SelectItem key={appraiser.id} value={appraiser.id}>
                          {appraiser.name} - {appraiser.title}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="appraisee">Appraisee</Label>
                  <Select value={contractForm.appraiseeId} onValueChange={handleAppraiseeChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select employee to appraise" />
                    </SelectTrigger>
                    <SelectContent>
                      {appraisees.map(employee => (
                        <SelectItem key={employee.id} value={employee.id}>
                          {employee.name} - Grade {employee.grade} ({employee.title})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div>
                <Label htmlFor="contractType">Contract Type</Label>
                <Select 
                  value={contractForm.contractType} 
                  onValueChange={(value: 'Self' | 'Other') => setContractForm({...contractForm, contractType: value})}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Self">Self Assessment</SelectItem>
                    <SelectItem value="Other">Other Assessment</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="evidence">Evidence Requirements</Label>
                <Textarea
                  id="evidence"
                  value={contractForm.evidence}
                  onChange={(e) => setContractForm({...contractForm, evidence: e.target.value})}
                  placeholder="Describe the evidence required for this contract..."
                  rows={4}
                />
              </div>

              {selectedAppraisee && contractTypeInfo && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-semibold text-blue-900 mb-2">Contract Type Information</h4>
                  <div className="space-y-2 text-sm text-blue-800">
                    <p><strong>Grade Range:</strong> {contractTypeInfo.gradeRange}</p>
                    <p><strong>Contract Type:</strong> {contractTypeInfo.contractType}</p>
                    <p><strong>Description:</strong> 
                      {contractTypeInfo.gradeRange === '6-10' && ' Individual activities with specific targets and measures.'}
                      {contractTypeInfo.gradeRange === '11-18' && ' Individual outputs with evidence of results required.'}
                      {contractTypeInfo.gradeRange === '19-23' && ' Programme-based contracts with outcomes and outputs.'}
                    </p>
                  </div>
                </div>
              )}

              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h4 className="font-semibold text-yellow-900 mb-2">Next Steps</h4>
                <div className="text-sm text-yellow-800">
                  <p>After creating this contract:</p>
                  <ol className="list-decimal list-inside mt-2 space-y-1">
                    <li>The appraiser will need to add specific performance items</li>
                    <li>The contract will be submitted to the appraisee</li>
                    <li>The appraisee will fill in their performance data</li>
                    <li>The appraiser will review and finalize the contract</li>
                  </ol>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Contract Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Contract Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <Label className="text-sm font-medium">Created by</Label>
                <p className="text-sm text-gray-600">{user?.name} (Admin)</p>
              </div>
              
              {selectedAppraiser && (
                <div>
                  <Label className="text-sm font-medium">Appraiser</Label>
                  <p className="text-sm text-gray-600">{selectedAppraiser.name}</p>
                  <p className="text-xs text-gray-500">{selectedAppraiser.title}</p>
                </div>
              )}
              
              {selectedAppraisee && (
                <>
                  <div>
                    <Label className="text-sm font-medium">Appraisee</Label>
                    <p className="text-sm text-gray-600">{selectedAppraisee.name}</p>
                    <p className="text-xs text-gray-500">{selectedAppraisee.title}</p>
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium">Grade Level</Label>
                    <Badge variant="outline">Grade {selectedAppraisee.grade}</Badge>
                  </div>
                  
                  {contractTypeInfo && (
                    <div>
                      <Label className="text-sm font-medium">Contract Type</Label>
                      <Badge>{contractTypeInfo.contractType}</Badge>
                    </div>
                  )}
                  
                  <div>
                    <Label className="text-sm font-medium">Department</Label>
                    <p className="text-sm text-gray-600">
                      {mockDepartments.find(d => d.id === selectedAppraisee.departmentId)?.title}
                    </p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button 
                variant="outline" 
                className="w-full"
                onClick={handleSaveDraft}
              >
                <Save className="mr-2 h-4 w-4" />
                Save as Draft
              </Button>
              
              <Button 
                className="w-full bg-netone-orange hover:bg-netone-orange/90"
                onClick={handleSubmit}
              >
                <Send className="mr-2 h-4 w-4" />
                Create & Assign Contract
              </Button>
              
              <div className="text-xs text-gray-500 mt-2">
                <p>Note: The appraiser will be notified to add performance items to this contract.</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

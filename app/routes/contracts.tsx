import { useState } from "react";
import { createFileRoute } from "@tanstack/react-router";
import { useAuth } from "@/lib/auth-context";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Search, Eye, Edit, FileText, Filter } from "lucide-react";
import {
  mockContracts,
  mockUsers,
  mockDepartments,
  getUserById,
  getDepartmentById,
} from "@/lib/mock-data";
import { Contract, getContractTypeFromGrade } from "@/lib/types";

export const Route = createFileRoute("/contracts")({
  component: ContractsPage,
});

function ContractsPage() {
  const { user } = useAuth();
  const [contracts] = useState<Contract[]>(mockContracts);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [departmentFilter, setDepartmentFilter] = useState<string>("all");
  const [gradeFilter, setGradeFilter] = useState<string>("all");

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-netone-black mb-4">
            Loading...
          </h1>
        </div>
      </div>
    );
  }

  const filteredContracts = contracts.filter((contract) => {
    const appraiser = getUserById(contract.appraiserId);
    const appraisee = getUserById(contract.appraiseeId);
    const department = getDepartmentById(contract.departmentId);

    const matchesSearch =
      contract.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      appraiser?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      appraisee?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      department?.title.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus =
      statusFilter === "all" || contract.status === statusFilter;
    const matchesType =
      typeFilter === "all" || contract.contractType === typeFilter;
    const matchesDepartment =
      departmentFilter === "all" || contract.departmentId === departmentFilter;
    const matchesGrade =
      gradeFilter === "all" || appraisee?.grade === gradeFilter;

    return (
      matchesSearch &&
      matchesStatus &&
      matchesType &&
      matchesDepartment &&
      matchesGrade
    );
  });

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "Draft":
        return "bg-gray-100 text-gray-800";
      case "Submitted":
        return "bg-blue-100 text-blue-800";
      case "Filled":
        return "bg-yellow-100 text-yellow-800";
      case "Completed":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getContractType = (contract: Contract) => {
    const appraisee = getUserById(contract.appraiseeId);
    if (appraisee) {
      return getContractTypeFromGrade(appraisee.grade);
    }
    return "Unknown";
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-netone-black mb-2">
          All Contracts
        </h1>
        <p className="text-gray-600">
          View and manage all performance contracts
        </p>
      </div>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="flex items-center">
                <FileText className="mr-2 h-5 w-5" />
                Contracts
              </CardTitle>
              <CardDescription>
                Total contracts: {contracts.length} | Filtered:{" "}
                {filteredContracts.length}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="mb-6 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search contracts..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="Draft">Draft</SelectItem>
                  <SelectItem value="Submitted">Submitted</SelectItem>
                  <SelectItem value="Filled">Filled</SelectItem>
                  <SelectItem value="Completed">Completed</SelectItem>
                </SelectContent>
              </Select>

              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="Self">Self</SelectItem>
                  <SelectItem value="Other">Other</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={departmentFilter}
                onValueChange={setDepartmentFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by department" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Departments</SelectItem>
                  {mockDepartments.map((dept) => (
                    <SelectItem key={dept.id} value={dept.id}>
                      {dept.title}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Select value={gradeFilter} onValueChange={setGradeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by grade" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Grades</SelectItem>
                  {Array.from(new Set(mockUsers.map((u) => u.grade)))
                    .sort()
                    .map((grade) => (
                      <SelectItem key={grade} value={grade}>
                        Grade {grade}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>

              <Button
                variant="outline"
                onClick={() => {
                  setSearchTerm("");
                  setStatusFilter("all");
                  setTypeFilter("all");
                  setDepartmentFilter("all");
                  setGradeFilter("all");
                }}
                className="flex items-center">
                <Filter className="mr-2 h-4 w-4" />
                Clear Filters
              </Button>
            </div>
          </div>

          {/* Contracts Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Contract Title</TableHead>
                  <TableHead>Appraiser</TableHead>
                  <TableHead>Appraisee</TableHead>
                  <TableHead>Department</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Grade Level</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredContracts.map((contract) => {
                  const appraiser = getUserById(contract.appraiserId);
                  const appraisee = getUserById(contract.appraiseeId);
                  const department = getDepartmentById(contract.departmentId);

                  return (
                    <TableRow key={contract.id}>
                      <TableCell className="font-medium">
                        <div>
                          <div className="font-semibold">{contract.title}</div>
                          <div className="text-sm text-gray-500">
                            {getContractType(contract)}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">
                            {appraiser?.name || "Unknown"}
                          </div>
                          <div className="text-sm text-gray-500">
                            {appraiser?.title}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">
                            {appraisee?.name || "Unknown"}
                          </div>
                          <div className="text-sm text-gray-500">
                            {appraisee?.title}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{department?.title || "Unknown"}</TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            contract.contractType === "Self"
                              ? "secondary"
                              : "default"
                          }>
                          {contract.contractType}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          Grade {appraisee?.grade}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge className={getStatusBadgeColor(contract.status)}>
                          {contract.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>

          {filteredContracts.length === 0 && (
            <div className="text-center py-8">
              <FileText className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-semibold text-gray-900">
                No contracts found
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                Try adjusting your search or filter criteria.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Summary Cards */}
      <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Total Contracts
                </p>
                <p className="text-2xl font-bold">{contracts.length}</p>
              </div>
              <FileText className="h-8 w-8 text-gray-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Draft</p>
                <p className="text-2xl font-bold">
                  {contracts.filter((c) => c.status === "Draft").length}
                </p>
              </div>
              <div className="h-8 w-8 rounded-full bg-gray-100"></div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">In Progress</p>
                <p className="text-2xl font-bold">
                  {
                    contracts.filter(
                      (c) => c.status === "Submitted" || c.status === "Filled"
                    ).length
                  }
                </p>
              </div>
              <div className="h-8 w-8 rounded-full bg-blue-100"></div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Completed</p>
                <p className="text-2xl font-bold">
                  {contracts.filter((c) => c.status === "Completed").length}
                </p>
              </div>
              <div className="h-8 w-8 rounded-full bg-green-100"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

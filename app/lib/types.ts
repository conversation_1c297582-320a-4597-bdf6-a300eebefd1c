// User and Organization Types
export interface User {
  ecNumber: string;
  name: string;
  email: string;
  role: "Admin" | "Appraiser" | "Appraisee";
  id: string;
  departmentId: string;
  sectionId: string;
  grade: string;
  title: string;
  reportsTo: string;
}

export interface Department {
  headId: string;
  title: string;
  sections: Section[];
  id: string;
}

export interface Section {
  headId: string;
  title: string;
  parentId: string;
  parentType: "Department" | "Section";
  id: string;
}

// Base Contract Type
export interface BaseContract {
  departmentId: string;
  appraiserId: string;
  appraiseeId: string;
  evidence: string;
  contractType: "Self" | "Other";
  status: "Draft" | "Submitted" | "Filled" | "Completed";
  id: string;
  title: string;
}

// Contract for Grades 19-23
export interface ProgrammeContract extends BaseContract {
  programmes: Programme[];
}

export interface Programme {
  title: string;
  outcomes: Outcome[];
  contractId: string;
  id: string;
}

export interface Outcome {
  title: string;
  weight: string;
  outputs: Output[];
  contractId: string;
  programmeId: string;
  id: string;
}

export interface Output {
  unit: string;
  weight: string;
  prevYearPerf: string;
  target: string;
  allowableVariance: string;
  actualPerformance: string;
  rawScore: string;
  weightedScore: string;
  contractId: string;
  outcomeId: string;
  programmeId: string;
  title: string;
  id: string;
}

export interface BudgetPerformance {
  weight: string;
  allowableVariance: string;
  actualUtilization: string;
  actualVariance: string;
  rating: string;
  weightedScore: string;
  contractId: string;
  title: string;
  id: string;
}

// Contract for Grades 11-18
export interface IndividualOutputContract extends BaseContract {
  individualOutputs: IndividualOutput[];
}

export interface IndividualOutput {
  unit: string;
  weight: string;
  title: string;
  measure: string;
  target: string;
  allowableVariance: string;
  actualVariance: string;
  score: string;
  weightedScore: string;
  contractId: string;
  actions: string;
  evidenceOfResults: string;
  id: string;
}

// Contract for Grades 6-10
export interface IndividualActivityContract extends BaseContract {
  individualActivities: IndividualActivity[];
}

export interface IndividualActivity {
  unit: string;
  weight: string;
  title: string;
  measure: string;
  target: string;
  allowableVariance: string;
  actualVariance: string;
  score: string;
  weightedScore: string;
  contractId: string;
  actions: string;
  evidenceOfResults: string;
  id: string;
}

// Union type for all contract types
export type Contract = ProgrammeContract | IndividualOutputContract | IndividualActivityContract;

// Auth Context Type
export interface AuthUser {
  user: User | null;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => void;
  isAuthenticated: boolean;
}

// Form Types
export interface LoginForm {
  email: string;
  password: string;
}

export interface CreateUserForm {
  ecNumber: string;
  name: string;
  email: string;
  role: "Admin" | "Appraiser" | "Appraisee";
  departmentId: string;
  sectionId: string;
  grade: string;
  title: string;
  reportsTo: string;
  password: string;
}

// Utility Types
export type ContractStatus = "Draft" | "Submitted" | "Filled" | "Completed";
export type UserRole = "Admin" | "Appraiser" | "Appraisee";
export type GradeRange = "6-10" | "11-18" | "19-23";

// Helper function to determine grade range
export function getGradeRange(grade: string): GradeRange {
  const gradeNum = parseInt(grade);
  if (gradeNum >= 6 && gradeNum <= 10) return "6-10";
  if (gradeNum >= 11 && gradeNum <= 18) return "11-18";
  if (gradeNum >= 19 && gradeNum <= 23) return "19-23";
  return "6-10"; // default
}

// Helper function to determine contract type from grade
export function getContractTypeFromGrade(grade: string): string {
  const range = getGradeRange(grade);
  switch (range) {
    case "6-10": return "Individual Activity";
    case "11-18": return "Individual Output";
    case "19-23": return "Programme";
    default: return "Individual Activity";
  }
}

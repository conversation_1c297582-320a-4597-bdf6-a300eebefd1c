import { User, Department, Section, Contract, ProgrammeContract, IndividualOutputContract, IndividualActivityContract } from './types';

// Mock Users
export const mockUsers: User[] = [
  {
    id: "1",
    ecNumber: "EC001",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Admin",
    departmentId: "dept1",
    sectionId: "sec1",
    grade: "20",
    title: "System Administrator",
    reportsTo: ""
  },
  {
    id: "2",
    ecNumber: "EC002",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Appraiser",
    departmentId: "dept1",
    sectionId: "sec1",
    grade: "18",
    title: "Department Manager",
    reportsTo: "1"
  },
  {
    id: "3",
    ecNumber: "EC003",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Appraisee",
    departmentId: "dept1",
    sectionId: "sec2",
    grade: "12",
    title: "Senior Officer",
    reportsTo: "2"
  },
  {
    id: "4",
    ecNumber: "EC004",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Appraisee",
    departmentId: "dept2",
    sectionId: "sec3",
    grade: "8",
    title: "Junior Officer",
    reportsTo: "2"
  }
];

// Mock Departments and Sections
export const mockSections: Section[] = [
  {
    id: "sec1",
    headId: "2",
    title: "Management Section",
    parentId: "dept1",
    parentType: "Department"
  },
  {
    id: "sec2",
    headId: "2",
    title: "Operations Section",
    parentId: "dept1",
    parentType: "Department"
  },
  {
    id: "sec3",
    headId: "2",
    title: "Support Section",
    parentId: "dept2",
    parentType: "Department"
  }
];

export const mockDepartments: Department[] = [
  {
    id: "dept1",
    headId: "2",
    title: "Information Technology",
    sections: mockSections.filter(s => s.parentId === "dept1")
  },
  {
    id: "dept2",
    headId: "2",
    title: "Human Resources",
    sections: mockSections.filter(s => s.parentId === "dept2")
  }
];

// Mock Contracts
export const mockContracts: Contract[] = [
  // Programme Contract (Grade 19-23)
  {
    id: "contract1",
    departmentId: "dept1",
    appraiserId: "1",
    appraiseeId: "2",
    evidence: "Performance reports and metrics",
    contractType: "Other",
    status: "Draft",
    title: "IT Department Strategic Goals 2024",
    programmes: [
      {
        id: "prog1",
        title: "Digital Transformation Initiative",
        contractId: "contract1",
        outcomes: [
          {
            id: "outcome1",
            title: "Improve System Efficiency",
            weight: "40",
            contractId: "contract1",
            programmeId: "prog1",
            outputs: [
              {
                id: "output1",
                title: "System Uptime",
                unit: "Percentage",
                weight: "20",
                prevYearPerf: "95",
                target: "99",
                allowableVariance: "2",
                actualPerformance: "",
                rawScore: "",
                weightedScore: "",
                contractId: "contract1",
                outcomeId: "outcome1",
                programmeId: "prog1"
              }
            ]
          }
        ]
      }
    ]
  } as ProgrammeContract,
  
  // Individual Output Contract (Grade 11-18)
  {
    id: "contract2",
    departmentId: "dept1",
    appraiserId: "2",
    appraiseeId: "3",
    evidence: "Monthly reports and deliverables",
    contractType: "Other",
    status: "Submitted",
    title: "Senior Officer Performance Contract 2024",
    individualOutputs: [
      {
        id: "output1",
        title: "Project Completion Rate",
        unit: "Percentage",
        weight: "30",
        measure: "Number of projects completed on time",
        target: "90",
        allowableVariance: "5",
        actualVariance: "",
        score: "",
        weightedScore: "",
        contractId: "contract2",
        actions: "Complete assigned projects within deadlines",
        evidenceOfResults: "Project completion reports"
      }
    ]
  } as IndividualOutputContract,
  
  // Individual Activity Contract (Grade 6-10)
  {
    id: "contract3",
    departmentId: "dept2",
    appraiserId: "2",
    appraiseeId: "4",
    evidence: "Daily activity logs",
    contractType: "Other",
    status: "Filled",
    title: "Junior Officer Activity Contract 2024",
    individualActivities: [
      {
        id: "activity1",
        title: "Data Entry Accuracy",
        unit: "Percentage",
        weight: "25",
        measure: "Accuracy rate of data entry tasks",
        target: "98",
        allowableVariance: "2",
        actualVariance: "",
        score: "",
        weightedScore: "",
        contractId: "contract3",
        actions: "Maintain high accuracy in data entry",
        evidenceOfResults: "Quality control reports"
      }
    ]
  } as IndividualActivityContract
];

// Mock authentication function
export const mockAuth = {
  authenticate: (email: string, password: string): User | null => {
    // Simple mock authentication - in real app, this would be secure
    const user = mockUsers.find(u => u.email === email);
    if (user && password === "password123") {
      return user;
    }
    return null;
  }
};

// Helper functions to get data
export const getUserById = (id: string): User | undefined => {
  return mockUsers.find(user => user.id === id);
};

export const getDepartmentById = (id: string): Department | undefined => {
  return mockDepartments.find(dept => dept.id === id);
};

export const getSectionById = (id: string): Section | undefined => {
  return mockSections.find(section => section.id === id);
};

export const getContractsByUserId = (userId: string): Contract[] => {
  return mockContracts.filter(contract => 
    contract.appraiseeId === userId || contract.appraiserId === userId
  );
};

export const getContractsByAppraiserId = (appraiserId: string): Contract[] => {
  return mockContracts.filter(contract => contract.appraiserId === appraiserId);
};

export const getContractsByAppraiseeId = (appraiseeId: string): Contract[] => {
  return mockContracts.filter(contract => contract.appraiseeId === appraiseeId);
};

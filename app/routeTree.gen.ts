/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as UsersImport } from './routes/users'
import { Route as LoginImport } from './routes/login'
import { Route as DepartmentsImport } from './routes/departments'
import { Route as ContractsImport } from './routes/contracts'
import { Route as IndexImport } from './routes/index'
import { Route as ContractsMyImport } from './routes/contracts/my'
import { Route as ContractsCreatedImport } from './routes/contracts/created'
import { Route as ContractsCreateImport } from './routes/contracts/create'
import { Route as ContractsAssignedImport } from './routes/contracts/assigned'

// Create/Update Routes

const UsersRoute = UsersImport.update({
  id: '/users',
  path: '/users',
  getParentRoute: () => rootRoute,
} as any)

const LoginRoute = LoginImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRoute,
} as any)

const DepartmentsRoute = DepartmentsImport.update({
  id: '/departments',
  path: '/departments',
  getParentRoute: () => rootRoute,
} as any)

const ContractsRoute = ContractsImport.update({
  id: '/contracts',
  path: '/contracts',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

const ContractsMyRoute = ContractsMyImport.update({
  id: '/my',
  path: '/my',
  getParentRoute: () => ContractsRoute,
} as any)

const ContractsCreatedRoute = ContractsCreatedImport.update({
  id: '/created',
  path: '/created',
  getParentRoute: () => ContractsRoute,
} as any)

const ContractsCreateRoute = ContractsCreateImport.update({
  id: '/create',
  path: '/create',
  getParentRoute: () => ContractsRoute,
} as any)

const ContractsAssignedRoute = ContractsAssignedImport.update({
  id: '/assigned',
  path: '/assigned',
  getParentRoute: () => ContractsRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/contracts': {
      id: '/contracts'
      path: '/contracts'
      fullPath: '/contracts'
      preLoaderRoute: typeof ContractsImport
      parentRoute: typeof rootRoute
    }
    '/departments': {
      id: '/departments'
      path: '/departments'
      fullPath: '/departments'
      preLoaderRoute: typeof DepartmentsImport
      parentRoute: typeof rootRoute
    }
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginImport
      parentRoute: typeof rootRoute
    }
    '/users': {
      id: '/users'
      path: '/users'
      fullPath: '/users'
      preLoaderRoute: typeof UsersImport
      parentRoute: typeof rootRoute
    }
    '/contracts/assigned': {
      id: '/contracts/assigned'
      path: '/assigned'
      fullPath: '/contracts/assigned'
      preLoaderRoute: typeof ContractsAssignedImport
      parentRoute: typeof ContractsImport
    }
    '/contracts/create': {
      id: '/contracts/create'
      path: '/create'
      fullPath: '/contracts/create'
      preLoaderRoute: typeof ContractsCreateImport
      parentRoute: typeof ContractsImport
    }
    '/contracts/created': {
      id: '/contracts/created'
      path: '/created'
      fullPath: '/contracts/created'
      preLoaderRoute: typeof ContractsCreatedImport
      parentRoute: typeof ContractsImport
    }
    '/contracts/my': {
      id: '/contracts/my'
      path: '/my'
      fullPath: '/contracts/my'
      preLoaderRoute: typeof ContractsMyImport
      parentRoute: typeof ContractsImport
    }
  }
}

// Create and export the route tree

interface ContractsRouteChildren {
  ContractsAssignedRoute: typeof ContractsAssignedRoute
  ContractsCreateRoute: typeof ContractsCreateRoute
  ContractsCreatedRoute: typeof ContractsCreatedRoute
  ContractsMyRoute: typeof ContractsMyRoute
}

const ContractsRouteChildren: ContractsRouteChildren = {
  ContractsAssignedRoute: ContractsAssignedRoute,
  ContractsCreateRoute: ContractsCreateRoute,
  ContractsCreatedRoute: ContractsCreatedRoute,
  ContractsMyRoute: ContractsMyRoute,
}

const ContractsRouteWithChildren = ContractsRoute._addFileChildren(
  ContractsRouteChildren,
)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/contracts': typeof ContractsRouteWithChildren
  '/departments': typeof DepartmentsRoute
  '/login': typeof LoginRoute
  '/users': typeof UsersRoute
  '/contracts/assigned': typeof ContractsAssignedRoute
  '/contracts/create': typeof ContractsCreateRoute
  '/contracts/created': typeof ContractsCreatedRoute
  '/contracts/my': typeof ContractsMyRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/contracts': typeof ContractsRouteWithChildren
  '/departments': typeof DepartmentsRoute
  '/login': typeof LoginRoute
  '/users': typeof UsersRoute
  '/contracts/assigned': typeof ContractsAssignedRoute
  '/contracts/create': typeof ContractsCreateRoute
  '/contracts/created': typeof ContractsCreatedRoute
  '/contracts/my': typeof ContractsMyRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/contracts': typeof ContractsRouteWithChildren
  '/departments': typeof DepartmentsRoute
  '/login': typeof LoginRoute
  '/users': typeof UsersRoute
  '/contracts/assigned': typeof ContractsAssignedRoute
  '/contracts/create': typeof ContractsCreateRoute
  '/contracts/created': typeof ContractsCreatedRoute
  '/contracts/my': typeof ContractsMyRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/contracts'
    | '/departments'
    | '/login'
    | '/users'
    | '/contracts/assigned'
    | '/contracts/create'
    | '/contracts/created'
    | '/contracts/my'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/contracts'
    | '/departments'
    | '/login'
    | '/users'
    | '/contracts/assigned'
    | '/contracts/create'
    | '/contracts/created'
    | '/contracts/my'
  id:
    | '__root__'
    | '/'
    | '/contracts'
    | '/departments'
    | '/login'
    | '/users'
    | '/contracts/assigned'
    | '/contracts/create'
    | '/contracts/created'
    | '/contracts/my'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  ContractsRoute: typeof ContractsRouteWithChildren
  DepartmentsRoute: typeof DepartmentsRoute
  LoginRoute: typeof LoginRoute
  UsersRoute: typeof UsersRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  ContractsRoute: ContractsRouteWithChildren,
  DepartmentsRoute: DepartmentsRoute,
  LoginRoute: LoginRoute,
  UsersRoute: UsersRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/contracts",
        "/departments",
        "/login",
        "/users"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/contracts": {
      "filePath": "contracts.tsx",
      "children": [
        "/contracts/assigned",
        "/contracts/create",
        "/contracts/created",
        "/contracts/my"
      ]
    },
    "/departments": {
      "filePath": "departments.tsx"
    },
    "/login": {
      "filePath": "login.tsx"
    },
    "/users": {
      "filePath": "users.tsx"
    },
    "/contracts/assigned": {
      "filePath": "contracts/assigned.tsx",
      "parent": "/contracts"
    },
    "/contracts/create": {
      "filePath": "contracts/create.tsx",
      "parent": "/contracts"
    },
    "/contracts/created": {
      "filePath": "contracts/created.tsx",
      "parent": "/contracts"
    },
    "/contracts/my": {
      "filePath": "contracts/my.tsx",
      "parent": "/contracts"
    }
  }
}
ROUTE_MANIFEST_END */

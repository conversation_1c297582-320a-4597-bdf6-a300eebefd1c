/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as LoginImport } from './routes/login'
import { Route as AppraiserImport } from './routes/appraiser'
import { Route as AppraiseeImport } from './routes/appraisee'
import { Route as AdminImport } from './routes/admin'
import { Route as IndexImport } from './routes/index'
import { Route as AppraiseeContractsImport } from './routes/appraisee/contracts'
import { Route as AdminUsersImport } from './routes/admin/users'
import { Route as AdminDepartmentsImport } from './routes/admin/departments'
import { Route as AdminContractsImport } from './routes/admin/contracts'
import { Route as AppraiserContractsMyImport } from './routes/appraiser/contracts/my'
import { Route as AppraiserContractsCreatedImport } from './routes/appraiser/contracts/created'
import { Route as AppraiserContractsCreateImport } from './routes/appraiser/contracts/create'
import { Route as AdminContractsCreateImport } from './routes/admin/contracts/create'

// Create/Update Routes

const LoginRoute = LoginImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRoute,
} as any)

const AppraiserRoute = AppraiserImport.update({
  id: '/appraiser',
  path: '/appraiser',
  getParentRoute: () => rootRoute,
} as any)

const AppraiseeRoute = AppraiseeImport.update({
  id: '/appraisee',
  path: '/appraisee',
  getParentRoute: () => rootRoute,
} as any)

const AdminRoute = AdminImport.update({
  id: '/admin',
  path: '/admin',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

const AppraiseeContractsRoute = AppraiseeContractsImport.update({
  id: '/contracts',
  path: '/contracts',
  getParentRoute: () => AppraiseeRoute,
} as any)

const AdminUsersRoute = AdminUsersImport.update({
  id: '/users',
  path: '/users',
  getParentRoute: () => AdminRoute,
} as any)

const AdminDepartmentsRoute = AdminDepartmentsImport.update({
  id: '/departments',
  path: '/departments',
  getParentRoute: () => AdminRoute,
} as any)

const AdminContractsRoute = AdminContractsImport.update({
  id: '/contracts',
  path: '/contracts',
  getParentRoute: () => AdminRoute,
} as any)

const AppraiserContractsMyRoute = AppraiserContractsMyImport.update({
  id: '/contracts/my',
  path: '/contracts/my',
  getParentRoute: () => AppraiserRoute,
} as any)

const AppraiserContractsCreatedRoute = AppraiserContractsCreatedImport.update({
  id: '/contracts/created',
  path: '/contracts/created',
  getParentRoute: () => AppraiserRoute,
} as any)

const AppraiserContractsCreateRoute = AppraiserContractsCreateImport.update({
  id: '/contracts/create',
  path: '/contracts/create',
  getParentRoute: () => AppraiserRoute,
} as any)

const AdminContractsCreateRoute = AdminContractsCreateImport.update({
  id: '/create',
  path: '/create',
  getParentRoute: () => AdminContractsRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/admin': {
      id: '/admin'
      path: '/admin'
      fullPath: '/admin'
      preLoaderRoute: typeof AdminImport
      parentRoute: typeof rootRoute
    }
    '/appraisee': {
      id: '/appraisee'
      path: '/appraisee'
      fullPath: '/appraisee'
      preLoaderRoute: typeof AppraiseeImport
      parentRoute: typeof rootRoute
    }
    '/appraiser': {
      id: '/appraiser'
      path: '/appraiser'
      fullPath: '/appraiser'
      preLoaderRoute: typeof AppraiserImport
      parentRoute: typeof rootRoute
    }
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginImport
      parentRoute: typeof rootRoute
    }
    '/admin/contracts': {
      id: '/admin/contracts'
      path: '/contracts'
      fullPath: '/admin/contracts'
      preLoaderRoute: typeof AdminContractsImport
      parentRoute: typeof AdminImport
    }
    '/admin/departments': {
      id: '/admin/departments'
      path: '/departments'
      fullPath: '/admin/departments'
      preLoaderRoute: typeof AdminDepartmentsImport
      parentRoute: typeof AdminImport
    }
    '/admin/users': {
      id: '/admin/users'
      path: '/users'
      fullPath: '/admin/users'
      preLoaderRoute: typeof AdminUsersImport
      parentRoute: typeof AdminImport
    }
    '/appraisee/contracts': {
      id: '/appraisee/contracts'
      path: '/contracts'
      fullPath: '/appraisee/contracts'
      preLoaderRoute: typeof AppraiseeContractsImport
      parentRoute: typeof AppraiseeImport
    }
    '/admin/contracts/create': {
      id: '/admin/contracts/create'
      path: '/create'
      fullPath: '/admin/contracts/create'
      preLoaderRoute: typeof AdminContractsCreateImport
      parentRoute: typeof AdminContractsImport
    }
    '/appraiser/contracts/create': {
      id: '/appraiser/contracts/create'
      path: '/contracts/create'
      fullPath: '/appraiser/contracts/create'
      preLoaderRoute: typeof AppraiserContractsCreateImport
      parentRoute: typeof AppraiserImport
    }
    '/appraiser/contracts/created': {
      id: '/appraiser/contracts/created'
      path: '/contracts/created'
      fullPath: '/appraiser/contracts/created'
      preLoaderRoute: typeof AppraiserContractsCreatedImport
      parentRoute: typeof AppraiserImport
    }
    '/appraiser/contracts/my': {
      id: '/appraiser/contracts/my'
      path: '/contracts/my'
      fullPath: '/appraiser/contracts/my'
      preLoaderRoute: typeof AppraiserContractsMyImport
      parentRoute: typeof AppraiserImport
    }
  }
}

// Create and export the route tree

interface AdminContractsRouteChildren {
  AdminContractsCreateRoute: typeof AdminContractsCreateRoute
}

const AdminContractsRouteChildren: AdminContractsRouteChildren = {
  AdminContractsCreateRoute: AdminContractsCreateRoute,
}

const AdminContractsRouteWithChildren = AdminContractsRoute._addFileChildren(
  AdminContractsRouteChildren,
)

interface AdminRouteChildren {
  AdminContractsRoute: typeof AdminContractsRouteWithChildren
  AdminDepartmentsRoute: typeof AdminDepartmentsRoute
  AdminUsersRoute: typeof AdminUsersRoute
}

const AdminRouteChildren: AdminRouteChildren = {
  AdminContractsRoute: AdminContractsRouteWithChildren,
  AdminDepartmentsRoute: AdminDepartmentsRoute,
  AdminUsersRoute: AdminUsersRoute,
}

const AdminRouteWithChildren = AdminRoute._addFileChildren(AdminRouteChildren)

interface AppraiseeRouteChildren {
  AppraiseeContractsRoute: typeof AppraiseeContractsRoute
}

const AppraiseeRouteChildren: AppraiseeRouteChildren = {
  AppraiseeContractsRoute: AppraiseeContractsRoute,
}

const AppraiseeRouteWithChildren = AppraiseeRoute._addFileChildren(
  AppraiseeRouteChildren,
)

interface AppraiserRouteChildren {
  AppraiserContractsCreateRoute: typeof AppraiserContractsCreateRoute
  AppraiserContractsCreatedRoute: typeof AppraiserContractsCreatedRoute
  AppraiserContractsMyRoute: typeof AppraiserContractsMyRoute
}

const AppraiserRouteChildren: AppraiserRouteChildren = {
  AppraiserContractsCreateRoute: AppraiserContractsCreateRoute,
  AppraiserContractsCreatedRoute: AppraiserContractsCreatedRoute,
  AppraiserContractsMyRoute: AppraiserContractsMyRoute,
}

const AppraiserRouteWithChildren = AppraiserRoute._addFileChildren(
  AppraiserRouteChildren,
)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/admin': typeof AdminRouteWithChildren
  '/appraisee': typeof AppraiseeRouteWithChildren
  '/appraiser': typeof AppraiserRouteWithChildren
  '/login': typeof LoginRoute
  '/admin/contracts': typeof AdminContractsRouteWithChildren
  '/admin/departments': typeof AdminDepartmentsRoute
  '/admin/users': typeof AdminUsersRoute
  '/appraisee/contracts': typeof AppraiseeContractsRoute
  '/admin/contracts/create': typeof AdminContractsCreateRoute
  '/appraiser/contracts/create': typeof AppraiserContractsCreateRoute
  '/appraiser/contracts/created': typeof AppraiserContractsCreatedRoute
  '/appraiser/contracts/my': typeof AppraiserContractsMyRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/admin': typeof AdminRouteWithChildren
  '/appraisee': typeof AppraiseeRouteWithChildren
  '/appraiser': typeof AppraiserRouteWithChildren
  '/login': typeof LoginRoute
  '/admin/contracts': typeof AdminContractsRouteWithChildren
  '/admin/departments': typeof AdminDepartmentsRoute
  '/admin/users': typeof AdminUsersRoute
  '/appraisee/contracts': typeof AppraiseeContractsRoute
  '/admin/contracts/create': typeof AdminContractsCreateRoute
  '/appraiser/contracts/create': typeof AppraiserContractsCreateRoute
  '/appraiser/contracts/created': typeof AppraiserContractsCreatedRoute
  '/appraiser/contracts/my': typeof AppraiserContractsMyRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/admin': typeof AdminRouteWithChildren
  '/appraisee': typeof AppraiseeRouteWithChildren
  '/appraiser': typeof AppraiserRouteWithChildren
  '/login': typeof LoginRoute
  '/admin/contracts': typeof AdminContractsRouteWithChildren
  '/admin/departments': typeof AdminDepartmentsRoute
  '/admin/users': typeof AdminUsersRoute
  '/appraisee/contracts': typeof AppraiseeContractsRoute
  '/admin/contracts/create': typeof AdminContractsCreateRoute
  '/appraiser/contracts/create': typeof AppraiserContractsCreateRoute
  '/appraiser/contracts/created': typeof AppraiserContractsCreatedRoute
  '/appraiser/contracts/my': typeof AppraiserContractsMyRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/admin'
    | '/appraisee'
    | '/appraiser'
    | '/login'
    | '/admin/contracts'
    | '/admin/departments'
    | '/admin/users'
    | '/appraisee/contracts'
    | '/admin/contracts/create'
    | '/appraiser/contracts/create'
    | '/appraiser/contracts/created'
    | '/appraiser/contracts/my'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/admin'
    | '/appraisee'
    | '/appraiser'
    | '/login'
    | '/admin/contracts'
    | '/admin/departments'
    | '/admin/users'
    | '/appraisee/contracts'
    | '/admin/contracts/create'
    | '/appraiser/contracts/create'
    | '/appraiser/contracts/created'
    | '/appraiser/contracts/my'
  id:
    | '__root__'
    | '/'
    | '/admin'
    | '/appraisee'
    | '/appraiser'
    | '/login'
    | '/admin/contracts'
    | '/admin/departments'
    | '/admin/users'
    | '/appraisee/contracts'
    | '/admin/contracts/create'
    | '/appraiser/contracts/create'
    | '/appraiser/contracts/created'
    | '/appraiser/contracts/my'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AdminRoute: typeof AdminRouteWithChildren
  AppraiseeRoute: typeof AppraiseeRouteWithChildren
  AppraiserRoute: typeof AppraiserRouteWithChildren
  LoginRoute: typeof LoginRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AdminRoute: AdminRouteWithChildren,
  AppraiseeRoute: AppraiseeRouteWithChildren,
  AppraiserRoute: AppraiserRouteWithChildren,
  LoginRoute: LoginRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/admin",
        "/appraisee",
        "/appraiser",
        "/login"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/admin": {
      "filePath": "admin.tsx",
      "children": [
        "/admin/contracts",
        "/admin/departments",
        "/admin/users"
      ]
    },
    "/appraisee": {
      "filePath": "appraisee.tsx",
      "children": [
        "/appraisee/contracts"
      ]
    },
    "/appraiser": {
      "filePath": "appraiser.tsx",
      "children": [
        "/appraiser/contracts/create",
        "/appraiser/contracts/created",
        "/appraiser/contracts/my"
      ]
    },
    "/login": {
      "filePath": "login.tsx"
    },
    "/admin/contracts": {
      "filePath": "admin/contracts.tsx",
      "parent": "/admin",
      "children": [
        "/admin/contracts/create"
      ]
    },
    "/admin/departments": {
      "filePath": "admin/departments.tsx",
      "parent": "/admin"
    },
    "/admin/users": {
      "filePath": "admin/users.tsx",
      "parent": "/admin"
    },
    "/appraisee/contracts": {
      "filePath": "appraisee/contracts.tsx",
      "parent": "/appraisee"
    },
    "/admin/contracts/create": {
      "filePath": "admin/contracts/create.tsx",
      "parent": "/admin/contracts"
    },
    "/appraiser/contracts/create": {
      "filePath": "appraiser/contracts/create.tsx",
      "parent": "/appraiser"
    },
    "/appraiser/contracts/created": {
      "filePath": "appraiser/contracts/created.tsx",
      "parent": "/appraiser"
    },
    "/appraiser/contracts/my": {
      "filePath": "appraiser/contracts/my.tsx",
      "parent": "/appraiser"
    }
  }
}
ROUTE_MANIFEST_END */

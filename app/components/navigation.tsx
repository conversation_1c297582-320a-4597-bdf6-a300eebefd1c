import React from "react";
import { <PERSON> } from "@tanstack/react-router";
import { useAuth } from "@/lib/auth-context";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Home,
  FileText,
  Users,
  Building,
  LogOut,
  User,
  PlusCircle,
  List,
} from "lucide-react";

export function Navigation() {
  const { user, logout } = useAuth();

  if (!user) return null;

  const getNavigationItems = () => {
    switch (user.role) {
      case "Admin":
        return [
          { href: "/admin", label: "Dashboard", icon: Home },
          { href: "/admin/users", label: "Users", icon: Users },
          { href: "/admin/departments", label: "Departments", icon: Building },
          { href: "/admin/contracts", label: "All Contracts", icon: FileText },
        ];

      case "Appraiser":
        return [
          { href: "/appraiser", label: "Dashboard", icon: Home },
          {
            href: "/appraiser/contracts/create",
            label: "Create Contract",
            icon: PlusCircle,
          },
          {
            href: "/appraiser/contracts/created",
            label: "Contracts I Created",
            icon: List,
          },
          {
            href: "/appraiser/contracts/my",
            label: "My Contracts",
            icon: FileText,
          },
        ];

      case "Appraisee":
        return [
          { href: "/appraisee", label: "Dashboard", icon: Home },
          {
            href: "/appraisee/contracts",
            label: "My Contracts",
            icon: FileText,
          },
        ];

      default:
        return [{ href: "/", label: "Dashboard", icon: Home }];
    }
  };

  const navigationItems = getNavigationItems();

  return (
    <nav className="bg-netone-white border-b border-netone-grey shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Logo and main navigation */}
          <div className="flex items-center space-x-8">
            {/* Logo */}
            <Link to="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-netone-orange rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-sm">N</span>
              </div>
              <span className="text-xl font-bold text-netone-black">IRBM</span>
            </Link>

            {/* Navigation Links */}
            <div className="hidden md:flex space-x-6">
              {navigationItems.map((item) => {
                const Icon = item.icon;
                return (
                  <Link
                    key={item.href}
                    to={item.href as any}
                    className="flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-netone-orange hover:bg-netone-grey transition-colors"
                    activeProps={{
                      className: "text-netone-orange bg-netone-grey",
                    }}>
                    <Icon className="w-4 h-4" />
                    <span>{item.label}</span>
                  </Link>
                );
              })}
            </div>
          </div>

          {/* User menu */}
          <div className="flex items-center space-x-4">
            <span className="text-sm text-gray-600 hidden sm:block">
              Welcome, {user.name}
            </span>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="relative h-8 w-8 rounded-full">
                  <div className="w-8 h-8 bg-netone-orange rounded-full flex items-center justify-center">
                    <User className="w-4 h-4 text-white" />
                  </div>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <div className="flex flex-col space-y-1 p-2">
                  <p className="text-sm font-medium leading-none">
                    {user.name}
                  </p>
                  <p className="text-xs leading-none text-muted-foreground">
                    {user.email}
                  </p>
                  <p className="text-xs leading-none text-muted-foreground">
                    {user.role} • Grade {user.grade}
                  </p>
                </div>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={logout} className="cursor-pointer">
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Log out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Mobile navigation */}
        <div className="md:hidden pb-3">
          <div className="flex flex-wrap gap-2">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.href}
                  to={item.href as any}
                  className="flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-netone-orange hover:bg-netone-grey transition-colors"
                  activeProps={{
                    className: "text-netone-orange bg-netone-grey",
                  }}>
                  <Icon className="w-4 h-4" />
                  <span>{item.label}</span>
                </Link>
              );
            })}
          </div>
        </div>
      </div>
    </nav>
  );
}

import {
  invariant,
  isNotFound,
  isPlainObject,
  isRedirect
} from "./chunk-YHFLSJAU.js";

// node_modules/cookie-es/dist/index.mjs
function splitSetCookieString(cookiesString) {
  if (Array.isArray(cookiesString)) {
    return cookiesString.flatMap((c) => splitSetCookieString(c));
  }
  if (typeof cookiesString !== "string") {
    return [];
  }
  const cookiesStrings = [];
  let pos = 0;
  let start;
  let ch;
  let lastComma;
  let nextStart;
  let cookiesSeparatorFound;
  const skipWhitespace = () => {
    while (pos < cookiesString.length && /\s/.test(cookiesString.charAt(pos))) {
      pos += 1;
    }
    return pos < cookiesString.length;
  };
  const notSpecialChar = () => {
    ch = cookiesString.charAt(pos);
    return ch !== "=" && ch !== ";" && ch !== ",";
  };
  while (pos < cookiesString.length) {
    start = pos;
    cookiesSeparatorFound = false;
    while (skipWhitespace()) {
      ch = cookiesString.charAt(pos);
      if (ch === ",") {
        lastComma = pos;
        pos += 1;
        skipWhitespace();
        nextStart = pos;
        while (pos < cookiesString.length && notSpecialChar()) {
          pos += 1;
        }
        if (pos < cookiesString.length && cookiesString.charAt(pos) === "=") {
          cookiesSeparatorFound = true;
          pos = nextStart;
          cookiesStrings.push(cookiesString.slice(start, lastComma));
          start = pos;
        } else {
          pos = lastComma + 1;
        }
      } else {
        pos += 1;
      }
    }
    if (!cookiesSeparatorFound || pos >= cookiesString.length) {
      cookiesStrings.push(cookiesString.slice(start, cookiesString.length));
    }
  }
  return cookiesStrings;
}

// node_modules/@tanstack/start-client-core/dist/esm/headers.js
function toHeadersInstance(init) {
  if (init instanceof Headers) {
    return new Headers(init);
  } else if (Array.isArray(init)) {
    return new Headers(init);
  } else if (typeof init === "object") {
    return new Headers(init);
  } else {
    return new Headers();
  }
}
function mergeHeaders(...headers) {
  return headers.reduce((acc, header) => {
    const headersInstance = toHeadersInstance(header);
    for (const [key, value] of headersInstance.entries()) {
      if (key === "set-cookie") {
        const splitCookies = splitSetCookieString(value);
        splitCookies.forEach((cookie) => acc.append("set-cookie", cookie));
      } else {
        acc.set(key, value);
      }
    }
    return acc;
  }, new Headers());
}

// node_modules/@tanstack/start-client-core/dist/esm/serializer.js
var startSerializer = {
  stringify: (value) => JSON.stringify(value, function replacer(key, val) {
    const ogVal = this[key];
    const serializer = serializers.find((t) => t.stringifyCondition(ogVal));
    if (serializer) {
      return serializer.stringify(ogVal);
    }
    return val;
  }),
  parse: (value) => JSON.parse(value, function parser(key, val) {
    const ogVal = this[key];
    if (isPlainObject(ogVal)) {
      const serializer = serializers.find((t) => t.parseCondition(ogVal));
      if (serializer) {
        return serializer.parse(ogVal);
      }
    }
    return val;
  }),
  encode: (value) => {
    if (Array.isArray(value)) {
      return value.map((v) => startSerializer.encode(v));
    }
    if (isPlainObject(value)) {
      return Object.fromEntries(
        Object.entries(value).map(([key, v]) => [
          key,
          startSerializer.encode(v)
        ])
      );
    }
    const serializer = serializers.find((t) => t.stringifyCondition(value));
    if (serializer) {
      return serializer.stringify(value);
    }
    return value;
  },
  decode: (value) => {
    if (isPlainObject(value)) {
      const serializer = serializers.find((t) => t.parseCondition(value));
      if (serializer) {
        return serializer.parse(value);
      }
    }
    if (Array.isArray(value)) {
      return value.map((v) => startSerializer.decode(v));
    }
    if (isPlainObject(value)) {
      return Object.fromEntries(
        Object.entries(value).map(([key, v]) => [
          key,
          startSerializer.decode(v)
        ])
      );
    }
    return value;
  }
};
var createSerializer = (key, check, toValue, fromValue) => ({
  key,
  stringifyCondition: check,
  stringify: (value) => ({ [`$${key}`]: toValue(value) }),
  parseCondition: (value) => Object.hasOwn(value, `$${key}`),
  parse: (value) => fromValue(value[`$${key}`])
});
var serializers = [
  createSerializer(
    // Key
    "undefined",
    // Check
    (v) => v === void 0,
    // To
    () => 0,
    // From
    () => void 0
  ),
  createSerializer(
    // Key
    "date",
    // Check
    (v) => v instanceof Date,
    // To
    (v) => v.toISOString(),
    // From
    (v) => new Date(v)
  ),
  createSerializer(
    // Key
    "error",
    // Check
    (v) => v instanceof Error,
    // To
    (v) => ({
      ...v,
      message: v.message,
      stack: true ? v.stack : void 0,
      cause: v.cause
    }),
    // From
    (v) => Object.assign(new Error(v.message), v)
  ),
  createSerializer(
    // Key
    "formData",
    // Check
    (v) => v instanceof FormData,
    // To
    (v) => {
      const entries = {};
      v.forEach((value, key) => {
        const entry = entries[key];
        if (entry !== void 0) {
          if (Array.isArray(entry)) {
            entry.push(value);
          } else {
            entries[key] = [entry, value];
          }
        } else {
          entries[key] = value;
        }
      });
      return entries;
    },
    // From
    (v) => {
      const formData = new FormData();
      Object.entries(v).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          value.forEach((val) => formData.append(key, val));
        } else {
          formData.append(key, value);
        }
      });
      return formData;
    }
  ),
  createSerializer(
    // Key
    "bigint",
    // Check
    (v) => typeof v === "bigint",
    // To
    (v) => v.toString(),
    // From
    (v) => BigInt(v)
  )
];

// node_modules/@tanstack/start-client-core/dist/esm/createIsomorphicFn.js
function createIsomorphicFn() {
  return null;
}

// node_modules/@tanstack/start-client-core/dist/esm/envOnly.js
var serverOnly = (fn) => fn;
var clientOnly = (fn) => fn;

// node_modules/@tanstack/start-client-core/dist/esm/registerGlobalMiddleware.js
var globalMiddleware = [];
function registerGlobalMiddleware(options) {
  globalMiddleware.push(...options.middleware);
}

// node_modules/@tanstack/start-client-core/dist/esm/createServerFn.js
function createServerFn(options, __opts) {
  const resolvedOptions = __opts || options || {};
  if (typeof resolvedOptions.method === "undefined") {
    resolvedOptions.method = "GET";
  }
  return {
    options: resolvedOptions,
    middleware: (middleware) => {
      return createServerFn(void 0, Object.assign(resolvedOptions, { middleware }));
    },
    validator: (validator) => {
      return createServerFn(void 0, Object.assign(resolvedOptions, { validator }));
    },
    type: (type) => {
      return createServerFn(void 0, Object.assign(resolvedOptions, { type }));
    },
    handler: (...args) => {
      const [extractedFn, serverFn] = args;
      Object.assign(resolvedOptions, {
        ...extractedFn,
        extractedFn,
        serverFn
      });
      const resolvedMiddleware = [
        ...resolvedOptions.middleware || [],
        serverFnBaseToMiddleware(resolvedOptions)
      ];
      return Object.assign(
        async (opts) => {
          return executeMiddleware(resolvedMiddleware, "client", {
            ...extractedFn,
            ...resolvedOptions,
            data: opts == null ? void 0 : opts.data,
            headers: opts == null ? void 0 : opts.headers,
            signal: opts == null ? void 0 : opts.signal,
            context: {}
          }).then((d) => {
            if (resolvedOptions.response === "full") {
              return d;
            }
            if (d.error) throw d.error;
            return d.result;
          });
        },
        {
          // This copies over the URL, function ID
          ...extractedFn,
          // The extracted function on the server-side calls
          // this function
          __executeServer: async (opts_, signal) => {
            const opts = opts_ instanceof FormData ? extractFormDataContext(opts_) : opts_;
            opts.type = typeof resolvedOptions.type === "function" ? resolvedOptions.type(opts) : resolvedOptions.type;
            const ctx = {
              ...extractedFn,
              ...opts,
              signal
            };
            const run = () => executeMiddleware(resolvedMiddleware, "server", ctx).then(
              (d) => ({
                // Only send the result and sendContext back to the client
                result: d.result,
                error: d.error,
                context: d.sendContext
              })
            );
            if (ctx.type === "static") {
              let response;
              if (serverFnStaticCache == null ? void 0 : serverFnStaticCache.getItem) {
                response = await serverFnStaticCache.getItem(ctx);
              }
              if (!response) {
                response = await run().then((d) => {
                  return {
                    ctx: d,
                    error: null
                  };
                }).catch((e) => {
                  return {
                    ctx: void 0,
                    error: e
                  };
                });
                if (serverFnStaticCache == null ? void 0 : serverFnStaticCache.setItem) {
                  await serverFnStaticCache.setItem(ctx, response);
                }
              }
              invariant(
                response,
                "No response from both server and static cache!"
              );
              if (response.error) {
                throw response.error;
              }
              return response.ctx;
            }
            return run();
          }
        }
      );
    }
  };
}
async function executeMiddleware(middlewares, env, opts) {
  const flattenedMiddlewares = flattenMiddlewares([
    ...globalMiddleware,
    ...middlewares
  ]);
  const next = async (ctx) => {
    const nextMiddleware = flattenedMiddlewares.shift();
    if (!nextMiddleware) {
      return ctx;
    }
    if (nextMiddleware.options.validator && (env === "client" ? nextMiddleware.options.validateClient : true)) {
      ctx.data = await execValidator(nextMiddleware.options.validator, ctx.data);
    }
    const middlewareFn = env === "client" ? nextMiddleware.options.client : nextMiddleware.options.server;
    if (middlewareFn) {
      return applyMiddleware(middlewareFn, ctx, async (newCtx) => {
        return next(newCtx).catch((error) => {
          if (isRedirect(error) || isNotFound(error)) {
            return {
              ...newCtx,
              error
            };
          }
          throw error;
        });
      });
    }
    return next(ctx);
  };
  return next({
    ...opts,
    headers: opts.headers || {},
    sendContext: opts.sendContext || {},
    context: opts.context || {}
  });
}
var serverFnStaticCache;
function setServerFnStaticCache(cache) {
  const previousCache = serverFnStaticCache;
  serverFnStaticCache = typeof cache === "function" ? cache() : cache;
  return () => {
    serverFnStaticCache = previousCache;
  };
}
function createServerFnStaticCache(serverFnStaticCache2) {
  return serverFnStaticCache2;
}
async function sha1Hash(message) {
  const msgBuffer = new TextEncoder().encode(message);
  const hashBuffer = await crypto.subtle.digest("SHA-1", msgBuffer);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  const hashHex = hashArray.map((b) => b.toString(16).padStart(2, "0")).join("");
  return hashHex;
}
setServerFnStaticCache(() => {
  const getStaticCacheUrl = async (options, hash) => {
    const filename = await sha1Hash(`${options.functionId}__${hash}`);
    return `/__tsr/staticServerFnCache/${filename}.json`;
  };
  const jsonToFilenameSafeString = (json2) => {
    const sortedKeysReplacer = (key, value) => value && typeof value === "object" && !Array.isArray(value) ? Object.keys(value).sort().reduce((acc, curr) => {
      acc[curr] = value[curr];
      return acc;
    }, {}) : value;
    const jsonString = JSON.stringify(json2 ?? "", sortedKeysReplacer);
    return jsonString.replace(/[/\\?%*:|"<>]/g, "-").replace(/\s+/g, "_");
  };
  const staticClientCache = typeof document !== "undefined" ? /* @__PURE__ */ new Map() : null;
  return createServerFnStaticCache({
    getItem: async (ctx) => {
      if (typeof document === "undefined") {
        const hash = jsonToFilenameSafeString(ctx.data);
        const url = await getStaticCacheUrl(ctx, hash);
        const publicUrl = process.env.TSS_OUTPUT_PUBLIC_DIR;
        const { promises: fs } = await import("./node_fs-5PARDPNU.js");
        const path = await import("./node_path-JJO4RSS7.js");
        const filePath = path.join(publicUrl, url);
        const [cachedResult, readError] = await fs.readFile(filePath, "utf-8").then((c) => [
          startSerializer.parse(c),
          null
        ]).catch((e) => [null, e]);
        if (readError && readError.code !== "ENOENT") {
          throw readError;
        }
        return cachedResult;
      }
      return void 0;
    },
    setItem: async (ctx, response) => {
      const { promises: fs } = await import("./node_fs-5PARDPNU.js");
      const path = await import("./node_path-JJO4RSS7.js");
      const hash = jsonToFilenameSafeString(ctx.data);
      const url = await getStaticCacheUrl(ctx, hash);
      const publicUrl = process.env.TSS_OUTPUT_PUBLIC_DIR;
      const filePath = path.join(publicUrl, url);
      await fs.mkdir(path.dirname(filePath), { recursive: true });
      await fs.writeFile(filePath, startSerializer.stringify(response));
    },
    fetchItem: async (ctx) => {
      const hash = jsonToFilenameSafeString(ctx.data);
      const url = await getStaticCacheUrl(ctx, hash);
      let result = staticClientCache == null ? void 0 : staticClientCache.get(url);
      if (!result) {
        result = await fetch(url, {
          method: "GET"
        }).then((r) => r.text()).then((d) => startSerializer.parse(d));
        staticClientCache == null ? void 0 : staticClientCache.set(url, result);
      }
      return result;
    }
  });
});
function extractFormDataContext(formData) {
  const serializedContext = formData.get("__TSR_CONTEXT");
  formData.delete("__TSR_CONTEXT");
  if (typeof serializedContext !== "string") {
    return {
      context: {},
      data: formData
    };
  }
  try {
    const context = startSerializer.parse(serializedContext);
    return {
      context,
      data: formData
    };
  } catch {
    return {
      data: formData
    };
  }
}
function flattenMiddlewares(middlewares) {
  const seen = /* @__PURE__ */ new Set();
  const flattened = [];
  const recurse = (middleware) => {
    middleware.forEach((m) => {
      if (m.options.middleware) {
        recurse(m.options.middleware);
      }
      if (!seen.has(m)) {
        seen.add(m);
        flattened.push(m);
      }
    });
  };
  recurse(middlewares);
  return flattened;
}
var applyMiddleware = async (middlewareFn, ctx, nextFn) => {
  return middlewareFn({
    ...ctx,
    next: async (userCtx = {}) => {
      return nextFn({
        ...ctx,
        ...userCtx,
        context: {
          ...ctx.context,
          ...userCtx.context
        },
        sendContext: {
          ...ctx.sendContext,
          ...userCtx.sendContext ?? {}
        },
        headers: mergeHeaders(ctx.headers, userCtx.headers),
        result: userCtx.result !== void 0 ? userCtx.result : ctx.response === "raw" ? userCtx : ctx.result,
        error: userCtx.error ?? ctx.error
      });
    }
  });
};
function execValidator(validator, input) {
  if (validator == null) return {};
  if ("~standard" in validator) {
    const result = validator["~standard"].validate(input);
    if (result instanceof Promise)
      throw new Error("Async validation not supported");
    if (result.issues)
      throw new Error(JSON.stringify(result.issues, void 0, 2));
    return result.value;
  }
  if ("parse" in validator) {
    return validator.parse(input);
  }
  if (typeof validator === "function") {
    return validator(input);
  }
  throw new Error("Invalid validator type!");
}
function serverFnBaseToMiddleware(options) {
  return {
    _types: void 0,
    options: {
      validator: options.validator,
      validateClient: options.validateClient,
      client: async ({ next, sendContext, ...ctx }) => {
        var _a;
        const payload = {
          ...ctx,
          // switch the sendContext over to context
          context: sendContext,
          type: typeof ctx.type === "function" ? ctx.type(ctx) : ctx.type
        };
        if (ctx.type === "static" && false) {
          invariant(
            serverFnStaticCache,
            "serverFnStaticCache.fetchItem is not available!"
          );
          const result = await serverFnStaticCache.fetchItem(payload);
          if (result) {
            if (result.error) {
              throw result.error;
            }
            return next(result.ctx);
          }
          tiny_warning_esm_default(
            result,
            `No static cache item found for ${payload.functionId}__${JSON.stringify(payload.data)}, falling back to server function...`
          );
        }
        const res = await ((_a = options.extractedFn) == null ? void 0 : _a.call(options, payload));
        return next(res);
      },
      server: async ({ next, ...ctx }) => {
        var _a;
        const result = await ((_a = options.serverFn) == null ? void 0 : _a.call(options, ctx));
        return next({
          ...ctx,
          result
        });
      }
    }
  };
}

// node_modules/@tanstack/start-client-core/dist/esm/json.js
function json(payload, init) {
  return new Response(JSON.stringify(payload), {
    ...init,
    headers: mergeHeaders(
      { "content-type": "application/json" },
      init == null ? void 0 : init.headers
    )
  });
}

// node_modules/@tanstack/start-client-core/dist/esm/createMiddleware.js
function createMiddleware(options, __opts) {
  const resolvedOptions = __opts || (options || {});
  return {
    options: resolvedOptions,
    middleware: (middleware) => {
      return createMiddleware(
        void 0,
        Object.assign(resolvedOptions, { middleware })
      );
    },
    validator: (validator) => {
      return createMiddleware(
        void 0,
        Object.assign(resolvedOptions, { validator })
      );
    },
    client: (client) => {
      return createMiddleware(
        void 0,
        Object.assign(resolvedOptions, { client })
      );
    },
    server: (server) => {
      return createMiddleware(
        void 0,
        Object.assign(resolvedOptions, { server })
      );
    }
  };
}

// node_modules/@tanstack/start-client-core/dist/esm/ssr-client.js
function hydrate(router) {
  var _a, _b, _c;
  invariant(
    (_a = window.__TSR_SSR__) == null ? void 0 : _a.dehydrated,
    "Expected to find a dehydrated data on window.__TSR_SSR__.dehydrated... but we did not. Please file an issue!"
  );
  const { manifest, dehydratedData } = startSerializer.parse(
    window.__TSR_SSR__.dehydrated
  );
  router.ssr = {
    manifest,
    serializer: startSerializer
  };
  router.clientSsr = {
    getStreamedValue: (key) => {
      var _a2;
      if (router.isServer) {
        return void 0;
      }
      const streamedValue = (_a2 = window.__TSR_SSR__) == null ? void 0 : _a2.streamedValues[key];
      if (!streamedValue) {
        return;
      }
      if (!streamedValue.parsed) {
        streamedValue.parsed = router.ssr.serializer.parse(streamedValue.value);
      }
      return streamedValue.parsed;
    }
  };
  const matches = router.matchRoutes(router.state.location);
  const routeChunkPromise = Promise.all(
    matches.map((match) => {
      const route = router.looseRoutesById[match.routeId];
      return router.loadRouteChunk(route);
    })
  );
  matches.forEach((match) => {
    var _a2;
    const dehydratedMatch = window.__TSR_SSR__.matches.find(
      (d) => d.id === match.id
    );
    if (dehydratedMatch) {
      Object.assign(match, dehydratedMatch);
      if (dehydratedMatch.__beforeLoadContext) {
        match.__beforeLoadContext = router.ssr.serializer.parse(
          dehydratedMatch.__beforeLoadContext
        );
      }
      if (dehydratedMatch.loaderData) {
        match.loaderData = router.ssr.serializer.parse(
          dehydratedMatch.loaderData
        );
      }
      if (dehydratedMatch.error) {
        match.error = router.ssr.serializer.parse(dehydratedMatch.error);
      }
      (_a2 = match.extracted) == null ? void 0 : _a2.forEach((ex) => {
        deepMutableSetByPath(match, ["loaderData", ...ex.path], ex.value);
      });
    } else {
      Object.assign(match, {
        status: "success",
        updatedAt: Date.now()
      });
    }
    return match;
  });
  router.__store.setState((s) => {
    return {
      ...s,
      matches
    };
  });
  (_c = (_b = router.options).hydrate) == null ? void 0 : _c.call(_b, dehydratedData);
  router.state.matches.forEach((match) => {
    var _a2, _b2, _c2, _d, _e, _f;
    const route = router.looseRoutesById[match.routeId];
    const parentMatch = router.state.matches[match.index - 1];
    const parentContext = (parentMatch == null ? void 0 : parentMatch.context) ?? router.options.context ?? {};
    const contextFnContext = {
      deps: match.loaderDeps,
      params: match.params,
      context: parentContext,
      location: router.state.location,
      navigate: (opts) => router.navigate({ ...opts, _fromLocation: router.state.location }),
      buildLocation: router.buildLocation,
      cause: match.cause,
      abortController: match.abortController,
      preload: false,
      matches
    };
    match.__routeContext = ((_b2 = (_a2 = route.options).context) == null ? void 0 : _b2.call(_a2, contextFnContext)) ?? {};
    match.context = {
      ...parentContext,
      ...match.__routeContext,
      ...match.__beforeLoadContext
    };
    const assetContext = {
      matches: router.state.matches,
      match,
      params: match.params,
      loaderData: match.loaderData
    };
    const headFnContent = (_d = (_c2 = route.options).head) == null ? void 0 : _d.call(_c2, assetContext);
    const scripts = (_f = (_e = route.options).scripts) == null ? void 0 : _f.call(_e, assetContext);
    match.meta = headFnContent == null ? void 0 : headFnContent.meta;
    match.links = headFnContent == null ? void 0 : headFnContent.links;
    match.headScripts = headFnContent == null ? void 0 : headFnContent.scripts;
    match.scripts = scripts;
  });
  return routeChunkPromise;
}
function deepMutableSetByPath(obj, path, value) {
  if (path.length === 1) {
    obj[path[0]] = value;
  }
  const [key, ...rest] = path;
  if (Array.isArray(obj)) {
    deepMutableSetByPath(obj[Number(key)], rest, value);
  } else if (isPlainObject(obj)) {
    deepMutableSetByPath(obj[key], rest, value);
  }
}

export {
  mergeHeaders,
  startSerializer,
  hydrate,
  createIsomorphicFn,
  serverOnly,
  clientOnly,
  globalMiddleware,
  registerGlobalMiddleware,
  createServerFn,
  json,
  createMiddleware
};
//# sourceMappingURL=chunk-ZZNDHO4S.js.map

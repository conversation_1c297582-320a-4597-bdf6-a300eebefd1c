{"version": 3, "sources": ["../../../../cookie-es/dist/index.mjs", "../../../../@tanstack/start-client-core/src/headers.ts", "../../../../@tanstack/start-client-core/src/serializer.ts", "../../../../@tanstack/start-client-core/src/createIsomorphicFn.ts", "../../../../@tanstack/start-client-core/src/envOnly.ts", "../../../../@tanstack/start-client-core/src/registerGlobalMiddleware.ts", "../../../../@tanstack/start-client-core/src/createServerFn.ts", "../../../../@tanstack/start-client-core/src/json.ts", "../../../../@tanstack/start-client-core/src/createMiddleware.ts", "../../../../@tanstack/start-client-core/src/ssr-client.tsx"], "sourcesContent": ["function parse(str, options) {\n  if (typeof str !== \"string\") {\n    throw new TypeError(\"argument str must be a string\");\n  }\n  const obj = {};\n  const opt = options || {};\n  const dec = opt.decode || decode;\n  let index = 0;\n  while (index < str.length) {\n    const eqIdx = str.indexOf(\"=\", index);\n    if (eqIdx === -1) {\n      break;\n    }\n    let endIdx = str.indexOf(\";\", index);\n    if (endIdx === -1) {\n      endIdx = str.length;\n    } else if (endIdx < eqIdx) {\n      index = str.lastIndexOf(\";\", eqIdx - 1) + 1;\n      continue;\n    }\n    const key = str.slice(index, eqIdx).trim();\n    if (opt?.filter && !opt?.filter(key)) {\n      index = endIdx + 1;\n      continue;\n    }\n    if (void 0 === obj[key]) {\n      let val = str.slice(eqIdx + 1, endIdx).trim();\n      if (val.codePointAt(0) === 34) {\n        val = val.slice(1, -1);\n      }\n      obj[key] = tryDecode(val, dec);\n    }\n    index = endIdx + 1;\n  }\n  return obj;\n}\nfunction decode(str) {\n  return str.includes(\"%\") ? decodeURIComponent(str) : str;\n}\nfunction tryDecode(str, decode2) {\n  try {\n    return decode2(str);\n  } catch {\n    return str;\n  }\n}\n\nconst fieldContentRegExp = /^[\\u0009\\u0020-\\u007E\\u0080-\\u00FF]+$/;\nfunction serialize(name, value, options) {\n  const opt = options || {};\n  const enc = opt.encode || encodeURIComponent;\n  if (typeof enc !== \"function\") {\n    throw new TypeError(\"option encode is invalid\");\n  }\n  if (!fieldContentRegExp.test(name)) {\n    throw new TypeError(\"argument name is invalid\");\n  }\n  const encodedValue = enc(value);\n  if (encodedValue && !fieldContentRegExp.test(encodedValue)) {\n    throw new TypeError(\"argument val is invalid\");\n  }\n  let str = name + \"=\" + encodedValue;\n  if (void 0 !== opt.maxAge && opt.maxAge !== null) {\n    const maxAge = opt.maxAge - 0;\n    if (Number.isNaN(maxAge) || !Number.isFinite(maxAge)) {\n      throw new TypeError(\"option maxAge is invalid\");\n    }\n    str += \"; Max-Age=\" + Math.floor(maxAge);\n  }\n  if (opt.domain) {\n    if (!fieldContentRegExp.test(opt.domain)) {\n      throw new TypeError(\"option domain is invalid\");\n    }\n    str += \"; Domain=\" + opt.domain;\n  }\n  if (opt.path) {\n    if (!fieldContentRegExp.test(opt.path)) {\n      throw new TypeError(\"option path is invalid\");\n    }\n    str += \"; Path=\" + opt.path;\n  }\n  if (opt.expires) {\n    if (!isDate(opt.expires) || Number.isNaN(opt.expires.valueOf())) {\n      throw new TypeError(\"option expires is invalid\");\n    }\n    str += \"; Expires=\" + opt.expires.toUTCString();\n  }\n  if (opt.httpOnly) {\n    str += \"; HttpOnly\";\n  }\n  if (opt.secure) {\n    str += \"; Secure\";\n  }\n  if (opt.priority) {\n    const priority = typeof opt.priority === \"string\" ? opt.priority.toLowerCase() : opt.priority;\n    switch (priority) {\n      case \"low\": {\n        str += \"; Priority=Low\";\n        break;\n      }\n      case \"medium\": {\n        str += \"; Priority=Medium\";\n        break;\n      }\n      case \"high\": {\n        str += \"; Priority=High\";\n        break;\n      }\n      default: {\n        throw new TypeError(\"option priority is invalid\");\n      }\n    }\n  }\n  if (opt.sameSite) {\n    const sameSite = typeof opt.sameSite === \"string\" ? opt.sameSite.toLowerCase() : opt.sameSite;\n    switch (sameSite) {\n      case true: {\n        str += \"; SameSite=Strict\";\n        break;\n      }\n      case \"lax\": {\n        str += \"; SameSite=Lax\";\n        break;\n      }\n      case \"strict\": {\n        str += \"; SameSite=Strict\";\n        break;\n      }\n      case \"none\": {\n        str += \"; SameSite=None\";\n        break;\n      }\n      default: {\n        throw new TypeError(\"option sameSite is invalid\");\n      }\n    }\n  }\n  if (opt.partitioned) {\n    str += \"; Partitioned\";\n  }\n  return str;\n}\nfunction isDate(val) {\n  return Object.prototype.toString.call(val) === \"[object Date]\" || val instanceof Date;\n}\n\nfunction parseSetCookie(setCookieValue, options) {\n  const parts = (setCookieValue || \"\").split(\";\").filter((str) => typeof str === \"string\" && !!str.trim());\n  const nameValuePairStr = parts.shift() || \"\";\n  const parsed = _parseNameValuePair(nameValuePairStr);\n  const name = parsed.name;\n  let value = parsed.value;\n  try {\n    value = options?.decode === false ? value : (options?.decode || decodeURIComponent)(value);\n  } catch {\n  }\n  const cookie = {\n    name,\n    value\n  };\n  for (const part of parts) {\n    const sides = part.split(\"=\");\n    const partKey = (sides.shift() || \"\").trimStart().toLowerCase();\n    const partValue = sides.join(\"=\");\n    switch (partKey) {\n      case \"expires\": {\n        cookie.expires = new Date(partValue);\n        break;\n      }\n      case \"max-age\": {\n        cookie.maxAge = Number.parseInt(partValue, 10);\n        break;\n      }\n      case \"secure\": {\n        cookie.secure = true;\n        break;\n      }\n      case \"httponly\": {\n        cookie.httpOnly = true;\n        break;\n      }\n      case \"samesite\": {\n        cookie.sameSite = partValue;\n        break;\n      }\n      default: {\n        cookie[partKey] = partValue;\n      }\n    }\n  }\n  return cookie;\n}\nfunction _parseNameValuePair(nameValuePairStr) {\n  let name = \"\";\n  let value = \"\";\n  const nameValueArr = nameValuePairStr.split(\"=\");\n  if (nameValueArr.length > 1) {\n    name = nameValueArr.shift();\n    value = nameValueArr.join(\"=\");\n  } else {\n    value = nameValuePairStr;\n  }\n  return { name, value };\n}\n\nfunction splitSetCookieString(cookiesString) {\n  if (Array.isArray(cookiesString)) {\n    return cookiesString.flatMap((c) => splitSetCookieString(c));\n  }\n  if (typeof cookiesString !== \"string\") {\n    return [];\n  }\n  const cookiesStrings = [];\n  let pos = 0;\n  let start;\n  let ch;\n  let lastComma;\n  let nextStart;\n  let cookiesSeparatorFound;\n  const skipWhitespace = () => {\n    while (pos < cookiesString.length && /\\s/.test(cookiesString.charAt(pos))) {\n      pos += 1;\n    }\n    return pos < cookiesString.length;\n  };\n  const notSpecialChar = () => {\n    ch = cookiesString.charAt(pos);\n    return ch !== \"=\" && ch !== \";\" && ch !== \",\";\n  };\n  while (pos < cookiesString.length) {\n    start = pos;\n    cookiesSeparatorFound = false;\n    while (skipWhitespace()) {\n      ch = cookiesString.charAt(pos);\n      if (ch === \",\") {\n        lastComma = pos;\n        pos += 1;\n        skipWhitespace();\n        nextStart = pos;\n        while (pos < cookiesString.length && notSpecialChar()) {\n          pos += 1;\n        }\n        if (pos < cookiesString.length && cookiesString.charAt(pos) === \"=\") {\n          cookiesSeparatorFound = true;\n          pos = nextStart;\n          cookiesStrings.push(cookiesString.slice(start, lastComma));\n          start = pos;\n        } else {\n          pos = lastComma + 1;\n        }\n      } else {\n        pos += 1;\n      }\n    }\n    if (!cookiesSeparatorFound || pos >= cookiesString.length) {\n      cookiesStrings.push(cookiesString.slice(start, cookiesString.length));\n    }\n  }\n  return cookiesStrings;\n}\n\nexport { parse, parseSetCookie, serialize, splitSetCookieString };\n", "import { splitSetCookieString } from 'cookie-es'\nimport type { OutgoingHttpHeaders } from 'node:http2'\n// A utility function to turn HeadersInit into an object\nexport function headersInitToObject(\n  headers: HeadersInit,\n): Record<keyof OutgoingHttpHeaders, string> {\n  const obj: Record<keyof OutgoingHttpHeaders, string> = {}\n  const headersInstance = new Headers(headers)\n  for (const [key, value] of headersInstance.entries()) {\n    obj[key] = value\n  }\n  return obj\n}\n\ntype AnyHeaders =\n  | Headers\n  | HeadersInit\n  | Record<string, string>\n  | Array<[string, string]>\n  | OutgoingHttpHeaders\n  | undefined\n\n// Helper function to convert various HeaderInit types to a Headers instance\nfunction toHeadersInstance(init: AnyHeaders) {\n  if (init instanceof Headers) {\n    return new Headers(init)\n  } else if (Array.isArray(init)) {\n    return new Headers(init)\n  } else if (typeof init === 'object') {\n    return new Headers(init as HeadersInit)\n  } else {\n    return new Headers()\n  }\n}\n\n// Function to merge headers with proper overrides\nexport function mergeHeaders(...headers: Array<AnyHeaders>) {\n  return headers.reduce((acc: Headers, header) => {\n    const headersInstance = toHeadersInstance(header)\n    for (const [key, value] of headersInstance.entries()) {\n      if (key === 'set-cookie') {\n        const splitCookies = splitSetCookieString(value)\n        splitCookies.forEach((cookie) => acc.append('set-cookie', cookie))\n      } else {\n        acc.set(key, value)\n      }\n    }\n    return acc\n  }, new Headers())\n}\n", "import { isPlainObject } from '@tanstack/router-core'\nimport type { StartSerializer } from '@tanstack/router-core'\n\nexport const startSerializer: StartSerializer = {\n  stringify: (value: any) =>\n    JSON.stringify(value, function replacer(key, val) {\n      const ogVal = this[key]\n      const serializer = serializers.find((t) => t.stringifyCondition(ogVal))\n\n      if (serializer) {\n        return serializer.stringify(ogVal)\n      }\n\n      return val\n    }),\n  parse: (value: string) =>\n    JSON.parse(value, function parser(key, val) {\n      const ogVal = this[key]\n      if (isPlainObject(ogVal)) {\n        const serializer = serializers.find((t) => t.parseCondition(ogVal))\n\n        if (serializer) {\n          return serializer.parse(ogVal)\n        }\n      }\n\n      return val\n    }),\n  encode: (value: any) => {\n    // When encoding, dive first\n    if (Array.isArray(value)) {\n      return value.map((v) => startSerializer.encode(v))\n    }\n\n    if (isPlainObject(value)) {\n      return Object.fromEntries(\n        Object.entries(value).map(([key, v]) => [\n          key,\n          startSerializer.encode(v),\n        ]),\n      )\n    }\n\n    const serializer = serializers.find((t) => t.stringifyCondition(value))\n    if (serializer) {\n      return serializer.stringify(value)\n    }\n\n    return value\n  },\n  decode: (value: any) => {\n    // Attempt transform first\n    if (isPlainObject(value)) {\n      const serializer = serializers.find((t) => t.parseCondition(value))\n      if (serializer) {\n        return serializer.parse(value)\n      }\n    }\n\n    if (Array.isArray(value)) {\n      return value.map((v) => startSerializer.decode(v))\n    }\n\n    if (isPlainObject(value)) {\n      return Object.fromEntries(\n        Object.entries(value).map(([key, v]) => [\n          key,\n          startSerializer.decode(v),\n        ]),\n      )\n    }\n\n    return value\n  },\n}\n\nconst createSerializer = <TKey extends string, TInput, TSerialized>(\n  key: TKey,\n  check: (value: any) => value is TInput,\n  toValue: (value: TInput) => TSerialized,\n  fromValue: (value: TSerialized) => TInput,\n) => ({\n  key,\n  stringifyCondition: check,\n  stringify: (value: any) => ({ [`$${key}`]: toValue(value) }),\n  parseCondition: (value: any) => Object.hasOwn(value, `$${key}`),\n  parse: (value: any) => fromValue(value[`$${key}`]),\n})\n\n// Keep these ordered by predicted frequency\n// Make sure to keep DefaultSerializable in sync with these serializers\n// Also, make sure that they are unit tested in serializer.test.tsx\nconst serializers = [\n  createSerializer(\n    // Key\n    'undefined',\n    // Check\n    (v): v is undefined => v === undefined,\n    // To\n    () => 0,\n    // From\n    () => undefined,\n  ),\n  createSerializer(\n    // Key\n    'date',\n    // Check\n    (v): v is Date => v instanceof Date,\n    // To\n    (v) => v.toISOString(),\n    // From\n    (v) => new Date(v),\n  ),\n  createSerializer(\n    // Key\n    'error',\n    // Check\n    (v): v is Error => v instanceof Error,\n    // To\n    (v) => ({\n      ...v,\n      message: v.message,\n      stack: process.env.NODE_ENV === 'development' ? v.stack : undefined,\n      cause: v.cause,\n    }),\n    // From\n    (v) => Object.assign(new Error(v.message), v),\n  ),\n  createSerializer(\n    // Key\n    'formData',\n    // Check\n    (v): v is FormData => v instanceof FormData,\n    // To\n    (v) => {\n      const entries: Record<\n        string,\n        Array<FormDataEntryValue> | FormDataEntryValue\n      > = {}\n      v.forEach((value, key) => {\n        const entry = entries[key]\n        if (entry !== undefined) {\n          if (Array.isArray(entry)) {\n            entry.push(value)\n          } else {\n            entries[key] = [entry, value]\n          }\n        } else {\n          entries[key] = value\n        }\n      })\n      return entries\n    },\n    // From\n    (v) => {\n      const formData = new FormData()\n      Object.entries(v).forEach(([key, value]) => {\n        if (Array.isArray(value)) {\n          value.forEach((val) => formData.append(key, val))\n        } else {\n          formData.append(key, value)\n        }\n      })\n      return formData\n    },\n  ),\n  createSerializer(\n    // Key\n    'bigint',\n    // Check\n    (v): v is bigint => typeof v === 'bigint',\n    // To\n    (v) => v.toString(),\n    // From\n    (v) => BigInt(v),\n  ),\n] as const\n", "// a function that can have different implementations on the client and server.\n// implementations not provided will default to a no-op function.\n\nexport type IsomorphicFn<\n  TArgs extends Array<any> = [],\n  TServer = undefined,\n  TClient = undefined,\n> = (...args: TArgs) => TServer | TClient\n\nexport interface ServerOnlyFn<TArgs extends Array<any>, TServer>\n  extends IsomorphicFn<TArgs, TServer> {\n  client: <TClient>(\n    clientImpl: (...args: TArgs) => TClient,\n  ) => IsomorphicFn<TArgs, TServer, TClient>\n}\n\nexport interface ClientOnlyFn<TArgs extends Array<any>, TClient>\n  extends IsomorphicFn<TArgs, undefined, TClient> {\n  server: <TServer>(\n    serverImpl: (...args: TArgs) => TServer,\n  ) => IsomorphicFn<TArgs, TServer, TClient>\n}\n\nexport interface IsomorphicFnBase extends IsomorphicFn {\n  server: <TArgs extends Array<any>, TServer>(\n    serverImpl: (...args: TArgs) => TServer,\n  ) => ServerOnlyFn<TArgs, TServer>\n  client: <TArgs extends Array<any>, TClient>(\n    clientImpl: (...args: TArgs) => TClient,\n  ) => ClientOnlyFn<TArgs, TClient>\n}\n\n// this is a dummy function, it will be replaced by the transformer\nexport function createIsomorphicFn(): IsomorphicFnBase {\n  return null!\n}\n", "type EnvOnlyFn = <TFn extends (...args: Array<any>) => any>(fn: TFn) => TFn\n\n// A function that will only be available in the server build\n// If called on the client, it will throw an error\nexport const serverOnly: EnvOnlyFn = (fn) => fn\n\n// A function that will only be available in the client build\n// If called on the server, it will throw an error\nexport const clientOnly: EnvOnlyFn = (fn) => fn\n", "import type { AnyMiddleware } from './createMiddleware'\n\nexport const globalMiddleware: Array<AnyMiddleware> = []\n\nexport function registerGlobalMiddleware(options: {\n  middleware: Array<AnyMiddleware>\n}) {\n  globalMiddleware.push(...options.middleware)\n}\n", "import { default as invariant } from 'tiny-invariant'\nimport { default as warning } from 'tiny-warning'\nimport { isNotFound, isRedirect } from '@tanstack/router-core'\nimport { startSerializer } from './serializer'\nimport { mergeHeaders } from './headers'\nimport { globalMiddleware } from './registerGlobalMiddleware'\nimport type {\n  AnyValidator,\n  Constrain,\n  Expand,\n  ResolveValidatorInput,\n  SerializerParse,\n  SerializerStringify,\n  SerializerStringifyBy,\n  Validator,\n} from '@tanstack/router-core'\nimport type { Readable } from 'node:stream'\nimport type {\n  AnyMiddleware,\n  AssignAllClientSendContext,\n  AssignAllServerContext,\n  IntersectAllValidatorInputs,\n  IntersectAllValidatorOutputs,\n  MiddlewareClientFnResult,\n  MiddlewareServerFnResult,\n} from './createMiddleware'\n\nexport function createServerFn<\n  TMethod extends Method,\n  TServerFnResponseType extends ServerFnResponseType = 'data',\n  TResponse = unknown,\n  TMiddlewares = undefined,\n  TValidator = undefined,\n>(\n  options?: {\n    method?: TMethod\n    response?: TServerFnResponseType\n    type?: ServerFnType\n  },\n  __opts?: ServerFnBaseOptions<\n    TMethod,\n    TServerFnResponseType,\n    TResponse,\n    TMiddlewares,\n    TValidator\n  >,\n): ServerFnBuilder<TMethod, TServerFnResponseType> {\n  const resolvedOptions = (__opts || options || {}) as ServerFnBaseOptions<\n    TMethod,\n    ServerFnResponseType,\n    TResponse,\n    TMiddlewares,\n    TValidator\n  >\n\n  if (typeof resolvedOptions.method === 'undefined') {\n    resolvedOptions.method = 'GET' as TMethod\n  }\n\n  return {\n    options: resolvedOptions as any,\n    middleware: (middleware) => {\n      return createServerFn<\n        TMethod,\n        ServerFnResponseType,\n        TResponse,\n        TMiddlewares,\n        TValidator\n      >(undefined, Object.assign(resolvedOptions, { middleware })) as any\n    },\n    validator: (validator) => {\n      return createServerFn<\n        TMethod,\n        ServerFnResponseType,\n        TResponse,\n        TMiddlewares,\n        TValidator\n      >(undefined, Object.assign(resolvedOptions, { validator })) as any\n    },\n    type: (type) => {\n      return createServerFn<\n        TMethod,\n        ServerFnResponseType,\n        TResponse,\n        TMiddlewares,\n        TValidator\n      >(undefined, Object.assign(resolvedOptions, { type })) as any\n    },\n    handler: (...args) => {\n      // This function signature changes due to AST transformations\n      // in the babel plugin. We need to cast it to the correct\n      // function signature post-transformation\n      const [extractedFn, serverFn] = args as unknown as [\n        CompiledFetcherFn<TResponse, TServerFnResponseType>,\n        ServerFn<\n          TMethod,\n          TServerFnResponseType,\n          TMiddlewares,\n          TValidator,\n          TResponse\n        >,\n      ]\n\n      // Keep the original function around so we can use it\n      // in the server environment\n      Object.assign(resolvedOptions, {\n        ...extractedFn,\n        extractedFn,\n        serverFn,\n      })\n\n      const resolvedMiddleware = [\n        ...(resolvedOptions.middleware || []),\n        serverFnBaseToMiddleware(resolvedOptions),\n      ]\n\n      // We want to make sure the new function has the same\n      // properties as the original function\n\n      return Object.assign(\n        async (opts?: CompiledFetcherFnOptions) => {\n          // Start by executing the client-side middleware chain\n          return executeMiddleware(resolvedMiddleware, 'client', {\n            ...extractedFn,\n            ...resolvedOptions,\n            data: opts?.data as any,\n            headers: opts?.headers,\n            signal: opts?.signal,\n            context: {},\n          }).then((d) => {\n            if (resolvedOptions.response === 'full') {\n              return d\n            }\n            if (d.error) throw d.error\n            return d.result\n          })\n        },\n        {\n          // This copies over the URL, function ID\n          ...extractedFn,\n          // The extracted function on the server-side calls\n          // this function\n          __executeServer: async (opts_: any, signal: AbortSignal) => {\n            const opts =\n              opts_ instanceof FormData ? extractFormDataContext(opts_) : opts_\n\n            opts.type =\n              typeof resolvedOptions.type === 'function'\n                ? resolvedOptions.type(opts)\n                : resolvedOptions.type\n\n            const ctx = {\n              ...extractedFn,\n              ...opts,\n              signal,\n            }\n\n            const run = () =>\n              executeMiddleware(resolvedMiddleware, 'server', ctx).then(\n                (d) => ({\n                  // Only send the result and sendContext back to the client\n                  result: d.result,\n                  error: d.error,\n                  context: d.sendContext,\n                }),\n              )\n\n            if (ctx.type === 'static') {\n              let response: StaticCachedResult | undefined\n\n              // If we can get the cached item, try to get it\n              if (serverFnStaticCache?.getItem) {\n                // If this throws, it's okay to let it bubble up\n                response = await serverFnStaticCache.getItem(ctx)\n              }\n\n              if (!response) {\n                // If there's no cached item, execute the server function\n                response = await run()\n                  .then((d) => {\n                    return {\n                      ctx: d,\n                      error: null,\n                    }\n                  })\n                  .catch((e) => {\n                    return {\n                      ctx: undefined,\n                      error: e,\n                    }\n                  })\n\n                if (serverFnStaticCache?.setItem) {\n                  await serverFnStaticCache.setItem(ctx, response)\n                }\n              }\n\n              invariant(\n                response,\n                'No response from both server and static cache!',\n              )\n\n              if (response.error) {\n                throw response.error\n              }\n\n              return response.ctx\n            }\n\n            return run()\n          },\n        },\n      ) as any\n    },\n  }\n}\n\nasync function executeMiddleware(\n  middlewares: Array<AnyMiddleware>,\n  env: 'client' | 'server',\n  opts: ServerFnMiddlewareOptions,\n): Promise<ServerFnMiddlewareResult> {\n  const flattenedMiddlewares = flattenMiddlewares([\n    ...globalMiddleware,\n    ...middlewares,\n  ])\n\n  const next: NextFn = async (ctx) => {\n    // Get the next middleware\n    const nextMiddleware = flattenedMiddlewares.shift()\n\n    // If there are no more middlewares, return the context\n    if (!nextMiddleware) {\n      return ctx\n    }\n\n    if (\n      nextMiddleware.options.validator &&\n      (env === 'client' ? nextMiddleware.options.validateClient : true)\n    ) {\n      // Execute the middleware's input function\n      ctx.data = await execValidator(nextMiddleware.options.validator, ctx.data)\n    }\n\n    const middlewareFn = (\n      env === 'client'\n        ? nextMiddleware.options.client\n        : nextMiddleware.options.server\n    ) as MiddlewareFn | undefined\n\n    if (middlewareFn) {\n      // Execute the middleware\n      return applyMiddleware(middlewareFn, ctx, async (newCtx) => {\n        return next(newCtx).catch((error: any) => {\n          if (isRedirect(error) || isNotFound(error)) {\n            return {\n              ...newCtx,\n              error,\n            }\n          }\n\n          throw error\n        })\n      })\n    }\n\n    return next(ctx)\n  }\n\n  // Start the middleware chain\n  return next({\n    ...opts,\n    headers: opts.headers || {},\n    sendContext: opts.sendContext || {},\n    context: opts.context || {},\n  })\n}\n\nexport interface JsonResponse<TData> extends Response {\n  json: () => Promise<TData>\n}\n\nexport type CompiledFetcherFnOptions = {\n  method: Method\n  data: unknown\n  response?: ServerFnResponseType\n  headers?: HeadersInit\n  signal?: AbortSignal\n  context?: any\n}\n\nexport type Fetcher<\n  TMiddlewares,\n  TValidator,\n  TResponse,\n  TServerFnResponseType extends ServerFnResponseType,\n> =\n  undefined extends IntersectAllValidatorInputs<TMiddlewares, TValidator>\n    ? OptionalFetcher<\n        TMiddlewares,\n        TValidator,\n        TResponse,\n        TServerFnResponseType\n      >\n    : RequiredFetcher<\n        TMiddlewares,\n        TValidator,\n        TResponse,\n        TServerFnResponseType\n      >\n\nexport interface FetcherBase {\n  url: string\n  __executeServer: (opts: {\n    method: Method\n    response?: ServerFnResponseType\n    data: unknown\n    headers?: HeadersInit\n    context?: any\n    signal: AbortSignal\n  }) => Promise<unknown>\n}\n\nexport type FetchResult<\n  TMiddlewares,\n  TResponse,\n  TServerFnResponseType extends ServerFnResponseType,\n> = TServerFnResponseType extends 'raw'\n  ? Promise<Response>\n  : TServerFnResponseType extends 'full'\n    ? Promise<FullFetcherData<TMiddlewares, TResponse>>\n    : Promise<FetcherData<TResponse>>\n\nexport interface OptionalFetcher<\n  TMiddlewares,\n  TValidator,\n  TResponse,\n  TServerFnResponseType extends ServerFnResponseType,\n> extends FetcherBase {\n  (\n    options?: OptionalFetcherDataOptions<TMiddlewares, TValidator>,\n  ): FetchResult<TMiddlewares, TResponse, TServerFnResponseType>\n}\n\nexport interface RequiredFetcher<\n  TMiddlewares,\n  TValidator,\n  TResponse,\n  TServerFnResponseType extends ServerFnResponseType,\n> extends FetcherBase {\n  (\n    opts: RequiredFetcherDataOptions<TMiddlewares, TValidator>,\n  ): FetchResult<TMiddlewares, TResponse, TServerFnResponseType>\n}\n\nexport type FetcherBaseOptions = {\n  headers?: HeadersInit\n  type?: ServerFnType\n  signal?: AbortSignal\n}\n\nexport type ServerFnType = 'static' | 'dynamic'\n\nexport interface OptionalFetcherDataOptions<TMiddlewares, TValidator>\n  extends FetcherBaseOptions {\n  data?: Expand<IntersectAllValidatorInputs<TMiddlewares, TValidator>>\n}\n\nexport interface RequiredFetcherDataOptions<TMiddlewares, TValidator>\n  extends FetcherBaseOptions {\n  data: Expand<IntersectAllValidatorInputs<TMiddlewares, TValidator>>\n}\n\nexport interface FullFetcherData<TMiddlewares, TResponse> {\n  error: unknown\n  result: FetcherData<TResponse>\n  context: AssignAllClientSendContext<TMiddlewares>\n}\n\nexport type FetcherData<TResponse> =\n  TResponse extends JsonResponse<any>\n    ? SerializerParse<ReturnType<TResponse['json']>>\n    : SerializerParse<TResponse>\n\nexport type RscStream<T> = {\n  __cacheState: T\n}\n\nexport type Method = 'GET' | 'POST'\nexport type ServerFnResponseType = 'data' | 'full' | 'raw'\n\n// see https://h3.unjs.io/guide/event-handler#responses-types\nexport type RawResponse = Response | ReadableStream | Readable | null | string\n\nexport type ServerFnReturnType<\n  TServerFnResponseType extends ServerFnResponseType,\n  TResponse,\n> = TServerFnResponseType extends 'raw'\n  ? RawResponse | Promise<RawResponse>\n  : Promise<SerializerStringify<TResponse>> | SerializerStringify<TResponse>\nexport type ServerFn<\n  TMethod,\n  TServerFnResponseType extends ServerFnResponseType,\n  TMiddlewares,\n  TValidator,\n  TResponse,\n> = (\n  ctx: ServerFnCtx<TMethod, TServerFnResponseType, TMiddlewares, TValidator>,\n) => ServerFnReturnType<TServerFnResponseType, TResponse>\n\nexport interface ServerFnCtx<\n  TMethod,\n  TServerFnResponseType extends ServerFnResponseType,\n  TMiddlewares,\n  TValidator,\n> {\n  method: TMethod\n  response: TServerFnResponseType\n  data: Expand<IntersectAllValidatorOutputs<TMiddlewares, TValidator>>\n  context: Expand<AssignAllServerContext<TMiddlewares>>\n  signal: AbortSignal\n}\n\nexport type CompiledFetcherFn<\n  TResponse,\n  TServerFnResponseType extends ServerFnResponseType,\n> = {\n  (\n    opts: CompiledFetcherFnOptions &\n      ServerFnBaseOptions<Method, TServerFnResponseType>,\n  ): Promise<TResponse>\n  url: string\n}\n\nexport type ServerFnBaseOptions<\n  TMethod extends Method = 'GET',\n  TServerFnResponseType extends ServerFnResponseType = 'data',\n  TResponse = unknown,\n  TMiddlewares = unknown,\n  TInput = unknown,\n> = {\n  method: TMethod\n  response?: TServerFnResponseType\n  validateClient?: boolean\n  middleware?: Constrain<TMiddlewares, ReadonlyArray<AnyMiddleware>>\n  validator?: ConstrainValidator<TInput>\n  extractedFn?: CompiledFetcherFn<TResponse, TServerFnResponseType>\n  serverFn?: ServerFn<\n    TMethod,\n    TServerFnResponseType,\n    TMiddlewares,\n    TInput,\n    TResponse\n  >\n  functionId: string\n  type: ServerFnTypeOrTypeFn<\n    TMethod,\n    TServerFnResponseType,\n    TMiddlewares,\n    AnyValidator\n  >\n}\n\nexport type ValidatorInputStringify<TValidator> = SerializerStringifyBy<\n  ResolveValidatorInput<TValidator>,\n  Date | undefined | FormData\n>\n\nexport type ValidatorSerializerStringify<TValidator> =\n  ValidatorInputStringify<TValidator> extends infer TInput\n    ? Validator<TInput, any>\n    : never\n\nexport type ConstrainValidator<TValidator> =\n  | (unknown extends TValidator\n      ? TValidator\n      : ResolveValidatorInput<TValidator> extends ValidatorInputStringify<TValidator>\n        ? TValidator\n        : never)\n  | ValidatorSerializerStringify<TValidator>\n\nexport interface ServerFnMiddleware<\n  TMethod extends Method,\n  TServerFnResponseType extends ServerFnResponseType,\n  TValidator,\n> {\n  middleware: <const TNewMiddlewares = undefined>(\n    middlewares: Constrain<TNewMiddlewares, ReadonlyArray<AnyMiddleware>>,\n  ) => ServerFnAfterMiddleware<\n    TMethod,\n    TServerFnResponseType,\n    TNewMiddlewares,\n    TValidator\n  >\n}\n\nexport interface ServerFnAfterMiddleware<\n  TMethod extends Method,\n  TServerFnResponseType extends ServerFnResponseType,\n  TMiddlewares,\n  TValidator,\n> extends ServerFnValidator<TMethod, TServerFnResponseType, TMiddlewares>,\n    ServerFnTyper<TMethod, TServerFnResponseType, TMiddlewares, TValidator>,\n    ServerFnHandler<TMethod, TServerFnResponseType, TMiddlewares, TValidator> {}\n\nexport type ValidatorFn<\n  TMethod extends Method,\n  TServerFnResponseType extends ServerFnResponseType,\n  TMiddlewares,\n> = <TValidator>(\n  validator: ConstrainValidator<TValidator>,\n) => ServerFnAfterValidator<\n  TMethod,\n  TServerFnResponseType,\n  TMiddlewares,\n  TValidator\n>\n\nexport interface ServerFnValidator<\n  TMethod extends Method,\n  TServerFnResponseType extends ServerFnResponseType,\n  TMiddlewares,\n> {\n  validator: ValidatorFn<TMethod, TServerFnResponseType, TMiddlewares>\n}\n\nexport interface ServerFnAfterValidator<\n  TMethod extends Method,\n  TServerFnResponseType extends ServerFnResponseType,\n  TMiddlewares,\n  TValidator,\n> extends ServerFnMiddleware<TMethod, TServerFnResponseType, TValidator>,\n    ServerFnTyper<TMethod, TServerFnResponseType, TMiddlewares, TValidator>,\n    ServerFnHandler<TMethod, TServerFnResponseType, TMiddlewares, TValidator> {}\n\n// Typer\nexport interface ServerFnTyper<\n  TMethod extends Method,\n  TServerFnResponseType extends ServerFnResponseType,\n  TMiddlewares,\n  TValidator,\n> {\n  type: (\n    typer: ServerFnTypeOrTypeFn<\n      TMethod,\n      TServerFnResponseType,\n      TMiddlewares,\n      TValidator\n    >,\n  ) => ServerFnAfterTyper<\n    TMethod,\n    TServerFnResponseType,\n    TMiddlewares,\n    TValidator\n  >\n}\n\nexport type ServerFnTypeOrTypeFn<\n  TMethod extends Method,\n  TServerFnResponseType extends ServerFnResponseType,\n  TMiddlewares,\n  TValidator,\n> =\n  | ServerFnType\n  | ((\n      ctx: ServerFnCtx<\n        TMethod,\n        TServerFnResponseType,\n        TMiddlewares,\n        TValidator\n      >,\n    ) => ServerFnType)\n\nexport interface ServerFnAfterTyper<\n  TMethod extends Method,\n  TServerFnResponseType extends ServerFnResponseType,\n  TMiddlewares,\n  TValidator,\n> extends ServerFnHandler<\n    TMethod,\n    TServerFnResponseType,\n    TMiddlewares,\n    TValidator\n  > {}\n\n// Handler\nexport interface ServerFnHandler<\n  TMethod extends Method,\n  TServerFnResponseType extends ServerFnResponseType,\n  TMiddlewares,\n  TValidator,\n> {\n  handler: <TNewResponse>(\n    fn?: ServerFn<\n      TMethod,\n      TServerFnResponseType,\n      TMiddlewares,\n      TValidator,\n      TNewResponse\n    >,\n  ) => Fetcher<TMiddlewares, TValidator, TNewResponse, TServerFnResponseType>\n}\n\nexport interface ServerFnBuilder<\n  TMethod extends Method = 'GET',\n  TServerFnResponseType extends ServerFnResponseType = 'data',\n> extends ServerFnMiddleware<TMethod, TServerFnResponseType, undefined>,\n    ServerFnValidator<TMethod, TServerFnResponseType, undefined>,\n    ServerFnTyper<TMethod, TServerFnResponseType, undefined, undefined>,\n    ServerFnHandler<TMethod, TServerFnResponseType, undefined, undefined> {\n  options: ServerFnBaseOptions<\n    TMethod,\n    TServerFnResponseType,\n    unknown,\n    undefined,\n    undefined\n  >\n}\n\nexport type StaticCachedResult = {\n  ctx?: {\n    result: any\n    context: any\n  }\n  error?: any\n}\n\nexport type ServerFnStaticCache = {\n  getItem: (\n    ctx: ServerFnMiddlewareResult,\n  ) => StaticCachedResult | Promise<StaticCachedResult | undefined>\n  setItem: (\n    ctx: ServerFnMiddlewareResult,\n    response: StaticCachedResult,\n  ) => Promise<void>\n  fetchItem: (\n    ctx: ServerFnMiddlewareResult,\n  ) => StaticCachedResult | Promise<StaticCachedResult | undefined>\n}\n\nexport let serverFnStaticCache: ServerFnStaticCache | undefined\n\nexport function setServerFnStaticCache(\n  cache?: ServerFnStaticCache | (() => ServerFnStaticCache | undefined),\n) {\n  const previousCache = serverFnStaticCache\n  serverFnStaticCache = typeof cache === 'function' ? cache() : cache\n\n  return () => {\n    serverFnStaticCache = previousCache\n  }\n}\n\nexport function createServerFnStaticCache(\n  serverFnStaticCache: ServerFnStaticCache,\n) {\n  return serverFnStaticCache\n}\n\n/**\n * This is a simple hash function for generating a hash from a string to make the filenames shorter.\n *\n * It is not cryptographically secure (as its using SHA-1) and should not be used for any security purposes.\n *\n * It is only used to generate a hash for the static cache filenames.\n *\n * @param message - The input string to hash.\n * @returns A promise that resolves to the SHA-1 hash of the input string in hexadecimal format.\n *\n * @example\n * ```typescript\n * const hash = await sha1Hash(\"hello\");\n * console.log(hash); // Outputs the SHA-1 hash of \"hello\" -> \"aaf4c61ddcc5e8a2dabede0f3b482cd9aea9434d\"\n * ```\n */\nasync function sha1Hash(message: string): Promise<string> {\n  // Encode the string as UTF-8\n  const msgBuffer = new TextEncoder().encode(message)\n\n  // Hash the message\n  const hashBuffer = await crypto.subtle.digest('SHA-1', msgBuffer)\n\n  // Convert the ArrayBuffer to a string\n  const hashArray = Array.from(new Uint8Array(hashBuffer))\n  const hashHex = hashArray.map((b) => b.toString(16).padStart(2, '0')).join('')\n  return hashHex\n}\n\nsetServerFnStaticCache(() => {\n  const getStaticCacheUrl = async (\n    options: ServerFnMiddlewareResult,\n    hash: string,\n  ) => {\n    const filename = await sha1Hash(`${options.functionId}__${hash}`)\n    return `/__tsr/staticServerFnCache/${filename}.json`\n  }\n\n  const jsonToFilenameSafeString = (json: any) => {\n    // Custom replacer to sort keys\n    const sortedKeysReplacer = (key: string, value: any) =>\n      value && typeof value === 'object' && !Array.isArray(value)\n        ? Object.keys(value)\n            .sort()\n            .reduce((acc: any, curr: string) => {\n              acc[curr] = value[curr]\n              return acc\n            }, {})\n        : value\n\n    // Convert JSON to string with sorted keys\n    const jsonString = JSON.stringify(json ?? '', sortedKeysReplacer)\n\n    // Replace characters invalid in filenames\n    return jsonString\n      .replace(/[/\\\\?%*:|\"<>]/g, '-') // Replace invalid characters with a dash\n      .replace(/\\s+/g, '_') // Optionally replace whitespace with underscores\n  }\n\n  const staticClientCache =\n    typeof document !== 'undefined' ? new Map<string, any>() : null\n\n  return createServerFnStaticCache({\n    getItem: async (ctx) => {\n      if (typeof document === 'undefined') {\n        const hash = jsonToFilenameSafeString(ctx.data)\n        const url = await getStaticCacheUrl(ctx, hash)\n        const publicUrl = process.env.TSS_OUTPUT_PUBLIC_DIR!\n\n        // Use fs instead of fetch to read from filesystem\n        const { promises: fs } = await import('node:fs')\n        const path = await import('node:path')\n        const filePath = path.join(publicUrl, url)\n\n        const [cachedResult, readError] = await fs\n          .readFile(filePath, 'utf-8')\n          .then((c) => [\n            startSerializer.parse(c) as {\n              ctx: unknown\n              error: any\n            },\n            null,\n          ])\n          .catch((e) => [null, e])\n\n        if (readError && readError.code !== 'ENOENT') {\n          throw readError\n        }\n\n        return cachedResult as StaticCachedResult\n      }\n\n      return undefined\n    },\n    setItem: async (ctx, response) => {\n      const { promises: fs } = await import('node:fs')\n      const path = await import('node:path')\n\n      const hash = jsonToFilenameSafeString(ctx.data)\n      const url = await getStaticCacheUrl(ctx, hash)\n      const publicUrl = process.env.TSS_OUTPUT_PUBLIC_DIR!\n      const filePath = path.join(publicUrl, url)\n\n      // Ensure the directory exists\n      await fs.mkdir(path.dirname(filePath), { recursive: true })\n\n      // Store the result with fs\n      await fs.writeFile(filePath, startSerializer.stringify(response))\n    },\n    fetchItem: async (ctx) => {\n      const hash = jsonToFilenameSafeString(ctx.data)\n      const url = await getStaticCacheUrl(ctx, hash)\n\n      let result: any = staticClientCache?.get(url)\n\n      if (!result) {\n        result = await fetch(url, {\n          method: 'GET',\n        })\n          .then((r) => r.text())\n          .then((d) => startSerializer.parse(d))\n\n        staticClientCache?.set(url, result)\n      }\n\n      return result\n    },\n  })\n})\n\nexport function extractFormDataContext(formData: FormData) {\n  const serializedContext = formData.get('__TSR_CONTEXT')\n  formData.delete('__TSR_CONTEXT')\n\n  if (typeof serializedContext !== 'string') {\n    return {\n      context: {},\n      data: formData,\n    }\n  }\n\n  try {\n    const context = startSerializer.parse(serializedContext)\n    return {\n      context,\n      data: formData,\n    }\n  } catch {\n    return {\n      data: formData,\n    }\n  }\n}\n\nexport function flattenMiddlewares(\n  middlewares: Array<AnyMiddleware>,\n): Array<AnyMiddleware> {\n  const seen = new Set<AnyMiddleware>()\n  const flattened: Array<AnyMiddleware> = []\n\n  const recurse = (middleware: Array<AnyMiddleware>) => {\n    middleware.forEach((m) => {\n      if (m.options.middleware) {\n        recurse(m.options.middleware)\n      }\n\n      if (!seen.has(m)) {\n        seen.add(m)\n        flattened.push(m)\n      }\n    })\n  }\n\n  recurse(middlewares)\n\n  return flattened\n}\n\nexport type ServerFnMiddlewareOptions = {\n  method: Method\n  response?: ServerFnResponseType\n  data: any\n  headers?: HeadersInit\n  signal?: AbortSignal\n  sendContext?: any\n  context?: any\n  type: ServerFnTypeOrTypeFn<any, any, any, any>\n  functionId: string\n}\n\nexport type ServerFnMiddlewareResult = ServerFnMiddlewareOptions & {\n  result?: unknown\n  error?: unknown\n  type: ServerFnTypeOrTypeFn<any, any, any, any>\n}\n\nexport type NextFn = (\n  ctx: ServerFnMiddlewareResult,\n) => Promise<ServerFnMiddlewareResult>\n\nexport type MiddlewareFn = (\n  ctx: ServerFnMiddlewareOptions & {\n    next: NextFn\n  },\n) => Promise<ServerFnMiddlewareResult>\n\nexport const applyMiddleware = async (\n  middlewareFn: MiddlewareFn,\n  ctx: ServerFnMiddlewareOptions,\n  nextFn: NextFn,\n) => {\n  return middlewareFn({\n    ...ctx,\n    next: (async (\n      userCtx: ServerFnMiddlewareResult | undefined = {} as any,\n    ) => {\n      // Return the next middleware\n      return nextFn({\n        ...ctx,\n        ...userCtx,\n        context: {\n          ...ctx.context,\n          ...userCtx.context,\n        },\n        sendContext: {\n          ...ctx.sendContext,\n          ...(userCtx.sendContext ?? {}),\n        },\n        headers: mergeHeaders(ctx.headers, userCtx.headers),\n        result:\n          userCtx.result !== undefined\n            ? userCtx.result\n            : ctx.response === 'raw'\n              ? userCtx\n              : (ctx as any).result,\n        error: userCtx.error ?? (ctx as any).error,\n      })\n    }) as any,\n  } as any)\n}\n\nexport function execValidator(\n  validator: AnyValidator,\n  input: unknown,\n): unknown {\n  if (validator == null) return {}\n\n  if ('~standard' in validator) {\n    const result = validator['~standard'].validate(input)\n\n    if (result instanceof Promise)\n      throw new Error('Async validation not supported')\n\n    if (result.issues)\n      throw new Error(JSON.stringify(result.issues, undefined, 2))\n\n    return result.value\n  }\n\n  if ('parse' in validator) {\n    return validator.parse(input)\n  }\n\n  if (typeof validator === 'function') {\n    return validator(input)\n  }\n\n  throw new Error('Invalid validator type!')\n}\n\nexport function serverFnBaseToMiddleware(\n  options: ServerFnBaseOptions<any, any, any, any, any>,\n): AnyMiddleware {\n  return {\n    _types: undefined!,\n    options: {\n      validator: options.validator,\n      validateClient: options.validateClient,\n      client: async ({ next, sendContext, ...ctx }) => {\n        const payload = {\n          ...ctx,\n          // switch the sendContext over to context\n          context: sendContext,\n          type: typeof ctx.type === 'function' ? ctx.type(ctx) : ctx.type,\n        } as any\n\n        if (\n          ctx.type === 'static' &&\n          process.env.NODE_ENV === 'production' &&\n          typeof document !== 'undefined'\n        ) {\n          invariant(\n            serverFnStaticCache,\n            'serverFnStaticCache.fetchItem is not available!',\n          )\n\n          const result = await serverFnStaticCache.fetchItem(payload)\n\n          if (result) {\n            if (result.error) {\n              throw result.error\n            }\n\n            return next(result.ctx)\n          }\n\n          warning(\n            result,\n            `No static cache item found for ${payload.functionId}__${JSON.stringify(payload.data)}, falling back to server function...`,\n          )\n        }\n\n        // Execute the extracted function\n        // but not before serializing the context\n        const res = await options.extractedFn?.(payload)\n\n        return next(res) as unknown as MiddlewareClientFnResult<any, any, any>\n      },\n      server: async ({ next, ...ctx }) => {\n        // Execute the server function\n        const result = await options.serverFn?.(ctx)\n\n        return next({\n          ...ctx,\n          result,\n        } as any) as unknown as MiddlewareServerFnResult<any, any, any, any>\n      },\n    },\n  }\n}\n", "import { mergeHeaders } from './headers'\nimport type { JsonResponse } from './createServerFn'\n\nexport function json<TData>(\n  payload: TData,\n  init?: ResponseInit,\n): JsonResponse<TData> {\n  return new Response(JSON.stringify(payload), {\n    ...init,\n    headers: mergeHeaders(\n      { 'content-type': 'application/json' },\n      init?.headers,\n    ),\n  })\n}\n", "import type {\n  ConstrainVali<PERSON><PERSON>,\n  Method,\n  ServerFnResponseType,\n  ServerFnTypeOrTypeFn,\n} from './createServerFn'\nimport type {\n  Assign,\n  Constrain,\n  Expand,\n  IntersectAssign,\n  ResolveValidatorInput,\n  ResolveValidatorOutput,\n  SerializerStringify,\n} from '@tanstack/router-core'\n\nexport type AssignAllMiddleware<\n  TMiddlewares,\n  TType extends keyof AnyMiddleware['_types'],\n  TAcc = undefined,\n> = TMiddlewares extends readonly [\n  infer TMiddleware extends AnyMiddleware,\n  ...infer TRest,\n]\n  ? AssignAllMiddleware<\n      TRest,\n      TType,\n      Assign<TAcc, TMiddleware['_types'][TType]>\n    >\n  : TAcc\n\n/**\n * Recursively resolve the client context type produced by a sequence of middleware\n */\nexport type AssignAllClientContextBeforeNext<\n  TMiddlewares,\n  TClientContext = undefined,\n> = unknown extends TClientContext\n  ? TClientContext\n  : Assign<\n      AssignAllMiddleware<TMiddlewares, 'allClientContextBeforeNext'>,\n      TClientContext\n    >\n\nexport type AssignAllClientSendContext<\n  TMiddlewares,\n  TSendContext = undefined,\n> = unknown extends TSendContext\n  ? TSendContext\n  : Assign<\n      AssignAllMiddleware<TMiddlewares, 'allClientSendContext'>,\n      TSendContext\n    >\n\nexport type AssignAllClientContextAfterNext<\n  TMiddlewares,\n  TClientContext = undefined,\n  TSendContext = undefined,\n> = unknown extends TClientContext\n  ? Assign<TClientContext, TSendContext>\n  : Assign<\n      AssignAllMiddleware<TMiddlewares, 'allClientContextAfterNext'>,\n      Assign<TClientContext, TSendContext>\n    >\n\n/**\n * Recursively resolve the server context type produced by a sequence of middleware\n */\nexport type AssignAllServerContext<\n  TMiddlewares,\n  TSendContext = undefined,\n  TServerContext = undefined,\n> = unknown extends TSendContext\n  ? Assign<TSendContext, TServerContext>\n  : Assign<\n      AssignAllMiddleware<TMiddlewares, 'allServerContext'>,\n      Assign<TSendContext, TServerContext>\n    >\n\nexport type AssignAllServerSendContext<\n  TMiddlewares,\n  TSendContext = undefined,\n> = unknown extends TSendContext\n  ? TSendContext\n  : Assign<\n      AssignAllMiddleware<TMiddlewares, 'allServerSendContext'>,\n      TSendContext\n    >\n\nexport type IntersectAllMiddleware<\n  TMiddlewares,\n  TType extends keyof AnyMiddleware['_types'],\n  TAcc = undefined,\n> = TMiddlewares extends readonly [\n  infer TMiddleware extends AnyMiddleware,\n  ...infer TRest,\n]\n  ? IntersectAllMiddleware<\n      TRest,\n      TType,\n      IntersectAssign<TAcc, TMiddleware['_types'][TType]>\n    >\n  : TAcc\n\n/**\n * Recursively resolve the input type produced by a sequence of middleware\n */\nexport type IntersectAllValidatorInputs<TMiddlewares, TValidator> =\n  unknown extends TValidator\n    ? TValidator\n    : IntersectAssign<\n        IntersectAllMiddleware<TMiddlewares, 'allInput'>,\n        TValidator extends undefined\n          ? undefined\n          : ResolveValidatorInput<TValidator>\n      >\n/**\n * Recursively merge the output type produced by a sequence of middleware\n */\nexport type IntersectAllValidatorOutputs<TMiddlewares, TValidator> =\n  unknown extends TValidator\n    ? TValidator\n    : IntersectAssign<\n        IntersectAllMiddleware<TMiddlewares, 'allOutput'>,\n        TValidator extends undefined\n          ? undefined\n          : ResolveValidatorOutput<TValidator>\n      >\n\nexport interface MiddlewareOptions<\n  in out TMiddlewares,\n  in out TValidator,\n  in out TServerContext,\n  in out TClientContext,\n  in out TServerFnResponseType extends ServerFnResponseType,\n> {\n  validateClient?: boolean\n  middleware?: TMiddlewares\n  validator?: ConstrainValidator<TValidator>\n  client?: MiddlewareClientFn<\n    TMiddlewares,\n    TValidator,\n    TServerContext,\n    TClientContext,\n    TServerFnResponseType\n  >\n  server?: MiddlewareServerFn<\n    TMiddlewares,\n    TValidator,\n    TServerContext,\n    unknown,\n    unknown,\n    TServerFnResponseType\n  >\n}\n\nexport type MiddlewareServerNextFn<TMiddlewares, TServerSendContext> = <\n  TNewServerContext = undefined,\n  TSendContext = undefined,\n>(ctx?: {\n  context?: TNewServerContext\n  sendContext?: SerializerStringify<TSendContext>\n}) => Promise<\n  ServerResultWithContext<\n    TMiddlewares,\n    TServerSendContext,\n    TNewServerContext,\n    TSendContext\n  >\n>\n\nexport interface MiddlewareServerFnOptions<\n  in out TMiddlewares,\n  in out TValidator,\n  in out TServerSendContext,\n  in out TServerFnResponseType,\n> {\n  data: Expand<IntersectAllValidatorOutputs<TMiddlewares, TValidator>>\n  context: Expand<AssignAllServerContext<TMiddlewares, TServerSendContext>>\n  next: MiddlewareServerNextFn<TMiddlewares, TServerSendContext>\n  response: TServerFnResponseType\n  method: Method\n  filename: string\n  functionId: string\n  signal: AbortSignal\n}\n\nexport type MiddlewareServerFn<\n  TMiddlewares,\n  TValidator,\n  TServerSendContext,\n  TNewServerContext,\n  TSendContext,\n  TServerFnResponseType extends ServerFnResponseType,\n> = (\n  options: MiddlewareServerFnOptions<\n    TMiddlewares,\n    TValidator,\n    TServerSendContext,\n    TServerFnResponseType\n  >,\n) => MiddlewareServerFnResult<\n  TMiddlewares,\n  TServerSendContext,\n  TNewServerContext,\n  TSendContext\n>\n\nexport type MiddlewareServerFnResult<\n  TMiddlewares,\n  TServerSendContext,\n  TServerContext,\n  TSendContext,\n> =\n  | Promise<\n      ServerResultWithContext<\n        TMiddlewares,\n        TServerSendContext,\n        TServerContext,\n        TSendContext\n      >\n    >\n  | ServerResultWithContext<\n      TMiddlewares,\n      TServerSendContext,\n      TServerContext,\n      TSendContext\n    >\n\nexport type MiddlewareClientNextFn<TMiddlewares> = <\n  TSendContext = undefined,\n  TNewClientContext = undefined,\n>(ctx?: {\n  context?: TNewClientContext\n  sendContext?: SerializerStringify<TSendContext>\n  headers?: HeadersInit\n}) => Promise<\n  ClientResultWithContext<TMiddlewares, TSendContext, TNewClientContext>\n>\n\nexport interface MiddlewareClientFnOptions<\n  in out TMiddlewares,\n  in out TValidator,\n  in out TServerFnResponseType extends ServerFnResponseType,\n> {\n  data: Expand<IntersectAllValidatorInputs<TMiddlewares, TValidator>>\n  context: Expand<AssignAllClientContextBeforeNext<TMiddlewares>>\n  sendContext: Expand<AssignAllServerSendContext<TMiddlewares>>\n  method: Method\n  response: TServerFnResponseType\n  signal: AbortSignal\n  next: MiddlewareClientNextFn<TMiddlewares>\n  filename: string\n  functionId: string\n  type: ServerFnTypeOrTypeFn<\n    Method,\n    TServerFnResponseType,\n    TMiddlewares,\n    TValidator\n  >\n}\n\nexport type MiddlewareClientFn<\n  TMiddlewares,\n  TValidator,\n  TSendContext,\n  TClientContext,\n  TServerFnResponseType extends ServerFnResponseType,\n> = (\n  options: MiddlewareClientFnOptions<\n    TMiddlewares,\n    TValidator,\n    TServerFnResponseType\n  >,\n) => MiddlewareClientFnResult<TMiddlewares, TSendContext, TClientContext>\n\nexport type MiddlewareClientFnResult<\n  TMiddlewares,\n  TSendContext,\n  TClientContext,\n> =\n  | Promise<ClientResultWithContext<TMiddlewares, TSendContext, TClientContext>>\n  | ClientResultWithContext<TMiddlewares, TSendContext, TClientContext>\n\nexport type ServerResultWithContext<\n  in out TMiddlewares,\n  in out TServerSendContext,\n  in out TServerContext,\n  in out TSendContext,\n> = {\n  'use functions must return the result of next()': true\n  _types: {\n    context: TServerContext\n    sendContext: TSendContext\n  }\n  context: Expand<\n    AssignAllServerContext<TMiddlewares, TServerSendContext, TServerContext>\n  >\n  sendContext: Expand<AssignAllClientSendContext<TMiddlewares, TSendContext>>\n}\n\nexport type ClientResultWithContext<\n  in out TMiddlewares,\n  in out TSendContext,\n  in out TClientContext,\n> = {\n  'use functions must return the result of next()': true\n  context: Expand<AssignAllClientContextAfterNext<TMiddlewares, TClientContext>>\n  sendContext: Expand<AssignAllServerSendContext<TMiddlewares, TSendContext>>\n  headers: HeadersInit\n}\n\nexport type AnyMiddleware = MiddlewareWithTypes<\n  any,\n  any,\n  any,\n  any,\n  any,\n  any,\n  any\n>\n\nexport interface MiddlewareTypes<\n  in out TMiddlewares,\n  in out TValidator,\n  in out TServerContext,\n  in out TServerSendContext,\n  in out TClientContext,\n  in out TClientSendContext,\n> {\n  middlewares: TMiddlewares\n  input: ResolveValidatorInput<TValidator>\n  allInput: IntersectAllValidatorInputs<TMiddlewares, TValidator>\n  output: ResolveValidatorOutput<TValidator>\n  allOutput: IntersectAllValidatorOutputs<TMiddlewares, TValidator>\n  clientContext: TClientContext\n  allClientContextBeforeNext: AssignAllClientContextBeforeNext<\n    TMiddlewares,\n    TClientContext\n  >\n  allClientContextAfterNext: AssignAllClientContextAfterNext<\n    TMiddlewares,\n    TClientContext,\n    TClientSendContext\n  >\n  serverContext: TServerContext\n  serverSendContext: TServerSendContext\n  allServerSendContext: AssignAllServerSendContext<\n    TMiddlewares,\n    TServerSendContext\n  >\n  allServerContext: AssignAllServerContext<\n    TMiddlewares,\n    TServerSendContext,\n    TServerContext\n  >\n  clientSendContext: TClientSendContext\n  allClientSendContext: AssignAllClientSendContext<\n    TMiddlewares,\n    TClientSendContext\n  >\n  validator: TValidator\n}\n\nexport interface MiddlewareWithTypes<\n  TMiddlewares,\n  TValidator,\n  TServerContext,\n  TServerSendContext,\n  TClientContext,\n  TClientSendContext,\n  TServerFnResponseType extends ServerFnResponseType,\n> {\n  _types: MiddlewareTypes<\n    TMiddlewares,\n    TValidator,\n    TServerContext,\n    TServerSendContext,\n    TClientContext,\n    TClientSendContext\n  >\n  options: MiddlewareOptions<\n    TMiddlewares,\n    TValidator,\n    TServerContext,\n    TClientContext,\n    TServerFnResponseType\n  >\n}\n\nexport interface MiddlewareAfterValidator<\n  TMiddlewares,\n  TValidator,\n  TServerFnResponseType extends ServerFnResponseType,\n> extends MiddlewareWithTypes<\n      TMiddlewares,\n      TValidator,\n      undefined,\n      undefined,\n      undefined,\n      undefined,\n      ServerFnResponseType\n    >,\n    MiddlewareServer<\n      TMiddlewares,\n      TValidator,\n      undefined,\n      undefined,\n      TServerFnResponseType\n    >,\n    MiddlewareClient<TMiddlewares, TValidator, ServerFnResponseType> {}\n\nexport interface MiddlewareValidator<\n  TMiddlewares,\n  TServerFnResponseType extends ServerFnResponseType,\n> {\n  validator: <TNewValidator>(\n    input: ConstrainValidator<TNewValidator>,\n  ) => MiddlewareAfterValidator<\n    TMiddlewares,\n    TNewValidator,\n    TServerFnResponseType\n  >\n}\n\nexport interface MiddlewareAfterServer<\n  TMiddlewares,\n  TValidator,\n  TServerContext,\n  TServerSendContext,\n  TClientContext,\n  TClientSendContext,\n  TServerFnResponseType extends ServerFnResponseType,\n> extends MiddlewareWithTypes<\n    TMiddlewares,\n    TValidator,\n    TServerContext,\n    TServerSendContext,\n    TClientContext,\n    TClientSendContext,\n    TServerFnResponseType\n  > {}\n\nexport interface MiddlewareServer<\n  TMiddlewares,\n  TValidator,\n  TServerSendContext,\n  TClientContext,\n  TServerFnResponseType extends ServerFnResponseType,\n> {\n  server: <TNewServerContext = undefined, TSendContext = undefined>(\n    server: MiddlewareServerFn<\n      TMiddlewares,\n      TValidator,\n      TServerSendContext,\n      TNewServerContext,\n      TSendContext,\n      TServerFnResponseType\n    >,\n  ) => MiddlewareAfterServer<\n    TMiddlewares,\n    TValidator,\n    TNewServerContext,\n    TServerSendContext,\n    TClientContext,\n    TSendContext,\n    ServerFnResponseType\n  >\n}\n\nexport interface MiddlewareAfterClient<\n  TMiddlewares,\n  TValidator,\n  TServerSendContext,\n  TClientContext,\n  TServerFnResponseType extends ServerFnResponseType,\n> extends MiddlewareWithTypes<\n      TMiddlewares,\n      TValidator,\n      undefined,\n      TServerSendContext,\n      TClientContext,\n      undefined,\n      TServerFnResponseType\n    >,\n    MiddlewareServer<\n      TMiddlewares,\n      TValidator,\n      TServerSendContext,\n      TClientContext,\n      TServerFnResponseType\n    > {}\n\nexport interface MiddlewareClient<\n  TMiddlewares,\n  TValidator,\n  TServerFnResponseType extends ServerFnResponseType,\n> {\n  client: <TSendServerContext = undefined, TNewClientContext = undefined>(\n    client: MiddlewareClientFn<\n      TMiddlewares,\n      TValidator,\n      TSendServerContext,\n      TNewClientContext,\n      TServerFnResponseType\n    >,\n  ) => MiddlewareAfterClient<\n    TMiddlewares,\n    TValidator,\n    TSendServerContext,\n    TNewClientContext,\n    ServerFnResponseType\n  >\n}\n\nexport interface MiddlewareAfterMiddleware<\n  TMiddlewares,\n  TServerFnResponseType extends ServerFnResponseType,\n> extends MiddlewareWithTypes<\n      TMiddlewares,\n      undefined,\n      undefined,\n      undefined,\n      undefined,\n      undefined,\n      TServerFnResponseType\n    >,\n    MiddlewareServer<\n      TMiddlewares,\n      undefined,\n      undefined,\n      undefined,\n      TServerFnResponseType\n    >,\n    MiddlewareClient<TMiddlewares, undefined, TServerFnResponseType>,\n    MiddlewareValidator<TMiddlewares, TServerFnResponseType> {}\n\nexport interface Middleware<TServerFnResponseType extends ServerFnResponseType>\n  extends MiddlewareAfterMiddleware<unknown, TServerFnResponseType> {\n  middleware: <const TNewMiddlewares = undefined>(\n    middlewares: Constrain<TNewMiddlewares, ReadonlyArray<AnyMiddleware>>,\n  ) => MiddlewareAfterMiddleware<TNewMiddlewares, TServerFnResponseType>\n}\n\nexport function createMiddleware(\n  options?: {\n    validateClient?: boolean\n  },\n  __opts?: MiddlewareOptions<\n    unknown,\n    undefined,\n    undefined,\n    undefined,\n    ServerFnResponseType\n  >,\n): Middleware<ServerFnResponseType> {\n  // const resolvedOptions = (__opts || options) as MiddlewareOptions<\n  const resolvedOptions =\n    __opts ||\n    ((options || {}) as MiddlewareOptions<\n      unknown,\n      undefined,\n      undefined,\n      undefined,\n      ServerFnResponseType\n    >)\n\n  return {\n    options: resolvedOptions as any,\n    middleware: (middleware: any) => {\n      return createMiddleware(\n        undefined,\n        Object.assign(resolvedOptions, { middleware }),\n      ) as any\n    },\n    validator: (validator: any) => {\n      return createMiddleware(\n        undefined,\n        Object.assign(resolvedOptions, { validator }),\n      ) as any\n    },\n    client: (client: any) => {\n      return createMiddleware(\n        undefined,\n        Object.assign(resolvedOptions, { client }),\n      ) as any\n    },\n    server: (server: any) => {\n      return createMiddleware(\n        undefined,\n        Object.assign(resolvedOptions, { server }),\n      ) as any\n    },\n  } as unknown as Middleware<ServerFnResponseType>\n}\n", "import { isPlainObject } from '@tanstack/router-core'\n\nimport invariant from 'tiny-invariant'\n\nimport { startSerializer } from './serializer'\nimport type {\n  Any<PERSON><PERSON>er,\n  ControllablePromise,\n  DeferredPromiseState,\n  MakeRouteMatch,\n  Manifest,\n  RouteContextOptions,\n} from '@tanstack/router-core'\n\ndeclare global {\n  interface Window {\n    __TSR_SSR__?: StartSsrGlobal\n  }\n}\n\nexport interface StartSsrGlobal {\n  matches: Array<SsrMatch>\n  streamedValues: Record<\n    string,\n    {\n      value: any\n      parsed: any\n    }\n  >\n  cleanScripts: () => void\n  dehydrated?: any\n  initMatch: (match: SsrMatch) => void\n  resolvePromise: (opts: {\n    matchId: string\n    id: number\n    promiseState: DeferredPromiseState<any>\n  }) => void\n  injectChunk: (opts: { matchId: string; id: number; chunk: string }) => void\n  closeStream: (opts: { matchId: string; id: number }) => void\n}\n\nexport interface SsrMatch {\n  id: string\n  __beforeLoadContext: string\n  loaderData?: string\n  error?: string\n  extracted?: Array<ClientExtractedEntry>\n  updatedAt: MakeRouteMatch['updatedAt']\n  status: MakeRouteMatch['status']\n}\n\nexport type ClientExtractedEntry =\n  | ClientExtractedStream\n  | ClientExtractedPromise\n\nexport interface ClientExtractedPromise extends ClientExtractedBaseEntry {\n  type: 'promise'\n  value?: ControllablePromise<any>\n}\n\nexport interface ClientExtractedStream extends ClientExtractedBaseEntry {\n  type: 'stream'\n  value?: ReadableStream & { controller?: ReadableStreamDefaultController }\n}\n\nexport interface ClientExtractedBaseEntry {\n  type: string\n  path: Array<string>\n}\n\nexport interface ResolvePromiseState {\n  matchId: string\n  id: number\n  promiseState: DeferredPromiseState<any>\n}\n\nexport interface DehydratedRouter {\n  manifest: Manifest | undefined\n  dehydratedData: any\n}\n\nexport function hydrate(router: AnyRouter) {\n  invariant(\n    window.__TSR_SSR__?.dehydrated,\n    'Expected to find a dehydrated data on window.__TSR_SSR__.dehydrated... but we did not. Please file an issue!',\n  )\n\n  const { manifest, dehydratedData } = startSerializer.parse(\n    window.__TSR_SSR__.dehydrated,\n  ) as DehydratedRouter\n\n  router.ssr = {\n    manifest,\n    serializer: startSerializer,\n  }\n\n  router.clientSsr = {\n    getStreamedValue: <T,>(key: string): T | undefined => {\n      if (router.isServer) {\n        return undefined\n      }\n\n      const streamedValue = window.__TSR_SSR__?.streamedValues[key]\n\n      if (!streamedValue) {\n        return\n      }\n\n      if (!streamedValue.parsed) {\n        streamedValue.parsed = router.ssr!.serializer.parse(streamedValue.value)\n      }\n\n      return streamedValue.parsed\n    },\n  }\n\n  // Hydrate the router state\n  const matches = router.matchRoutes(router.state.location)\n  // kick off loading the route chunks\n  const routeChunkPromise = Promise.all(\n    matches.map((match) => {\n      const route = router.looseRoutesById[match.routeId]!\n      return router.loadRouteChunk(route)\n    }),\n  )\n  // Right after hydration and before the first render, we need to rehydrate each match\n  // First step is to reyhdrate loaderData and __beforeLoadContext\n  matches.forEach((match) => {\n    const dehydratedMatch = window.__TSR_SSR__!.matches.find(\n      (d) => d.id === match.id,\n    )\n\n    if (dehydratedMatch) {\n      Object.assign(match, dehydratedMatch)\n\n      // Handle beforeLoadContext\n      if (dehydratedMatch.__beforeLoadContext) {\n        match.__beforeLoadContext = router.ssr!.serializer.parse(\n          dehydratedMatch.__beforeLoadContext,\n        ) as any\n      }\n\n      // Handle loaderData\n      if (dehydratedMatch.loaderData) {\n        match.loaderData = router.ssr!.serializer.parse(\n          dehydratedMatch.loaderData,\n        )\n      }\n\n      // Handle error\n      if (dehydratedMatch.error) {\n        match.error = router.ssr!.serializer.parse(dehydratedMatch.error)\n      }\n\n      // Handle extracted\n      ;(match as unknown as SsrMatch).extracted?.forEach((ex) => {\n        deepMutableSetByPath(match, ['loaderData', ...ex.path], ex.value)\n      })\n    } else {\n      Object.assign(match, {\n        status: 'success',\n        updatedAt: Date.now(),\n      })\n    }\n\n    return match\n  })\n\n  router.__store.setState((s) => {\n    return {\n      ...s,\n      matches,\n    }\n  })\n\n  // Allow the user to handle custom hydration data\n  router.options.hydrate?.(dehydratedData)\n\n  // now that all necessary data is hydrated:\n  // 1) fully reconstruct the route context\n  // 2) execute `head()` and `scripts()` for each match\n  router.state.matches.forEach((match) => {\n    const route = router.looseRoutesById[match.routeId]!\n\n    const parentMatch = router.state.matches[match.index - 1]\n    const parentContext = parentMatch?.context ?? router.options.context ?? {}\n\n    // `context()` was already executed by `matchRoutes`, however route context was not yet fully reconstructed\n    // so run it again and merge route context\n    const contextFnContext: RouteContextOptions<any, any, any, any> = {\n      deps: match.loaderDeps,\n      params: match.params,\n      context: parentContext,\n      location: router.state.location,\n      navigate: (opts: any) =>\n        router.navigate({ ...opts, _fromLocation: router.state.location }),\n      buildLocation: router.buildLocation,\n      cause: match.cause,\n      abortController: match.abortController,\n      preload: false,\n      matches,\n    }\n    match.__routeContext = route.options.context?.(contextFnContext) ?? {}\n\n    match.context = {\n      ...parentContext,\n      ...match.__routeContext,\n      ...match.__beforeLoadContext,\n    }\n\n    const assetContext = {\n      matches: router.state.matches,\n      match,\n      params: match.params,\n      loaderData: match.loaderData,\n    }\n    const headFnContent = route.options.head?.(assetContext)\n\n    const scripts = route.options.scripts?.(assetContext)\n\n    match.meta = headFnContent?.meta\n    match.links = headFnContent?.links\n    match.headScripts = headFnContent?.scripts\n    match.scripts = scripts\n  })\n\n  return routeChunkPromise\n}\n\nfunction deepMutableSetByPath<T>(obj: T, path: Array<string>, value: any) {\n  // mutable set by path retaining array and object references\n  if (path.length === 1) {\n    ;(obj as any)[path[0]!] = value\n  }\n\n  const [key, ...rest] = path\n\n  if (Array.isArray(obj)) {\n    deepMutableSetByPath(obj[Number(key)], rest, value)\n  } else if (isPlainObject(obj)) {\n    deepMutableSetByPath((obj as any)[key!], rest, value)\n  }\n}\n"], "mappings": ";;;;;;;;AA6MA,SAAS,qBAAqB,eAAe;AAC3C,MAAI,MAAM,QAAQ,aAAa,GAAG;AAChC,WAAO,cAAc,QAAQ,CAAC,MAAM,qBAAqB,CAAC,CAAC;AAAA,EAC7D;AACA,MAAI,OAAO,kBAAkB,UAAU;AACrC,WAAO,CAAC;AAAA,EACV;AACA,QAAM,iBAAiB,CAAC;AACxB,MAAI,MAAM;AACV,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,QAAM,iBAAiB,MAAM;AAC3B,WAAO,MAAM,cAAc,UAAU,KAAK,KAAK,cAAc,OAAO,GAAG,CAAC,GAAG;AACzE,aAAO;AAAA,IACT;AACA,WAAO,MAAM,cAAc;AAAA,EAC7B;AACA,QAAM,iBAAiB,MAAM;AAC3B,SAAK,cAAc,OAAO,GAAG;AAC7B,WAAO,OAAO,OAAO,OAAO,OAAO,OAAO;AAAA,EAC5C;AACA,SAAO,MAAM,cAAc,QAAQ;AACjC,YAAQ;AACR,4BAAwB;AACxB,WAAO,eAAe,GAAG;AACvB,WAAK,cAAc,OAAO,GAAG;AAC7B,UAAI,OAAO,KAAK;AACd,oBAAY;AACZ,eAAO;AACP,uBAAe;AACf,oBAAY;AACZ,eAAO,MAAM,cAAc,UAAU,eAAe,GAAG;AACrD,iBAAO;AAAA,QACT;AACA,YAAI,MAAM,cAAc,UAAU,cAAc,OAAO,GAAG,MAAM,KAAK;AACnE,kCAAwB;AACxB,gBAAM;AACN,yBAAe,KAAK,cAAc,MAAM,OAAO,SAAS,CAAC;AACzD,kBAAQ;AAAA,QACV,OAAO;AACL,gBAAM,YAAY;AAAA,QACpB;AAAA,MACF,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI,CAAC,yBAAyB,OAAO,cAAc,QAAQ;AACzD,qBAAe,KAAK,cAAc,MAAM,OAAO,cAAc,MAAM,CAAC;AAAA,IACtE;AAAA,EACF;AACA,SAAO;AACT;;;AC5OA,SAAS,kBAAkB,MAAkB;AAC3C,MAAI,gBAAgB,SAAS;AACpB,WAAA,IAAI,QAAQ,IAAI;EACd,WAAA,MAAM,QAAQ,IAAI,GAAG;AACvB,WAAA,IAAI,QAAQ,IAAI;EAAA,WACd,OAAO,SAAS,UAAU;AAC5B,WAAA,IAAI,QAAQ,IAAmB;EAAA,OACjC;AACL,WAAO,IAAI,QAAQ;EAAA;AAEvB;AAGO,SAAS,gBAAgB,SAA4B;AAC1D,SAAO,QAAQ,OAAO,CAAC,KAAc,WAAW;AACxC,UAAA,kBAAkB,kBAAkB,MAAM;AAChD,eAAW,CAAC,KAAK,KAAK,KAAK,gBAAgB,QAAA,GAAW;AACpD,UAAI,QAAQ,cAAc;AAClB,cAAA,eAAe,qBAAqB,KAAK;AAC/C,qBAAa,QAAQ,CAAC,WAAW,IAAI,OAAO,cAAc,MAAM,CAAC;MAAA,OAC5D;AACD,YAAA,IAAI,KAAK,KAAK;MAAA;IACpB;AAEK,WAAA;EAAA,GACN,IAAI,QAAA,CAAS;AAClB;;;AC9CO,IAAM,kBAAmC;EAC9C,WAAW,CAAC,UACV,KAAK,UAAU,OAAO,SAAS,SAAS,KAAK,KAAK;AAC1C,UAAA,QAAQ,KAAK,GAAG;AAChB,UAAA,aAAa,YAAY,KAAK,CAAC,MAAM,EAAE,mBAAmB,KAAK,CAAC;AAEtE,QAAI,YAAY;AACP,aAAA,WAAW,UAAU,KAAK;IAAA;AAG5B,WAAA;EAAA,CACR;EACH,OAAO,CAAC,UACN,KAAK,MAAM,OAAO,SAAS,OAAO,KAAK,KAAK;AACpC,UAAA,QAAQ,KAAK,GAAG;AAClB,QAAA,cAAc,KAAK,GAAG;AAClB,YAAA,aAAa,YAAY,KAAK,CAAC,MAAM,EAAE,eAAe,KAAK,CAAC;AAElE,UAAI,YAAY;AACP,eAAA,WAAW,MAAM,KAAK;MAAA;IAC/B;AAGK,WAAA;EAAA,CACR;EACH,QAAQ,CAAC,UAAe;AAElB,QAAA,MAAM,QAAQ,KAAK,GAAG;AACxB,aAAO,MAAM,IAAI,CAAC,MAAM,gBAAgB,OAAO,CAAC,CAAC;IAAA;AAG/C,QAAA,cAAc,KAAK,GAAG;AACxB,aAAO,OAAO;QACZ,OAAO,QAAQ,KAAK,EAAE,IAAI,CAAC,CAAC,KAAK,CAAC,MAAM;UACtC;UACA,gBAAgB,OAAO,CAAC;QACzB,CAAA;MACH;IAAA;AAGI,UAAA,aAAa,YAAY,KAAK,CAAC,MAAM,EAAE,mBAAmB,KAAK,CAAC;AACtE,QAAI,YAAY;AACP,aAAA,WAAW,UAAU,KAAK;IAAA;AAG5B,WAAA;EACT;EACA,QAAQ,CAAC,UAAe;AAElB,QAAA,cAAc,KAAK,GAAG;AAClB,YAAA,aAAa,YAAY,KAAK,CAAC,MAAM,EAAE,eAAe,KAAK,CAAC;AAClE,UAAI,YAAY;AACP,eAAA,WAAW,MAAM,KAAK;MAAA;IAC/B;AAGE,QAAA,MAAM,QAAQ,KAAK,GAAG;AACxB,aAAO,MAAM,IAAI,CAAC,MAAM,gBAAgB,OAAO,CAAC,CAAC;IAAA;AAG/C,QAAA,cAAc,KAAK,GAAG;AACxB,aAAO,OAAO;QACZ,OAAO,QAAQ,KAAK,EAAE,IAAI,CAAC,CAAC,KAAK,CAAC,MAAM;UACtC;UACA,gBAAgB,OAAO,CAAC;QACzB,CAAA;MACH;IAAA;AAGK,WAAA;EAAA;AAEX;AAEA,IAAM,mBAAmB,CACvB,KACA,OACA,SACA,eACI;EACJ;EACA,oBAAoB;EACpB,WAAW,CAAC,WAAgB,EAAE,CAAC,IAAI,GAAG,EAAE,GAAG,QAAQ,KAAK,EAAA;EACxD,gBAAgB,CAAC,UAAe,OAAO,OAAO,OAAO,IAAI,GAAG,EAAE;EAC9D,OAAO,CAAC,UAAe,UAAU,MAAM,IAAI,GAAG,EAAE,CAAC;AACnD;AAKA,IAAM,cAAc;EAClB;;IAEE;;IAEA,CAAC,MAAsB,MAAM;;IAE7B,MAAM;;IAEN,MAAM;EACR;EACA;;IAEE;;IAEA,CAAC,MAAiB,aAAa;;IAE/B,CAAC,MAAM,EAAE,YAAY;;IAErB,CAAC,MAAM,IAAI,KAAK,CAAC;EACnB;EACA;;IAEE;;IAEA,CAAC,MAAkB,aAAa;;IAEhC,CAAC,OAAO;MACN,GAAG;MACH,SAAS,EAAE;MACX,OAAO,OAAyC,EAAE,QAAQ;MAC1D,OAAO,EAAE;IAAA;;IAGX,CAAC,MAAM,OAAO,OAAO,IAAI,MAAM,EAAE,OAAO,GAAG,CAAC;EAC9C;EACA;;IAEE;;IAEA,CAAC,MAAqB,aAAa;;IAEnC,CAAC,MAAM;AACL,YAAM,UAGF,CAAC;AACH,QAAA,QAAQ,CAAC,OAAO,QAAQ;AAClB,cAAA,QAAQ,QAAQ,GAAG;AACzB,YAAI,UAAU,QAAW;AACnB,cAAA,MAAM,QAAQ,KAAK,GAAG;AACxB,kBAAM,KAAK,KAAK;UAAA,OACX;AACL,oBAAQ,GAAG,IAAI,CAAC,OAAO,KAAK;UAAA;QAC9B,OACK;AACL,kBAAQ,GAAG,IAAI;QAAA;MACjB,CACD;AACM,aAAA;IACT;;IAEA,CAAC,MAAM;AACC,YAAA,WAAW,IAAI,SAAS;AACvB,aAAA,QAAQ,CAAC,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AACtC,YAAA,MAAM,QAAQ,KAAK,GAAG;AACxB,gBAAM,QAAQ,CAAC,QAAQ,SAAS,OAAO,KAAK,GAAG,CAAC;QAAA,OAC3C;AACI,mBAAA,OAAO,KAAK,KAAK;QAAA;MAC5B,CACD;AACM,aAAA;IAAA;EAEX;EACA;;IAEE;;IAEA,CAAC,MAAmB,OAAO,MAAM;;IAEjC,CAAC,MAAM,EAAE,SAAS;;IAElB,CAAC,MAAM,OAAO,CAAC;EAAA;AAEnB;;;AC/IO,SAAS,qBAAuC;AAC9C,SAAA;AACT;;;AC/Ba,IAAA,aAAwB,CAAC,OAAO;AAIhC,IAAA,aAAwB,CAAC,OAAO;;;ACNtC,IAAM,mBAAyC,CAAA;AAE/C,SAAS,yBAAyB,SAEtC;AACgB,mBAAA,KAAK,GAAG,QAAQ,UAAU;AAC7C;;;ACmBgB,SAAA,eAOd,SAKA,QAOiD;AAC3C,QAAA,kBAAmB,UAAU,WAAW,CAAC;AAQ3C,MAAA,OAAO,gBAAgB,WAAW,aAAa;AACjD,oBAAgB,SAAS;EAAA;AAGpB,SAAA;IACL,SAAS;IACT,YAAY,CAAC,eAAe;AACnB,aAAA,eAML,QAAW,OAAO,OAAO,iBAAiB,EAAE,WAAA,CAAY,CAAC;IAC7D;IACA,WAAW,CAAC,cAAc;AACjB,aAAA,eAML,QAAW,OAAO,OAAO,iBAAiB,EAAE,UAAA,CAAW,CAAC;IAC5D;IACA,MAAM,CAAC,SAAS;AACP,aAAA,eAML,QAAW,OAAO,OAAO,iBAAiB,EAAE,KAAA,CAAM,CAAC;IACvD;IACA,SAAS,IAAI,SAAS;AAId,YAAA,CAAC,aAAa,QAAQ,IAAI;AAahC,aAAO,OAAO,iBAAiB;QAC7B,GAAG;QACH;QACA;MAAA,CACD;AAED,YAAM,qBAAqB;QACzB,GAAI,gBAAgB,cAAc,CAAC;QACnC,yBAAyB,eAAe;MAC1C;AAKA,aAAO,OAAO;QACZ,OAAO,SAAoC;AAElC,iBAAA,kBAAkB,oBAAoB,UAAU;YACrD,GAAG;YACH,GAAG;YACH,MAAM,QAAA,OAAA,SAAA,KAAM;YACZ,SAAS,QAAA,OAAA,SAAA,KAAM;YACf,QAAQ,QAAA,OAAA,SAAA,KAAM;YACd,SAAS,CAAA;UAAC,CACX,EAAE,KAAK,CAAC,MAAM;AACT,gBAAA,gBAAgB,aAAa,QAAQ;AAChC,qBAAA;YAAA;AAEL,gBAAA,EAAE,MAAO,OAAM,EAAE;AACrB,mBAAO,EAAE;UAAA,CACV;QACH;QACA;;UAEE,GAAG;;;UAGH,iBAAiB,OAAO,OAAY,WAAwB;AAC1D,kBAAM,OACJ,iBAAiB,WAAW,uBAAuB,KAAK,IAAI;AAEzD,iBAAA,OACH,OAAO,gBAAgB,SAAS,aAC5B,gBAAgB,KAAK,IAAI,IACzB,gBAAgB;AAEtB,kBAAM,MAAM;cACV,GAAG;cACH,GAAG;cACH;YACF;AAEA,kBAAM,MAAM,MACV,kBAAkB,oBAAoB,UAAU,GAAG,EAAE;cACnD,CAAC,OAAO;;gBAEN,QAAQ,EAAE;gBACV,OAAO,EAAE;gBACT,SAAS,EAAE;cACb;YACF;AAEE,gBAAA,IAAI,SAAS,UAAU;AACrB,kBAAA;AAGJ,kBAAI,uBAAA,OAAA,SAAA,oBAAqB,SAAS;AAErB,2BAAA,MAAM,oBAAoB,QAAQ,GAAG;cAAA;AAGlD,kBAAI,CAAC,UAAU;AAEb,2BAAW,MAAM,IAAA,EACd,KAAK,CAAC,MAAM;AACJ,yBAAA;oBACL,KAAK;oBACL,OAAO;kBACT;gBAAA,CACD,EACA,MAAM,CAAC,MAAM;AACL,yBAAA;oBACL,KAAK;oBACL,OAAO;kBACT;gBAAA,CACD;AAEH,oBAAI,uBAAA,OAAA,SAAA,oBAAqB,SAAS;AAC1B,wBAAA,oBAAoB,QAAQ,KAAK,QAAQ;gBAAA;cACjD;AAGF;gBACE;gBACA;cACF;AAEA,kBAAI,SAAS,OAAO;AAClB,sBAAM,SAAS;cAAA;AAGjB,qBAAO,SAAS;YAAA;AAGlB,mBAAO,IAAI;UAAA;QACb;MAEJ;IAAA;EAEJ;AACF;AAEA,eAAe,kBACb,aACA,KACA,MACmC;AACnC,QAAM,uBAAuB,mBAAmB;IAC9C,GAAG;IACH,GAAG;EAAA,CACJ;AAEK,QAAA,OAAe,OAAO,QAAQ;AAE5B,UAAA,iBAAiB,qBAAqB,MAAM;AAGlD,QAAI,CAAC,gBAAgB;AACZ,aAAA;IAAA;AAIP,QAAA,eAAe,QAAQ,cACtB,QAAQ,WAAW,eAAe,QAAQ,iBAAiB,OAC5D;AAEA,UAAI,OAAO,MAAM,cAAc,eAAe,QAAQ,WAAW,IAAI,IAAI;IAAA;AAG3E,UAAM,eACJ,QAAQ,WACJ,eAAe,QAAQ,SACvB,eAAe,QAAQ;AAG7B,QAAI,cAAc;AAEhB,aAAO,gBAAgB,cAAc,KAAK,OAAO,WAAW;AAC1D,eAAO,KAAK,MAAM,EAAE,MAAM,CAAC,UAAe;AACxC,cAAI,WAAW,KAAK,KAAK,WAAW,KAAK,GAAG;AACnC,mBAAA;cACL,GAAG;cACH;YACF;UAAA;AAGI,gBAAA;QAAA,CACP;MAAA,CACF;IAAA;AAGH,WAAO,KAAK,GAAG;EACjB;AAGA,SAAO,KAAK;IACV,GAAG;IACH,SAAS,KAAK,WAAW,CAAC;IAC1B,aAAa,KAAK,eAAe,CAAC;IAClC,SAAS,KAAK,WAAW,CAAA;EAAC,CAC3B;AACH;AA4WW,IAAA;AAEJ,SAAS,uBACd,OACA;AACA,QAAM,gBAAgB;AACtB,wBAAsB,OAAO,UAAU,aAAa,MAAU,IAAA;AAE9D,SAAO,MAAM;AACW,0BAAA;EACxB;AACF;AAEO,SAAS,0BACdA,sBACA;AACOA,SAAAA;AACT;AAkBA,eAAe,SAAS,SAAkC;AAExD,QAAM,YAAY,IAAI,YAAA,EAAc,OAAO,OAAO;AAGlD,QAAM,aAAa,MAAM,OAAO,OAAO,OAAO,SAAS,SAAS;AAGhE,QAAM,YAAY,MAAM,KAAK,IAAI,WAAW,UAAU,CAAC;AACvD,QAAM,UAAU,UAAU,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC,EAAE,KAAK,EAAE;AACtE,SAAA;AACT;AAEA,uBAAuB,MAAM;AACrB,QAAA,oBAAoB,OACxB,SACA,SACG;AACG,UAAA,WAAW,MAAM,SAAS,GAAG,QAAQ,UAAU,KAAK,IAAI,EAAE;AAChE,WAAO,8BAA8B,QAAQ;EAC/C;AAEM,QAAA,2BAA2B,CAACC,UAAc;AAExC,UAAA,qBAAqB,CAAC,KAAa,UACvC,SAAS,OAAO,UAAU,YAAY,CAAC,MAAM,QAAQ,KAAK,IACtD,OAAO,KAAK,KAAK,EACd,KAAA,EACA,OAAO,CAAC,KAAU,SAAiB;AAC9B,UAAA,IAAI,IAAI,MAAM,IAAI;AACf,aAAA;IAAA,GACN,CAAA,CAAE,IACP;AAGN,UAAM,aAAa,KAAK,UAAUA,SAAQ,IAAI,kBAAkB;AAGhE,WAAO,WACJ,QAAQ,kBAAkB,GAAG,EAC7B,QAAQ,QAAQ,GAAG;EACxB;AAEA,QAAM,oBACJ,OAAO,aAAa,cAAc,oBAAI,IAAqB,IAAA;AAE7D,SAAO,0BAA0B;IAC/B,SAAS,OAAO,QAAQ;AAClB,UAAA,OAAO,aAAa,aAAa;AAC7B,cAAA,OAAO,yBAAyB,IAAI,IAAI;AAC9C,cAAM,MAAM,MAAM,kBAAkB,KAAK,IAAI;AACvC,cAAA,YAAY,QAAQ,IAAI;AAG9B,cAAM,EAAE,UAAU,GAAA,IAAO,MAAM,OAAO,uBAAS;AACzC,cAAA,OAAO,MAAM,OAAO,yBAAW;AACrC,cAAM,WAAW,KAAK,KAAK,WAAW,GAAG;AAEzC,cAAM,CAAC,cAAc,SAAS,IAAI,MAAM,GACrC,SAAS,UAAU,OAAO,EAC1B,KAAK,CAAC,MAAM;UACX,gBAAgB,MAAM,CAAC;UAIvB;QAAA,CACD,EACA,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAErB,YAAA,aAAa,UAAU,SAAS,UAAU;AACtC,gBAAA;QAAA;AAGD,eAAA;MAAA;AAGF,aAAA;IACT;IACA,SAAS,OAAO,KAAK,aAAa;AAChC,YAAM,EAAE,UAAU,GAAA,IAAO,MAAM,OAAO,uBAAS;AACzC,YAAA,OAAO,MAAM,OAAO,yBAAW;AAE/B,YAAA,OAAO,yBAAyB,IAAI,IAAI;AAC9C,YAAM,MAAM,MAAM,kBAAkB,KAAK,IAAI;AACvC,YAAA,YAAY,QAAQ,IAAI;AAC9B,YAAM,WAAW,KAAK,KAAK,WAAW,GAAG;AAGnC,YAAA,GAAG,MAAM,KAAK,QAAQ,QAAQ,GAAG,EAAE,WAAW,KAAA,CAAM;AAG1D,YAAM,GAAG,UAAU,UAAU,gBAAgB,UAAU,QAAQ,CAAC;IAClE;IACA,WAAW,OAAO,QAAQ;AAClB,YAAA,OAAO,yBAAyB,IAAI,IAAI;AAC9C,YAAM,MAAM,MAAM,kBAAkB,KAAK,IAAI;AAEzC,UAAA,SAAc,qBAAA,OAAA,SAAA,kBAAmB,IAAI,GAAA;AAEzC,UAAI,CAAC,QAAQ;AACF,iBAAA,MAAM,MAAM,KAAK;UACxB,QAAQ;QACT,CAAA,EACE,KAAK,CAAC,MAAM,EAAE,KAAM,CAAA,EACpB,KAAK,CAAC,MAAM,gBAAgB,MAAM,CAAC,CAAC;AAEpB,6BAAA,OAAA,SAAA,kBAAA,IAAI,KAAK,MAAA;MAAM;AAG7B,aAAA;IAAA;EACT,CACD;AACH,CAAC;AAEM,SAAS,uBAAuB,UAAoB;AACnD,QAAA,oBAAoB,SAAS,IAAI,eAAe;AACtD,WAAS,OAAO,eAAe;AAE3B,MAAA,OAAO,sBAAsB,UAAU;AAClC,WAAA;MACL,SAAS,CAAC;MACV,MAAM;IACR;EAAA;AAGE,MAAA;AACI,UAAA,UAAU,gBAAgB,MAAM,iBAAiB;AAChD,WAAA;MACL;MACA,MAAM;IACR;EAAA,QACM;AACC,WAAA;MACL,MAAM;IACR;EAAA;AAEJ;AAEO,SAAS,mBACd,aACsB;AAChB,QAAA,OAAA,oBAAW,IAAmB;AACpC,QAAM,YAAkC,CAAC;AAEnC,QAAA,UAAU,CAAC,eAAqC;AACzC,eAAA,QAAQ,CAAC,MAAM;AACpB,UAAA,EAAE,QAAQ,YAAY;AAChB,gBAAA,EAAE,QAAQ,UAAU;MAAA;AAG9B,UAAI,CAAC,KAAK,IAAI,CAAC,GAAG;AAChB,aAAK,IAAI,CAAC;AACV,kBAAU,KAAK,CAAC;MAAA;IAClB,CACD;EACH;AAEA,UAAQ,WAAW;AAEZ,SAAA;AACT;AA8BO,IAAM,kBAAkB,OAC7B,cACA,KACA,WACG;AACH,SAAO,aAAa;IAClB,GAAG;IACH,MAAO,OACL,UAAgD,CAAA,MAC7C;AAEH,aAAO,OAAO;QACZ,GAAG;QACH,GAAG;QACH,SAAS;UACP,GAAG,IAAI;UACP,GAAG,QAAQ;QACb;QACA,aAAa;UACX,GAAG,IAAI;UACP,GAAI,QAAQ,eAAe,CAAA;QAC7B;QACA,SAAS,aAAa,IAAI,SAAS,QAAQ,OAAO;QAClD,QACE,QAAQ,WAAW,SACf,QAAQ,SACR,IAAI,aAAa,QACf,UACC,IAAY;QACrB,OAAO,QAAQ,SAAU,IAAY;MAAA,CACtC;IAAA;EACH,CACM;AACV;AAEgB,SAAA,cACd,WACA,OACS;AACL,MAAA,aAAa,KAAM,QAAO,CAAC;AAE/B,MAAI,eAAe,WAAW;AAC5B,UAAM,SAAS,UAAU,WAAW,EAAE,SAAS,KAAK;AAEpD,QAAI,kBAAkB;AACd,YAAA,IAAI,MAAM,gCAAgC;AAElD,QAAI,OAAO;AACH,YAAA,IAAI,MAAM,KAAK,UAAU,OAAO,QAAQ,QAAW,CAAC,CAAC;AAE7D,WAAO,OAAO;EAAA;AAGhB,MAAI,WAAW,WAAW;AACjB,WAAA,UAAU,MAAM,KAAK;EAAA;AAG1B,MAAA,OAAO,cAAc,YAAY;AACnC,WAAO,UAAU,KAAK;EAAA;AAGlB,QAAA,IAAI,MAAM,yBAAyB;AAC3C;AAEO,SAAS,yBACd,SACe;AACR,SAAA;IACL,QAAQ;IACR,SAAS;MACP,WAAW,QAAQ;MACnB,gBAAgB,QAAQ;MACxB,QAAQ,OAAO,EAAE,MAAM,aAAa,GAAG,IAAA,MAAU;;AAC/C,cAAM,UAAU;UACd,GAAG;;UAEH,SAAS;UACT,MAAM,OAAO,IAAI,SAAS,aAAa,IAAI,KAAK,GAAG,IAAI,IAAI;QAC7D;AAGE,YAAA,IAAI,SAAS,YACb,OAEA;AACA;YACE;YACA;UACF;AAEA,gBAAM,SAAS,MAAM,oBAAoB,UAAU,OAAO;AAE1D,cAAI,QAAQ;AACV,gBAAI,OAAO,OAAO;AAChB,oBAAM,OAAO;YAAA;AAGR,mBAAA,KAAK,OAAO,GAAG;UAAA;AAGxB;YACE;YACA,kCAAkC,QAAQ,UAAU,KAAK,KAAK,UAAU,QAAQ,IAAI,CAAC;UACvF;QAAA;AAKF,cAAM,MAAM,QAAM,KAAA,QAAQ,gBAAR,OAAA,SAAA,GAAA,KAAA,SAAsB,OAAA;AAExC,eAAO,KAAK,GAAG;MACjB;MACA,QAAQ,OAAO,EAAE,MAAM,GAAG,IAAA,MAAU;;AAElC,cAAM,SAAS,QAAM,KAAA,QAAQ,aAAR,OAAA,SAAA,GAAA,KAAA,SAAmB,GAAA;AAExC,eAAO,KAAK;UACV,GAAG;UACH;QAAA,CACM;MAAA;IACV;EAEJ;AACF;;;ACz9BgB,SAAA,KACd,SACA,MACqB;AACrB,SAAO,IAAI,SAAS,KAAK,UAAU,OAAO,GAAG;IAC3C,GAAG;IACH,SAAS;MACP,EAAE,gBAAgB,mBAAmB;MACrC,QAAA,OAAA,SAAA,KAAM;IAAA;EACR,CACD;AACH;;;ACkhBgB,SAAA,iBACd,SAGA,QAOkC;AAE5B,QAAA,kBACJ,WACE,WAAW,CAAA;AAQR,SAAA;IACL,SAAS;IACT,YAAY,CAAC,eAAoB;AACxB,aAAA;QACL;QACA,OAAO,OAAO,iBAAiB,EAAE,WAAY,CAAA;MAC/C;IACF;IACA,WAAW,CAAC,cAAmB;AACtB,aAAA;QACL;QACA,OAAO,OAAO,iBAAiB,EAAE,UAAW,CAAA;MAC9C;IACF;IACA,QAAQ,CAAC,WAAgB;AAChB,aAAA;QACL;QACA,OAAO,OAAO,iBAAiB,EAAE,OAAQ,CAAA;MAC3C;IACF;IACA,QAAQ,CAAC,WAAgB;AAChB,aAAA;QACL;QACA,OAAO,OAAO,iBAAiB,EAAE,OAAQ,CAAA;MAC3C;IAAA;EAEJ;AACF;;;ACjgBO,SAAS,QAAQ,QAAmB;;AACzC;KACE,KAAA,OAAO,gBAAP,OAAA,SAAA,GAAoB;IACpB;EACF;AAEA,QAAM,EAAE,UAAU,eAAe,IAAI,gBAAgB;IACnD,OAAO,YAAY;EACrB;AAEA,SAAO,MAAM;IACX;IACA,YAAY;EACd;AAEA,SAAO,YAAY;IACjB,kBAAkB,CAAK,QAA+B;;AACpD,UAAI,OAAO,UAAU;AACZ,eAAA;MAAA;AAGT,YAAM,iBAAgBC,MAAA,OAAO,gBAAP,OAAA,SAAAA,IAAoB,eAAe,GAAA;AAEzD,UAAI,CAAC,eAAe;AAClB;MAAA;AAGE,UAAA,CAAC,cAAc,QAAQ;AACzB,sBAAc,SAAS,OAAO,IAAK,WAAW,MAAM,cAAc,KAAK;MAAA;AAGzE,aAAO,cAAc;IAAA;EAEzB;AAGA,QAAM,UAAU,OAAO,YAAY,OAAO,MAAM,QAAQ;AAExD,QAAM,oBAAoB,QAAQ;IAChC,QAAQ,IAAI,CAAC,UAAU;AACrB,YAAM,QAAQ,OAAO,gBAAgB,MAAM,OAAO;AAC3C,aAAA,OAAO,eAAe,KAAK;IACnC,CAAA;EACH;AAGQ,UAAA,QAAQ,CAAC,UAAU;;AACnB,UAAA,kBAAkB,OAAO,YAAa,QAAQ;MAClD,CAAC,MAAM,EAAE,OAAO,MAAM;IACxB;AAEA,QAAI,iBAAiB;AACZ,aAAA,OAAO,OAAO,eAAe;AAGpC,UAAI,gBAAgB,qBAAqB;AACjC,cAAA,sBAAsB,OAAO,IAAK,WAAW;UACjD,gBAAgB;QAClB;MAAA;AAIF,UAAI,gBAAgB,YAAY;AACxB,cAAA,aAAa,OAAO,IAAK,WAAW;UACxC,gBAAgB;QAClB;MAAA;AAIF,UAAI,gBAAgB,OAAO;AACzB,cAAM,QAAQ,OAAO,IAAK,WAAW,MAAM,gBAAgB,KAAK;MAAA;AAIhE,OAAAA,MAAA,MAA8B,cAA9B,OAAA,SAAAA,IAAyC,QAAQ,CAAC,OAAO;AACpC,6BAAA,OAAO,CAAC,cAAc,GAAG,GAAG,IAAI,GAAG,GAAG,KAAK;MAAA,CAAA;IACjE,OACI;AACL,aAAO,OAAO,OAAO;QACnB,QAAQ;QACR,WAAW,KAAK,IAAI;MAAA,CACrB;IAAA;AAGI,WAAA;EAAA,CACR;AAEM,SAAA,QAAQ,SAAS,CAAC,MAAM;AACtB,WAAA;MACL,GAAG;MACH;IACF;EAAA,CACD;AAGM,GAAA,MAAA,KAAA,OAAA,SAAQ,YAAR,OAAA,SAAA,GAAA,KAAA,IAAkB,cAAA;AAKzB,SAAO,MAAM,QAAQ,QAAQ,CAAC,UAAU;;AACtC,UAAM,QAAQ,OAAO,gBAAgB,MAAM,OAAO;AAElD,UAAM,cAAc,OAAO,MAAM,QAAQ,MAAM,QAAQ,CAAC;AACxD,UAAM,iBAAgB,eAAA,OAAA,SAAA,YAAa,YAAW,OAAO,QAAQ,WAAW,CAAC;AAIzE,UAAM,mBAA4D;MAChE,MAAM,MAAM;MACZ,QAAQ,MAAM;MACd,SAAS;MACT,UAAU,OAAO,MAAM;MACvB,UAAU,CAAC,SACT,OAAO,SAAS,EAAE,GAAG,MAAM,eAAe,OAAO,MAAM,SAAA,CAAU;MACnE,eAAe,OAAO;MACtB,OAAO,MAAM;MACb,iBAAiB,MAAM;MACvB,SAAS;MACT;IACF;AACA,UAAM,mBAAiBC,OAAAD,MAAA,MAAM,SAAQ,YAAd,OAAA,SAAAC,IAAA,KAAAD,KAAwB,gBAAA,MAAqB,CAAC;AAErE,UAAM,UAAU;MACd,GAAG;MACH,GAAG,MAAM;MACT,GAAG,MAAM;IACX;AAEA,UAAM,eAAe;MACnB,SAAS,OAAO,MAAM;MACtB;MACA,QAAQ,MAAM;MACd,YAAY,MAAM;IACpB;AACA,UAAM,iBAAgB,MAAAE,MAAA,MAAM,SAAQ,SAAd,OAAA,SAAA,GAAA,KAAAA,KAAqB,YAAA;AAE3C,UAAM,WAAU,MAAA,KAAA,MAAM,SAAQ,YAAd,OAAA,SAAA,GAAA,KAAA,IAAwB,YAAA;AAExC,UAAM,OAAO,iBAAA,OAAA,SAAA,cAAe;AAC5B,UAAM,QAAQ,iBAAA,OAAA,SAAA,cAAe;AAC7B,UAAM,cAAc,iBAAA,OAAA,SAAA,cAAe;AACnC,UAAM,UAAU;EAAA,CACjB;AAEM,SAAA;AACT;AAEA,SAAS,qBAAwB,KAAQ,MAAqB,OAAY;AAEpE,MAAA,KAAK,WAAW,GAAG;AACnB,QAAY,KAAK,CAAC,CAAE,IAAI;EAAA;AAG5B,QAAM,CAAC,KAAK,GAAG,IAAI,IAAI;AAEnB,MAAA,MAAM,QAAQ,GAAG,GAAG;AACtB,yBAAqB,IAAI,OAAO,GAAG,CAAC,GAAG,MAAM,KAAK;EAAA,WACzC,cAAc,GAAG,GAAG;AAC7B,yBAAsB,IAAY,GAAI,GAAG,MAAM,KAAK;EAAA;AAExD;", "names": ["serverFnStaticCache", "json", "_a", "_b", "_c"]}
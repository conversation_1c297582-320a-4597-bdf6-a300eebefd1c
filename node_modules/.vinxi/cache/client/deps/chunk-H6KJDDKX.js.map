{"version": 3, "sources": ["../../../../use-sync-external-store/cjs/use-sync-external-store-shim.development.js", "../../../../use-sync-external-store/shim/index.js", "../../../../use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js", "../../../../use-sync-external-store/shim/with-selector.js", "../../../../jsesc/jsesc.js", "../../../../@tanstack/react-router/src/awaited.tsx", "../../../../@tanstack/react-router/src/CatchBoundary.tsx", "../../../../@tanstack/react-router/src/ClientOnly.tsx", "../../../../@tanstack/react-store/src/index.ts", "../../../../@tanstack/react-router/src/routerContext.tsx", "../../../../@tanstack/react-router/src/useRouter.tsx", "../../../../@tanstack/react-router/src/useRouterState.tsx", "../../../../@tanstack/react-router/src/matchContext.tsx", "../../../../@tanstack/react-router/src/useMatch.tsx", "../../../../@tanstack/react-router/src/useLoaderData.tsx", "../../../../@tanstack/react-router/src/useLoaderDeps.tsx", "../../../../@tanstack/react-router/src/useParams.tsx", "../../../../@tanstack/react-router/src/useSearch.tsx", "../../../../@tanstack/react-router/src/useNavigate.tsx", "../../../../@tanstack/react-router/src/utils.ts", "../../../../@tanstack/react-router/src/Transitioner.tsx", "../../../../@tanstack/react-router/src/not-found.tsx", "../../../../@tanstack/react-router/src/SafeFragment.tsx", "../../../../@tanstack/react-router/src/renderRouteNotFound.tsx", "../../../../@tanstack/react-router/src/ScriptOnce.tsx", "../../../../@tanstack/react-router/src/scroll-restoration.tsx", "../../../../@tanstack/react-router/src/Match.tsx", "../../../../@tanstack/react-router/src/Matches.tsx", "../../../../@tanstack/react-router/src/link.tsx", "../../../../@tanstack/react-router/src/route.tsx", "../../../../@tanstack/react-router/src/fileRoute.ts", "../../../../@tanstack/react-router/src/lazyRouteComponent.tsx", "../../../../@tanstack/react-router/src/router.ts", "../../../../@tanstack/react-router/src/RouterProvider.tsx", "../../../../@tanstack/react-router/src/ScrollRestoration.tsx", "../../../../@tanstack/react-router/src/useBlocker.tsx", "../../../../@tanstack/react-router/src/useRouteContext.ts", "../../../../@tanstack/react-router/src/useLocation.tsx", "../../../../@tanstack/react-router/src/useCanGoBack.ts", "../../../../@tanstack/react-router/src/Asset.tsx", "../../../../@tanstack/react-router/src/HeadContent.tsx", "../../../../@tanstack/react-router/src/Scripts.tsx"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    function useSyncExternalStore$2(subscribe, getSnapshot) {\n      didWarnOld18Alpha ||\n        void 0 === React.startTransition ||\n        ((didWarnOld18Alpha = !0),\n        console.error(\n          \"You are using an outdated, pre-release alpha of React 18 that does not support useSyncExternalStore. The use-sync-external-store shim will not work correctly. Upgrade to a newer pre-release.\"\n        ));\n      var value = getSnapshot();\n      if (!didWarnUncachedGetSnapshot) {\n        var cachedValue = getSnapshot();\n        objectIs(value, cachedValue) ||\n          (console.error(\n            \"The result of getSnapshot should be cached to avoid an infinite loop\"\n          ),\n          (didWarnUncachedGetSnapshot = !0));\n      }\n      cachedValue = useState({\n        inst: { value: value, getSnapshot: getSnapshot }\n      });\n      var inst = cachedValue[0].inst,\n        forceUpdate = cachedValue[1];\n      useLayoutEffect(\n        function () {\n          inst.value = value;\n          inst.getSnapshot = getSnapshot;\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n        },\n        [subscribe, value, getSnapshot]\n      );\n      useEffect(\n        function () {\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          return subscribe(function () {\n            checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          });\n        },\n        [subscribe]\n      );\n      useDebugValue(value);\n      return value;\n    }\n    function checkIfSnapshotChanged(inst) {\n      var latestGetSnapshot = inst.getSnapshot;\n      inst = inst.value;\n      try {\n        var nextValue = latestGetSnapshot();\n        return !objectIs(inst, nextValue);\n      } catch (error) {\n        return !0;\n      }\n    }\n    function useSyncExternalStore$1(subscribe, getSnapshot) {\n      return getSnapshot();\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useState = React.useState,\n      useEffect = React.useEffect,\n      useLayoutEffect = React.useLayoutEffect,\n      useDebugValue = React.useDebugValue,\n      didWarnOld18Alpha = !1,\n      didWarnUncachedGetSnapshot = !1,\n      shim =\n        \"undefined\" === typeof window ||\n        \"undefined\" === typeof window.document ||\n        \"undefined\" === typeof window.document.createElement\n          ? useSyncExternalStore$1\n          : useSyncExternalStore$2;\n    exports.useSyncExternalStore =\n      void 0 !== React.useSyncExternalStore ? React.useSyncExternalStore : shim;\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim.development.js');\n}\n", "/**\n * @license React\n * use-sync-external-store-shim/with-selector.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      shim = require(\"use-sync-external-store/shim\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useSyncExternalStore = shim.useSyncExternalStore,\n      useRef = React.useRef,\n      useEffect = React.useEffect,\n      useMemo = React.useMemo,\n      useDebugValue = React.useDebugValue;\n    exports.useSyncExternalStoreWithSelector = function (\n      subscribe,\n      getSnapshot,\n      getServerSnapshot,\n      selector,\n      isEqual\n    ) {\n      var instRef = useRef(null);\n      if (null === instRef.current) {\n        var inst = { hasValue: !1, value: null };\n        instRef.current = inst;\n      } else inst = instRef.current;\n      instRef = useMemo(\n        function () {\n          function memoizedSelector(nextSnapshot) {\n            if (!hasMemo) {\n              hasMemo = !0;\n              memoizedSnapshot = nextSnapshot;\n              nextSnapshot = selector(nextSnapshot);\n              if (void 0 !== isEqual && inst.hasValue) {\n                var currentSelection = inst.value;\n                if (isEqual(currentSelection, nextSnapshot))\n                  return (memoizedSelection = currentSelection);\n              }\n              return (memoizedSelection = nextSnapshot);\n            }\n            currentSelection = memoizedSelection;\n            if (objectIs(memoizedSnapshot, nextSnapshot))\n              return currentSelection;\n            var nextSelection = selector(nextSnapshot);\n            if (void 0 !== isEqual && isEqual(currentSelection, nextSelection))\n              return (memoizedSnapshot = nextSnapshot), currentSelection;\n            memoizedSnapshot = nextSnapshot;\n            return (memoizedSelection = nextSelection);\n          }\n          var hasMemo = !1,\n            memoizedSnapshot,\n            memoizedSelection,\n            maybeGetServerSnapshot =\n              void 0 === getServerSnapshot ? null : getServerSnapshot;\n          return [\n            function () {\n              return memoizedSelector(getSnapshot());\n            },\n            null === maybeGetServerSnapshot\n              ? void 0\n              : function () {\n                  return memoizedSelector(maybeGetServerSnapshot());\n                }\n          ];\n        },\n        [getSnapshot, getServerSnapshot, selector, isEqual]\n      );\n      var value = useSyncExternalStore(subscribe, instRef[0], instRef[1]);\n      useEffect(\n        function () {\n          inst.hasValue = !0;\n          inst.value = value;\n        },\n        [value]\n      );\n      useDebugValue(value);\n      return value;\n    };\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.development.js');\n}\n", "'use strict';\n\nconst object = {};\nconst hasOwnProperty = object.hasOwnProperty;\nconst forOwn = (object, callback) => {\n\tfor (const key in object) {\n\t\tif (hasOwnProperty.call(object, key)) {\n\t\t\tcallback(key, object[key]);\n\t\t}\n\t}\n};\n\nconst extend = (destination, source) => {\n\tif (!source) {\n\t\treturn destination;\n\t}\n\tforOwn(source, (key, value) => {\n\t\tdestination[key] = value;\n\t});\n\treturn destination;\n};\n\nconst forEach = (array, callback) => {\n\tconst length = array.length;\n\tlet index = -1;\n\twhile (++index < length) {\n\t\tcallback(array[index]);\n\t}\n};\n\nconst fourHexEscape = (hex) => {\n\treturn '\\\\u' + ('0000' + hex).slice(-4);\n}\n\nconst hexadecimal = (code, lowercase) => {\n\tlet hexadecimal = code.toString(16);\n\tif (lowercase) return hexadecimal;\n\treturn hexadecimal.toUpperCase();\n};\n\nconst toString = object.toString;\nconst isArray = Array.isArray;\nconst isBuffer = (value) => {\n\treturn typeof Buffer === 'function' && Buffer.isBuffer(value);\n};\nconst isObject = (value) => {\n\t// This is a very simple check, but it’s good enough for what we need.\n\treturn toString.call(value) == '[object Object]';\n};\nconst isString = (value) => {\n\treturn typeof value == 'string' ||\n\t\ttoString.call(value) == '[object String]';\n};\nconst isNumber = (value) => {\n\treturn typeof value == 'number' ||\n\t\ttoString.call(value) == '[object Number]';\n};\nconst isBigInt = (value) => {\n  return typeof value == 'bigint';\n};\nconst isFunction = (value) => {\n\treturn typeof value == 'function';\n};\nconst isMap = (value) => {\n\treturn toString.call(value) == '[object Map]';\n};\nconst isSet = (value) => {\n\treturn toString.call(value) == '[object Set]';\n};\n\n/*--------------------------------------------------------------------------*/\n\n// https://mathiasbynens.be/notes/javascript-escapes#single\nconst singleEscapes = {\n\t'\\\\': '\\\\\\\\',\n\t'\\b': '\\\\b',\n\t'\\f': '\\\\f',\n\t'\\n': '\\\\n',\n\t'\\r': '\\\\r',\n\t'\\t': '\\\\t'\n\t// `\\v` is omitted intentionally, because in IE < 9, '\\v' == 'v'.\n\t// '\\v': '\\\\x0B'\n};\nconst regexSingleEscape = /[\\\\\\b\\f\\n\\r\\t]/;\n\nconst regexDigit = /[0-9]/;\nconst regexWhitespace = /[\\xA0\\u1680\\u2000-\\u200A\\u2028\\u2029\\u202F\\u205F\\u3000]/;\n\nconst escapeEverythingRegex = /([\\uD800-\\uDBFF][\\uDC00-\\uDFFF])|([\\uD800-\\uDFFF])|(['\"`])|[^]/g;\nconst escapeNonAsciiRegex = /([\\uD800-\\uDBFF][\\uDC00-\\uDFFF])|([\\uD800-\\uDFFF])|(['\"`])|[^ !#-&\\(-\\[\\]-_a-~]/g;\n\nconst jsesc = (argument, options) => {\n\tconst increaseIndentation = () => {\n\t\toldIndent = indent;\n\t\t++options.indentLevel;\n\t\tindent = options.indent.repeat(options.indentLevel)\n\t};\n\t// Handle options\n\tconst defaults = {\n\t\t'escapeEverything': false,\n\t\t'minimal': false,\n\t\t'isScriptContext': false,\n\t\t'quotes': 'single',\n\t\t'wrap': false,\n\t\t'es6': false,\n\t\t'json': false,\n\t\t'compact': true,\n\t\t'lowercaseHex': false,\n\t\t'numbers': 'decimal',\n\t\t'indent': '\\t',\n\t\t'indentLevel': 0,\n\t\t'__inline1__': false,\n\t\t'__inline2__': false\n\t};\n\tconst json = options && options.json;\n\tif (json) {\n\t\tdefaults.quotes = 'double';\n\t\tdefaults.wrap = true;\n\t}\n\toptions = extend(defaults, options);\n\tif (\n\t\toptions.quotes != 'single' &&\n\t\toptions.quotes != 'double' &&\n\t\toptions.quotes != 'backtick'\n\t) {\n\t\toptions.quotes = 'single';\n\t}\n\tconst quote = options.quotes == 'double' ?\n\t\t'\"' :\n\t\t(options.quotes == 'backtick' ?\n\t\t\t'`' :\n\t\t\t'\\''\n\t\t);\n\tconst compact = options.compact;\n\tconst lowercaseHex = options.lowercaseHex;\n\tlet indent = options.indent.repeat(options.indentLevel);\n\tlet oldIndent = '';\n\tconst inline1 = options.__inline1__;\n\tconst inline2 = options.__inline2__;\n\tconst newLine = compact ? '' : '\\n';\n\tlet result;\n\tlet isEmpty = true;\n\tconst useBinNumbers = options.numbers == 'binary';\n\tconst useOctNumbers = options.numbers == 'octal';\n\tconst useDecNumbers = options.numbers == 'decimal';\n\tconst useHexNumbers = options.numbers == 'hexadecimal';\n\n\tif (json && argument && isFunction(argument.toJSON)) {\n\t\targument = argument.toJSON();\n\t}\n\n\tif (!isString(argument)) {\n\t\tif (isMap(argument)) {\n\t\t\tif (argument.size == 0) {\n\t\t\t\treturn 'new Map()';\n\t\t\t}\n\t\t\tif (!compact) {\n\t\t\t\toptions.__inline1__ = true;\n\t\t\t\toptions.__inline2__ = false;\n\t\t\t}\n\t\t\treturn 'new Map(' + jsesc(Array.from(argument), options) + ')';\n\t\t}\n\t\tif (isSet(argument)) {\n\t\t\tif (argument.size == 0) {\n\t\t\t\treturn 'new Set()';\n\t\t\t}\n\t\t\treturn 'new Set(' + jsesc(Array.from(argument), options) + ')';\n\t\t}\n\t\tif (isBuffer(argument)) {\n\t\t\tif (argument.length == 0) {\n\t\t\t\treturn 'Buffer.from([])';\n\t\t\t}\n\t\t\treturn 'Buffer.from(' + jsesc(Array.from(argument), options) + ')';\n\t\t}\n\t\tif (isArray(argument)) {\n\t\t\tresult = [];\n\t\t\toptions.wrap = true;\n\t\t\tif (inline1) {\n\t\t\t\toptions.__inline1__ = false;\n\t\t\t\toptions.__inline2__ = true;\n\t\t\t}\n\t\t\tif (!inline2) {\n\t\t\t\tincreaseIndentation();\n\t\t\t}\n\t\t\tforEach(argument, (value) => {\n\t\t\t\tisEmpty = false;\n\t\t\t\tif (inline2) {\n\t\t\t\t\toptions.__inline2__ = false;\n\t\t\t\t}\n\t\t\t\tresult.push(\n\t\t\t\t\t(compact || inline2 ? '' : indent) +\n\t\t\t\t\tjsesc(value, options)\n\t\t\t\t);\n\t\t\t});\n\t\t\tif (isEmpty) {\n\t\t\t\treturn '[]';\n\t\t\t}\n\t\t\tif (inline2) {\n\t\t\t\treturn '[' + result.join(', ') + ']';\n\t\t\t}\n\t\t\treturn '[' + newLine + result.join(',' + newLine) + newLine +\n\t\t\t\t(compact ? '' : oldIndent) + ']';\n\t\t} else if (isNumber(argument) || isBigInt(argument)) {\n\t\t\tif (json) {\n\t\t\t\t// Some number values (e.g. `Infinity`) cannot be represented in JSON.\n\t\t\t\t// `BigInt` values less than `-Number.MAX_VALUE` or greater than\n        // `Number.MAX_VALUE` cannot be represented in JSON so they will become\n        // `-Infinity` or `Infinity`, respectively, and then become `null` when\n        // stringified.\n\t\t\t\treturn JSON.stringify(Number(argument));\n\t\t\t}\n\n      let result;\n\t\t\tif (useDecNumbers) {\n\t\t\t\tresult = String(argument);\n\t\t\t} else if (useHexNumbers) {\n\t\t\t\tlet hexadecimal = argument.toString(16);\n\t\t\t\tif (!lowercaseHex) {\n\t\t\t\t\thexadecimal = hexadecimal.toUpperCase();\n\t\t\t\t}\n\t\t\t\tresult = '0x' + hexadecimal;\n\t\t\t} else if (useBinNumbers) {\n\t\t\t\tresult = '0b' + argument.toString(2);\n\t\t\t} else if (useOctNumbers) {\n\t\t\t\tresult = '0o' + argument.toString(8);\n\t\t\t}\n\n      if (isBigInt(argument)) {\n        return result + 'n';\n      }\n      return result;\n\t\t} else if (isBigInt(argument)) {\n\t\t\tif (json) {\n\t\t\t\t// `BigInt` values less than `-Number.MAX_VALUE` or greater than\n        // `Number.MAX_VALUE` will become `-Infinity` or `Infinity`,\n        // respectively, and cannot be represented in JSON.\n\t\t\t\treturn JSON.stringify(Number(argument));\n\t\t\t}\n      return argument + 'n';\n    } else if (!isObject(argument)) {\n\t\t\tif (json) {\n\t\t\t\t// For some values (e.g. `undefined`, `function` objects),\n\t\t\t\t// `JSON.stringify(value)` returns `undefined` (which isn’t valid\n\t\t\t\t// JSON) instead of `'null'`.\n\t\t\t\treturn JSON.stringify(argument) || 'null';\n\t\t\t}\n\t\t\treturn String(argument);\n\t\t} else { // it’s an object\n\t\t\tresult = [];\n\t\t\toptions.wrap = true;\n\t\t\tincreaseIndentation();\n\t\t\tforOwn(argument, (key, value) => {\n\t\t\t\tisEmpty = false;\n\t\t\t\tresult.push(\n\t\t\t\t\t(compact ? '' : indent) +\n\t\t\t\t\tjsesc(key, options) + ':' +\n\t\t\t\t\t(compact ? '' : ' ') +\n\t\t\t\t\tjsesc(value, options)\n\t\t\t\t);\n\t\t\t});\n\t\t\tif (isEmpty) {\n\t\t\t\treturn '{}';\n\t\t\t}\n\t\t\treturn '{' + newLine + result.join(',' + newLine) + newLine +\n\t\t\t\t(compact ? '' : oldIndent) + '}';\n\t\t}\n\t}\n\n\tconst regex = options.escapeEverything ? escapeEverythingRegex : escapeNonAsciiRegex;\n\tresult = argument.replace(regex, (char, pair, lone, quoteChar, index, string) => {\n\t\tif (pair) {\n\t\t\tif (options.minimal) return pair;\n\t\t\tconst first = pair.charCodeAt(0);\n\t\t\tconst second = pair.charCodeAt(1);\n\t\t\tif (options.es6) {\n\t\t\t\t// https://mathiasbynens.be/notes/javascript-encoding#surrogate-formulae\n\t\t\t\tconst codePoint = (first - 0xD800) * 0x400 + second - 0xDC00 + 0x10000;\n\t\t\t\tconst hex = hexadecimal(codePoint, lowercaseHex);\n\t\t\t\treturn '\\\\u{' + hex + '}';\n\t\t\t}\n\t\t\treturn fourHexEscape(hexadecimal(first, lowercaseHex)) + fourHexEscape(hexadecimal(second, lowercaseHex));\n\t\t}\n\n\t\tif (lone) {\n\t\t\treturn fourHexEscape(hexadecimal(lone.charCodeAt(0), lowercaseHex));\n\t\t}\n\n\t\tif (\n\t\t\tchar == '\\0' &&\n\t\t\t!json &&\n\t\t\t!regexDigit.test(string.charAt(index + 1))\n\t\t) {\n\t\t\treturn '\\\\0';\n\t\t}\n\n\t\tif (quoteChar) {\n\t\t\tif (quoteChar == quote || options.escapeEverything) {\n\t\t\t\treturn '\\\\' + quoteChar;\n\t\t\t}\n\t\t\treturn quoteChar;\n\t\t}\n\n\t\tif (regexSingleEscape.test(char)) {\n\t\t\t// no need for a `hasOwnProperty` check here\n\t\t\treturn singleEscapes[char];\n\t\t}\n\n\t\tif (options.minimal && !regexWhitespace.test(char)) {\n\t\t\treturn char;\n\t\t}\n\n\t\tconst hex = hexadecimal(char.charCodeAt(0), lowercaseHex);\n\t\tif (json || hex.length > 2) {\n\t\t\treturn fourHexEscape(hex);\n\t\t}\n\n\t\treturn '\\\\x' + ('00' + hex).slice(-2);\n\t});\n\n\tif (quote == '`') {\n\t\tresult = result.replace(/\\$\\{/g, '\\\\${');\n\t}\n\tif (options.isScriptContext) {\n\t\t// https://mathiasbynens.be/notes/etago\n\t\tresult = result\n\t\t\t.replace(/<\\/(script|style)/gi, '<\\\\/$1')\n\t\t\t.replace(/<!--/g, json ? '\\\\u003C!--' : '\\\\x3C!--');\n\t}\n\tif (options.wrap) {\n\t\tresult = quote + result + quote;\n\t}\n\treturn result;\n};\n\njsesc.version = '3.0.2';\n\nmodule.exports = jsesc;\n", "import * as React from 'react'\n\nimport { TSR_DEFERRED_PROMISE, defer } from '@tanstack/router-core'\nimport type { DeferredPromise } from '@tanstack/router-core'\n\nexport type AwaitOptions<T> = {\n  promise: Promise<T>\n}\n\nexport function useAwaited<T>({\n  promise: _promise,\n}: AwaitOptions<T>): [T, DeferredPromise<T>] {\n  const promise = defer(_promise)\n\n  if (promise[TSR_DEFERRED_PROMISE].status === 'pending') {\n    throw promise\n  }\n\n  if (promise[TSR_DEFERRED_PROMISE].status === 'error') {\n    throw promise[TSR_DEFERRED_PROMISE].error\n  }\n\n  return [promise[TSR_DEFERRED_PROMISE].data, promise]\n}\n\nexport function Await<T>(\n  props: AwaitOptions<T> & {\n    fallback?: React.ReactNode\n    children: (result: T) => React.ReactNode\n  },\n) {\n  const inner = <AwaitInner {...props} />\n  if (props.fallback) {\n    return <React.Suspense fallback={props.fallback}>{inner}</React.Suspense>\n  }\n  return inner\n}\n\nfunction AwaitInner<T>(\n  props: AwaitOptions<T> & {\n    fallback?: React.ReactNode\n    children: (result: T) => React.ReactNode\n  },\n): React.JSX.Element {\n  const [data] = useAwaited(props)\n\n  return props.children(data) as React.JSX.Element\n}\n", "import * as React from 'react'\nimport type { ErrorRouteComponent } from './route'\nimport type { ErrorInfo } from 'react'\n\nexport function CatchBoundary(props: {\n  getResetKey: () => number | string\n  children: React.ReactNode\n  errorComponent?: ErrorRouteComponent\n  onCatch?: (error: Error, errorInfo: ErrorInfo) => void\n}) {\n  const errorComponent = props.errorComponent ?? ErrorComponent\n\n  return (\n    <CatchBoundaryImpl\n      getResetKey={props.getResetKey}\n      onCatch={props.onCatch}\n      children={({ error, reset }) => {\n        if (error) {\n          return React.createElement(errorComponent, {\n            error,\n            reset,\n          })\n        }\n\n        return props.children\n      }}\n    />\n  )\n}\n\nclass CatchBoundaryImpl extends React.Component<{\n  getResetKey: () => number | string\n  children: (props: {\n    error: Error | null\n    reset: () => void\n  }) => React.ReactNode\n  onCatch?: (error: Error, errorInfo: ErrorInfo) => void\n}> {\n  state = { error: null } as { error: Error | null; resetKey: string }\n  static getDerivedStateFromProps(props: any) {\n    return { resetKey: props.getResetKey() }\n  }\n  static getDerivedStateFromError(error: Error) {\n    return { error }\n  }\n  reset() {\n    this.setState({ error: null })\n  }\n  componentDidUpdate(\n    prevProps: Readonly<{\n      getResetKey: () => string\n      children: (props: { error: any; reset: () => void }) => any\n      onCatch?: ((error: any, info: any) => void) | undefined\n    }>,\n    prevState: any,\n  ): void {\n    if (prevState.error && prevState.resetKey !== this.state.resetKey) {\n      this.reset()\n    }\n  }\n  componentDidCatch(error: Error, errorInfo: ErrorInfo) {\n    if (this.props.onCatch) {\n      this.props.onCatch(error, errorInfo)\n    }\n  }\n  render() {\n    // If the resetKey has changed, don't render the error\n    return this.props.children({\n      error:\n        this.state.resetKey !== this.props.getResetKey()\n          ? null\n          : this.state.error,\n      reset: () => {\n        this.reset()\n      },\n    })\n  }\n}\n\nexport function ErrorComponent({ error }: { error: any }) {\n  const [show, setShow] = React.useState(process.env.NODE_ENV !== 'production')\n\n  return (\n    <div style={{ padding: '.5rem', maxWidth: '100%' }}>\n      <div style={{ display: 'flex', alignItems: 'center', gap: '.5rem' }}>\n        <strong style={{ fontSize: '1rem' }}>Something went wrong!</strong>\n        <button\n          style={{\n            appearance: 'none',\n            fontSize: '.6em',\n            border: '1px solid currentColor',\n            padding: '.1rem .2rem',\n            fontWeight: 'bold',\n            borderRadius: '.25rem',\n          }}\n          onClick={() => setShow((d) => !d)}\n        >\n          {show ? 'Hide Error' : 'Show Error'}\n        </button>\n      </div>\n      <div style={{ height: '.25rem' }} />\n      {show ? (\n        <div>\n          <pre\n            style={{\n              fontSize: '.7em',\n              border: '1px solid red',\n              borderRadius: '.25rem',\n              padding: '.3rem',\n              color: 'red',\n              overflow: 'auto',\n            }}\n          >\n            {error.message ? <code>{error.message}</code> : null}\n          </pre>\n        </div>\n      ) : null}\n    </div>\n  )\n}\n", "import React from 'react'\n\nexport interface ClientOnlyProps {\n  /**\n   * The children to render when the JS is loaded.\n   */\n  children: React.ReactNode\n  /**\n   * The fallback component to render if the JS is not yet loaded.\n   */\n  fallback?: React.ReactNode\n}\n\n/**\n * Render the children only after the JS has loaded client-side. Use an optional\n * fallback component if the JS is not yet loaded.\n *\n * @example\n * Render a Chart component if JS loads, renders a simple FakeChart\n * component server-side or if there is no JS. The FakeChart can have only the\n * UI without the behavior or be a loading spinner or skeleton.\n *\n * ```tsx\n * return (\n *   <ClientOnly fallback={<FakeChart />}>\n *     <Chart />\n *   </ClientOnly>\n * )\n * ```\n */\nexport function ClientOnly({ children, fallback = null }: ClientOnlyProps) {\n  return useHydrated() ? (\n    <React.Fragment>{children}</React.Fragment>\n  ) : (\n    <React.Fragment>{fallback}</React.Fragment>\n  )\n}\n\n/**\n * Return a boolean indicating if the JS has been hydrated already.\n * When doing Server-Side Rendering, the result will always be false.\n * When doing Client-Side Rendering, the result will always be false on the\n * first render and true from then on. Even if a new component renders it will\n * always start with true.\n *\n * @example\n * ```tsx\n * // Disable a button that needs JS to work.\n * let hydrated = useHydrated()\n * return (\n *   <button type=\"button\" disabled={!hydrated} onClick={doSomethingCustom}>\n *     Click me\n *   </button>\n * )\n * ```\n * @returns True if the JS has been hydrated already, false otherwise.\n */\nfunction useHydrated(): boolean {\n  return React.useSyncExternalStore(\n    subscribe,\n    () => true,\n    () => false,\n  )\n}\n\nfunction subscribe() {\n  return () => {}\n}\n", "import { useSyncExternalStoreWithSelector } from 'use-sync-external-store/shim/with-selector.js'\nimport type { Derived, Store } from '@tanstack/store'\n\nexport * from '@tanstack/store'\n\n/**\n * @private\n */\nexport type NoInfer<T> = [T][T extends any ? 0 : never]\n\nexport function useStore<TState, TSelected = NoInfer<TState>>(\n  store: Store<TState, any>,\n  selector?: (state: NoInfer<TState>) => TSelected,\n): TSelected\nexport function useStore<TState, TSelected = NoInfer<TState>>(\n  store: Derived<TState, any>,\n  selector?: (state: NoInfer<TState>) => TSelected,\n): TSelected\nexport function useStore<TState, TSelected = NoInfer<TState>>(\n  store: Store<TState, any> | Derived<TState, any>,\n  selector: (state: NoInfer<TState>) => TSelected = (d) => d as any,\n): TSelected {\n  const slice = useSyncExternalStoreWithSelector(\n    store.subscribe,\n    () => store.state,\n    () => store.state,\n    selector,\n    shallow,\n  )\n\n  return slice\n}\n\nexport function shallow<T>(objA: T, objB: T) {\n  if (Object.is(objA, objB)) {\n    return true\n  }\n\n  if (\n    typeof objA !== 'object' ||\n    objA === null ||\n    typeof objB !== 'object' ||\n    objB === null\n  ) {\n    return false\n  }\n\n  if (objA instanceof Map && objB instanceof Map) {\n    if (objA.size !== objB.size) return false\n    for (const [k, v] of objA) {\n      if (!objB.has(k) || !Object.is(v, objB.get(k))) return false\n    }\n    return true\n  }\n\n  if (objA instanceof Set && objB instanceof Set) {\n    if (objA.size !== objB.size) return false\n    for (const v of objA) {\n      if (!objB.has(v)) return false\n    }\n    return true\n  }\n\n  const keysA = Object.keys(objA)\n  if (keysA.length !== Object.keys(objB).length) {\n    return false\n  }\n\n  for (let i = 0; i < keysA.length; i++) {\n    if (\n      !Object.prototype.hasOwnProperty.call(objB, keysA[i] as string) ||\n      !Object.is(objA[keysA[i] as keyof T], objB[keysA[i] as keyof T])\n    ) {\n      return false\n    }\n  }\n  return true\n}\n", "import * as React from 'react'\nimport type { AnyRouter } from '@tanstack/router-core'\n\ndeclare global {\n  interface Window {\n    __TSR_ROUTER_CONTEXT__?: React.Context<AnyRouter>\n  }\n}\n\nconst routerContext = React.createContext<AnyRouter>(null!)\n\nexport function getRouterContext() {\n  if (typeof document === 'undefined') {\n    return routerContext\n  }\n\n  if (window.__TSR_ROUTER_CONTEXT__) {\n    return window.__TSR_ROUTER_CONTEXT__\n  }\n\n  window.__TSR_ROUTER_CONTEXT__ = routerContext as any\n\n  return routerContext\n}\n", "import * as React from 'react'\nimport warning from 'tiny-warning'\nimport { getRouterContext } from './routerContext'\nimport type { AnyRouter, RegisteredRouter } from '@tanstack/router-core'\n\nexport function useRouter<TRouter extends AnyRouter = RegisteredRouter>(opts?: {\n  warn?: boolean\n}): TRouter {\n  const value = React.useContext(getRouterContext())\n  warning(\n    !((opts?.warn ?? true) && !value),\n    'useRouter must be used inside a <RouterProvider> component!',\n  )\n  return value as any\n}\n", "import { useStore } from '@tanstack/react-store'\nimport { useRef } from 'react'\nimport { replaceEqualDeep } from '@tanstack/router-core'\nimport { useRouter } from './useRouter'\nimport type {\n  AnyRouter,\n  RegisteredRouter,\n  RouterState,\n} from '@tanstack/router-core'\nimport type {\n  StructuralSharingOption,\n  ValidateSelected,\n} from './structuralSharing'\n\nexport type UseRouterStateOptions<\n  TRouter extends AnyRouter,\n  TSelected,\n  TStructuralSharing,\n> = {\n  router?: TRouter\n  select?: (\n    state: RouterState<TRouter['routeTree']>,\n  ) => ValidateSelected<TRouter, TSelected, TStructuralSharing>\n} & StructuralSharingOption<TRouter, TSelected, TStructuralSharing>\n\nexport type UseRouterStateResult<\n  TRouter extends AnyRouter,\n  TSelected,\n> = unknown extends TSelected ? RouterState<TRouter['routeTree']> : TSelected\n\nexport function useRouterState<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts?: UseRouterStateOptions<TRouter, TSelected, TStructuralSharing>,\n): UseRouterStateResult<TRouter, TSelected> {\n  const contextRouter = useRouter<TRouter>({\n    warn: opts?.router === undefined,\n  })\n  const router = opts?.router || contextRouter\n  const previousResult =\n    useRef<ValidateSelected<TRouter, TSelected, TStructuralSharing>>(undefined)\n\n  return useStore(router.__store, (state) => {\n    if (opts?.select) {\n      if (opts.structuralSharing ?? router.options.defaultStructuralSharing) {\n        const newSlice = replaceEqualDeep(\n          previousResult.current,\n          opts.select(state),\n        )\n        previousResult.current = newSlice\n        return newSlice\n      }\n      return opts.select(state)\n    }\n    return state\n  }) as UseRouterStateResult<TRouter, TSelected>\n}\n", "import * as React from 'react'\n\nexport const matchContext = React.createContext<string | undefined>(undefined)\n\n// N.B. this only exists so we can conditionally call useContext on it when we are not interested in the nearest match\nexport const dummyMatchContext = React.createContext<string | undefined>(\n  undefined,\n)\n", "import * as React from 'react'\nimport invariant from 'tiny-invariant'\nimport { useRouterState } from './useRouterState'\nimport { dummyMatchContext, matchContext } from './matchContext'\nimport type {\n  StructuralSharingOption,\n  ValidateSelected,\n} from './structuralSharing'\nimport type {\n  AnyR<PERSON>er,\n  MakeRouteMatch,\n  MakeRouteMatchUnion,\n  RegisteredRouter,\n  StrictOrFrom,\n  ThrowConstraint,\n  ThrowOrOptional,\n} from '@tanstack/router-core'\n\nexport interface UseMatchBaseOptions<\n  TRouter extends AnyRouter,\n  TFrom,\n  TStrict extends boolean,\n  TThrow extends boolean,\n  TSelected,\n  TStructuralSharing extends boolean,\n> {\n  select?: (\n    match: MakeRouteMatch<TRouter['routeTree'], TFrom, TStrict>,\n  ) => ValidateSelected<TRouter, TSelected, TStructuralSharing>\n  shouldThrow?: TThrow\n}\n\nexport type UseMatchRoute<out TFrom> = <\n  TRouter extends AnyRouter = RegisteredRouter,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts?: UseMatchBaseOptions<\n    TRouter,\n    TFrom,\n    true,\n    true,\n    TSelected,\n    TStructuralSharing\n  > &\n    StructuralSharingOption<TRouter, TSelected, TStructuralSharing>,\n) => UseMatchResult<TRouter, TFrom, true, TSelected>\n\nexport type UseMatchOptions<\n  TRouter extends AnyRouter,\n  TFrom extends string | undefined,\n  TStrict extends boolean,\n  TThrow extends boolean,\n  TSelected,\n  TStructuralSharing extends boolean,\n> = StrictOrFrom<TRouter, TFrom, TStrict> &\n  UseMatchBaseOptions<\n    TRouter,\n    TFrom,\n    TStrict,\n    TThrow,\n    TSelected,\n    TStructuralSharing\n  > &\n  StructuralSharingOption<TRouter, TSelected, TStructuralSharing>\n\nexport type UseMatchResult<\n  TRouter extends AnyRouter,\n  TFrom,\n  TStrict extends boolean,\n  TSelected,\n> = unknown extends TSelected\n  ? TStrict extends true\n    ? MakeRouteMatch<TRouter['routeTree'], TFrom, TStrict>\n    : MakeRouteMatchUnion<TRouter>\n  : TSelected\n\nexport function useMatch<\n  TRouter extends AnyRouter = RegisteredRouter,\n  const TFrom extends string | undefined = undefined,\n  TStrict extends boolean = true,\n  TThrow extends boolean = true,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts: UseMatchOptions<\n    TRouter,\n    TFrom,\n    TStrict,\n    ThrowConstraint<TStrict, TThrow>,\n    TSelected,\n    TStructuralSharing\n  >,\n): ThrowOrOptional<UseMatchResult<TRouter, TFrom, TStrict, TSelected>, TThrow> {\n  const nearestMatchId = React.useContext(\n    opts.from ? dummyMatchContext : matchContext,\n  )\n\n  const matchSelection = useRouterState({\n    select: (state: any) => {\n      const match = state.matches.find((d: any) =>\n        opts.from ? opts.from === d.routeId : d.id === nearestMatchId,\n      )\n      invariant(\n        !((opts.shouldThrow ?? true) && !match),\n        `Could not find ${opts.from ? `an active match from \"${opts.from}\"` : 'a nearest match!'}`,\n      )\n\n      if (match === undefined) {\n        return undefined\n      }\n\n      return opts.select ? opts.select(match) : match\n    },\n    structuralSharing: opts.structuralSharing,\n  } as any)\n\n  return matchSelection as any\n}\n", "import { useMatch } from './useMatch'\nimport type {\n  StructuralSharingOption,\n  ValidateSelected,\n} from './structuralSharing'\nimport type {\n  AnyRouter,\n  RegisteredRouter,\n  ResolveUseLoaderData,\n  StrictOrFrom,\n  UseLoaderDataResult,\n} from '@tanstack/router-core'\n\nexport interface UseLoaderDataBaseOptions<\n  TRouter extends AnyRouter,\n  TFrom,\n  TStrict extends boolean,\n  TSelected,\n  TStructuralSharing,\n> {\n  select?: (\n    match: ResolveUseLoaderData<TRouter, TFrom, TStrict>,\n  ) => ValidateSelected<TRouter, TSelected, TStructuralSharing>\n}\n\nexport type UseLoaderDataOptions<\n  TRouter extends AnyRouter,\n  T<PERSON>rom extends string | undefined,\n  TStrict extends boolean,\n  TSelected,\n  TStructuralSharing,\n> = StrictOrFrom<TRouter, TFrom, TStrict> &\n  UseLoaderDataBaseOptions<\n    TRouter,\n    TFrom,\n    TStrict,\n    TSelected,\n    TStructuralSharing\n  > &\n  StructuralSharingOption<TRouter, TSelected, TStructuralSharing>\n\nexport type UseLoaderDataRoute<out TId> = <\n  TRouter extends AnyRouter = RegisteredRouter,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts?: UseLoaderDataBaseOptions<\n    TRouter,\n    TId,\n    true,\n    TSelected,\n    TStructuralSharing\n  > &\n    StructuralSharingOption<TRouter, TSelected, TStructuralSharing>,\n) => UseLoaderDataResult<TRouter, TId, true, TSelected>\n\nexport function useLoaderData<\n  TRouter extends AnyRouter = RegisteredRouter,\n  const TFrom extends string | undefined = undefined,\n  TStrict extends boolean = true,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts: UseLoaderDataOptions<\n    TRouter,\n    TFrom,\n    TStrict,\n    TSelected,\n    TStructuralSharing\n  >,\n): UseLoaderDataResult<TRouter, TFrom, TStrict, TSelected> {\n  return useMatch({\n    from: opts.from!,\n    strict: opts.strict,\n    structuralSharing: opts.structuralSharing,\n    select: (s: any) => {\n      return opts.select ? opts.select(s.loaderData) : s.loaderData\n    },\n  } as any) as UseLoaderDataResult<TRouter, TFrom, TStrict, TSelected>\n}\n", "import { useMatch } from './useMatch'\nimport type {\n  StructuralSharingOption,\n  ValidateSelected,\n} from './structuralSharing'\nimport type {\n  AnyRouter,\n  RegisteredRouter,\n  ResolveUseLoaderDeps,\n  StrictOrFrom,\n  UseLoaderDepsResult,\n} from '@tanstack/router-core'\n\nexport interface UseLoaderDepsBaseOptions<\n  TRouter extends AnyRouter,\n  TFrom,\n  TSelected,\n  TStructuralSharing,\n> {\n  select?: (\n    deps: ResolveUseLoaderDeps<TRouter, TFrom>,\n  ) => ValidateSelected<TRouter, TSelected, TStructuralSharing>\n}\n\nexport type UseLoaderDepsOptions<\n  TRouter extends AnyRouter,\n  TFrom extends string | undefined,\n  TSelected,\n  TStructuralSharing,\n> = StrictOrFrom<TRouter, TFrom> &\n  UseLoaderDepsBaseOptions<TRouter, TFrom, TSelected, TStructuralSharing> &\n  StructuralSharingOption<TRouter, TSelected, TStructuralSharing>\n\nexport type UseLoaderDepsRoute<out TId> = <\n  TRouter extends AnyRouter = RegisteredRouter,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts?: UseLoaderDepsBaseOptions<TRouter, TId, TSelected, TStructuralSharing> &\n    StructuralSharingOption<TRouter, TSelected, false>,\n) => UseLoaderDepsResult<TRouter, TId, TSelected>\n\nexport function useLoaderDeps<\n  TRouter extends AnyRouter = RegisteredRouter,\n  const TFrom extends string | undefined = undefined,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts: UseLoaderDepsOptions<TRouter, TFrom, TSelected, TStructuralSharing>,\n): UseLoaderDepsResult<TRouter, TFrom, TSelected> {\n  const { select, ...rest } = opts\n  return useMatch({\n    ...rest,\n    select: (s) => {\n      return select ? select(s.loaderDeps) : s.loaderDeps\n    },\n  }) as UseLoaderDepsResult<TRouter, TFrom, TSelected>\n}\n", "import { useMatch } from './useMatch'\nimport type {\n  StructuralSharingOption,\n  ValidateSelected,\n} from './structuralSharing'\nimport type {\n  AnyRouter,\n  RegisteredRouter,\n  ResolveUseParams,\n  StrictOrFrom,\n  ThrowConstraint,\n  ThrowOrOptional,\n  UseParamsResult,\n} from '@tanstack/router-core'\n\nexport interface UseParamsBaseOptions<\n  TRouter extends AnyRouter,\n  TFrom,\n  TStrict extends boolean,\n  TThrow extends boolean,\n  TSelected,\n  TStructuralSharing,\n> {\n  select?: (\n    params: ResolveUseParams<TRouter, TFrom, TStrict>,\n  ) => ValidateSelected<TRouter, TSelected, TStructuralSharing>\n  shouldThrow?: TThrow\n}\n\nexport type UseParamsOptions<\n  TRouter extends AnyRouter,\n  TFrom extends string | undefined,\n  TStrict extends boolean,\n  TThrow extends boolean,\n  TSelected,\n  TStructuralSharing,\n> = StrictOrFrom<TRouter, TFrom, TStrict> &\n  UseParamsBaseOptions<\n    TRouter,\n    TFrom,\n    TStrict,\n    TThrow,\n    TSelected,\n    TStructuralSharing\n  > &\n  StructuralSharingOption<TRouter, TSelected, TStructuralSharing>\n\nexport type UseParamsRoute<out TFrom> = <\n  TRouter extends AnyRouter = RegisteredRouter,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts?: UseParamsBaseOptions<\n    TRouter,\n    TFrom,\n    /* TStrict */ true,\n    /* TThrow */ true,\n    TSelected,\n    TStructuralSharing\n  > &\n    StructuralSharingOption<TRouter, TSelected, TStructuralSharing>,\n) => UseParamsResult<TRouter, TFrom, true, TSelected>\n\nexport function useParams<\n  TRouter extends AnyRouter = RegisteredRouter,\n  const TFrom extends string | undefined = undefined,\n  TStrict extends boolean = true,\n  TThrow extends boolean = true,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts: UseParamsOptions<\n    TRouter,\n    TFrom,\n    TStrict,\n    ThrowConstraint<TStrict, TThrow>,\n    TSelected,\n    TStructuralSharing\n  >,\n): ThrowOrOptional<\n  UseParamsResult<TRouter, TFrom, TStrict, TSelected>,\n  TThrow\n> {\n  return useMatch({\n    from: opts.from!,\n    strict: opts.strict,\n    shouldThrow: opts.shouldThrow,\n    structuralSharing: opts.structuralSharing,\n    select: (match: any) => {\n      return opts.select ? opts.select(match.params) : match.params\n    },\n  }) as any\n}\n", "import { useMatch } from './useMatch'\nimport type {\n  StructuralSharingOption,\n  ValidateSelected,\n} from './structuralSharing'\nimport type {\n  AnyRouter,\n  RegisteredRouter,\n  ResolveUseSearch,\n  StrictOrFrom,\n  ThrowConstraint,\n  ThrowOrOptional,\n  UseSearchResult,\n} from '@tanstack/router-core'\n\nexport interface UseSearchBaseOptions<\n  TRouter extends AnyRouter,\n  TFrom,\n  TStrict extends boolean,\n  TThrow extends boolean,\n  TSelected,\n  TStructuralSharing,\n> {\n  select?: (\n    state: ResolveUseSearch<TRouter, TFrom, TStrict>,\n  ) => ValidateSelected<TRouter, TSelected, TStructuralSharing>\n  shouldThrow?: TThrow\n}\n\nexport type UseSearchOptions<\n  TRouter extends AnyRouter,\n  TFrom,\n  TStrict extends boolean,\n  TThrow extends boolean,\n  TSelected,\n  TStructuralSharing,\n> = StrictOrFrom<TRouter, TFrom, TStrict> &\n  UseSearchBaseOptions<\n    TRouter,\n    TFrom,\n    TStrict,\n    TThrow,\n    TSelected,\n    TStructuralSharing\n  > &\n  StructuralSharingOption<TRouter, TSelected, TStructuralSharing>\n\nexport type UseSearchRoute<out TFrom> = <\n  TRouter extends AnyRouter = RegisteredRouter,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts?: UseSearchBaseOptions<\n    TRouter,\n    TFrom,\n    /* TStrict */ true,\n    /* TThrow */ true,\n    TSelected,\n    TStructuralSharing\n  > &\n    StructuralSharingOption<TRouter, TSelected, TStructuralSharing>,\n) => UseSearchResult<TRouter, TFrom, true, TSelected>\n\nexport function useSearch<\n  TRouter extends AnyRouter = RegisteredRouter,\n  const TFrom extends string | undefined = undefined,\n  TStrict extends boolean = true,\n  TThrow extends boolean = true,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts: UseSearchOptions<\n    TRouter,\n    TFrom,\n    TStrict,\n    ThrowConstraint<TStrict, TThrow>,\n    TSelected,\n    TStructuralSharing\n  >,\n): ThrowOrOptional<\n  UseSearchResult<TRouter, TFrom, TStrict, TSelected>,\n  TThrow\n> {\n  return useMatch({\n    from: opts.from!,\n    strict: opts.strict,\n    shouldThrow: opts.shouldThrow,\n    structuralSharing: opts.structuralSharing,\n    select: (match: any) => {\n      return opts.select ? opts.select(match.search) : match.search\n    },\n  }) as any\n}\n", "import * as React from 'react'\nimport { useRouter } from './useRouter'\nimport type {\n  AnyRouter,\n  FromPathOption,\n  NavigateOptions,\n  RegisteredRouter,\n  UseNavigateResult,\n} from '@tanstack/router-core'\n\nexport function useNavigate<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TDefaultFrom extends string = string,\n>(_defaultOpts?: {\n  from?: FromPathOption<TRouter, TDefaultFrom>\n}): UseNavigateResult<TDefaultFrom> {\n  const { navigate } = useRouter()\n\n  return React.useCallback(\n    (options: NavigateOptions) => {\n      return navigate({\n        from: _defaultOpts?.from,\n        ...options,\n      })\n    },\n    [_defaultOpts?.from, navigate],\n  ) as UseNavigateResult<TDefaultFrom>\n}\n\nexport function Navigate<\n  TRouter extends AnyRouter = RegisteredRouter,\n  const TFrom extends string = string,\n  const TTo extends string | undefined = undefined,\n  const TMaskFrom extends string = TFrom,\n  const TMaskTo extends string = '',\n>(props: NavigateOptions<TRouter, TFrom, TTo, TMaskFrom, TMaskTo>): null {\n  const router = useRouter()\n\n  const previousPropsRef = React.useRef<NavigateOptions<\n    TRouter,\n    TFrom,\n    TTo,\n    TMaskFrom,\n    TMaskTo\n  > | null>(null)\n  React.useEffect(() => {\n    if (previousPropsRef.current !== props) {\n      router.navigate({\n        ...props,\n      })\n      previousPropsRef.current = props\n    }\n  }, [router, props])\n  return null\n}\n", "import * as React from 'react'\n\nexport function useStableCallback<T extends (...args: Array<any>) => any>(\n  fn: T,\n): T {\n  const fnRef = React.useRef(fn)\n  fnRef.current = fn\n\n  const ref = React.useRef((...args: Array<any>) => fnRef.current(...args))\n  return ref.current as T\n}\n\nexport const useLayoutEffect =\n  typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect\n\n/**\n * Taken from https://www.developerway.com/posts/implementing-advanced-use-previous-hook#part3\n */\nexport function usePrevious<T>(value: T): T | null {\n  // initialise the ref with previous and current values\n  const ref = React.useRef<{ value: T; prev: T | null }>({\n    value: value,\n    prev: null,\n  })\n\n  const current = ref.current.value\n\n  // if the value passed into hook doesn't match what we store as \"current\"\n  // move the \"current\" to the \"previous\"\n  // and store the passed value as \"current\"\n  if (value !== current) {\n    ref.current = {\n      value: value,\n      prev: current,\n    }\n  }\n\n  // return the previous value only\n  return ref.current.prev\n}\n\n/**\n * React hook to wrap `IntersectionObserver`.\n *\n * This hook will create an `IntersectionObserver` and observe the ref passed to it.\n *\n * When the intersection changes, the callback will be called with the `IntersectionObserverEntry`.\n *\n * @param ref - The ref to observe\n * @param intersectionObserverOptions - The options to pass to the IntersectionObserver\n * @param options - The options to pass to the hook\n * @param callback - The callback to call when the intersection changes\n * @returns The IntersectionObserver instance\n * @example\n * ```tsx\n * const MyComponent = () => {\n * const ref = React.useRef<HTMLDivElement>(null)\n * useIntersectionObserver(\n *  ref,\n *  (entry) => { doSomething(entry) },\n *  { rootMargin: '10px' },\n *  { disabled: false }\n * )\n * return <div ref={ref} />\n * ```\n */\nexport function useIntersectionObserver<T extends Element>(\n  ref: React.RefObject<T | null>,\n  callback: (entry: IntersectionObserverEntry | undefined) => void,\n  intersectionObserverOptions: IntersectionObserverInit = {},\n  options: { disabled?: boolean } = {},\n): IntersectionObserver | null {\n  const isIntersectionObserverAvailable = React.useRef(\n    typeof IntersectionObserver === 'function',\n  )\n\n  const observerRef = React.useRef<IntersectionObserver | null>(null)\n\n  React.useEffect(() => {\n    if (\n      !ref.current ||\n      !isIntersectionObserverAvailable.current ||\n      options.disabled\n    ) {\n      return\n    }\n\n    observerRef.current = new IntersectionObserver(([entry]) => {\n      callback(entry)\n    }, intersectionObserverOptions)\n\n    observerRef.current.observe(ref.current)\n\n    return () => {\n      observerRef.current?.disconnect()\n    }\n  }, [callback, intersectionObserverOptions, options.disabled, ref])\n\n  return observerRef.current\n}\n\n/**\n * React hook to take a `React.ForwardedRef` and returns a `ref` that can be used on a DOM element.\n *\n * @param ref - The forwarded ref\n * @returns The inner ref returned by `useRef`\n * @example\n * ```tsx\n * const MyComponent = React.forwardRef((props, ref) => {\n *  const innerRef = useForwardedRef(ref)\n *  return <div ref={innerRef} />\n * })\n * ```\n */\nexport function useForwardedRef<T>(ref?: React.ForwardedRef<T>) {\n  const innerRef = React.useRef<T>(null)\n  React.useImperativeHandle(ref, () => innerRef.current!, [])\n  return innerRef\n}\n", "import * as React from 'react'\nimport {\n  getLocationChangeInfo,\n  handleHashScroll,\n  trimPathRight,\n} from '@tanstack/router-core'\nimport { useLayoutEffect, usePrevious } from './utils'\nimport { useRouter } from './useRouter'\nimport { useRouterState } from './useRouterState'\n\nexport function Transitioner() {\n  const router = useRouter()\n  const mountLoadForRouter = React.useRef({ router, mounted: false })\n  const isLoading = useRouterState({\n    select: ({ isLoading }) => isLoading,\n  })\n\n  const [isTransitioning, setIsTransitioning] = React.useState(false)\n  // Track pending state changes\n  const hasPendingMatches = useRouterState({\n    select: (s) => s.matches.some((d) => d.status === 'pending'),\n    structuralSharing: true,\n  })\n\n  const previousIsLoading = usePrevious(isLoading)\n\n  const isAnyPending = isLoading || isTransitioning || hasPendingMatches\n  const previousIsAnyPending = usePrevious(isAnyPending)\n\n  const isPagePending = isLoading || hasPendingMatches\n  const previousIsPagePending = usePrevious(isPagePending)\n\n  if (!router.isServer) {\n    router.startTransition = (fn: () => void) => {\n      setIsTransitioning(true)\n      React.startTransition(() => {\n        fn()\n        setIsTransitioning(false)\n      })\n    }\n  }\n\n  // Subscribe to location changes\n  // and try to load the new location\n  React.useEffect(() => {\n    const unsub = router.history.subscribe(router.load)\n\n    const nextLocation = router.buildLocation({\n      to: router.latestLocation.pathname,\n      search: true,\n      params: true,\n      hash: true,\n      state: true,\n      _includeValidateSearch: true,\n    })\n\n    if (\n      trimPathRight(router.latestLocation.href) !==\n      trimPathRight(nextLocation.href)\n    ) {\n      router.commitLocation({ ...nextLocation, replace: true })\n    }\n\n    return () => {\n      unsub()\n    }\n  }, [router, router.history])\n\n  // Try to load the initial location\n  useLayoutEffect(() => {\n    if (\n      (typeof window !== 'undefined' && router.clientSsr) ||\n      (mountLoadForRouter.current.router === router &&\n        mountLoadForRouter.current.mounted)\n    ) {\n      return\n    }\n    mountLoadForRouter.current = { router, mounted: true }\n\n    const tryLoad = async () => {\n      try {\n        await router.load()\n      } catch (err) {\n        console.error(err)\n      }\n    }\n\n    tryLoad()\n  }, [router])\n\n  useLayoutEffect(() => {\n    // The router was loading and now it's not\n    if (previousIsLoading && !isLoading) {\n      router.emit({\n        type: 'onLoad', // When the new URL has committed, when the new matches have been loaded into state.matches\n        ...getLocationChangeInfo(router.state),\n      })\n    }\n  }, [previousIsLoading, router, isLoading])\n\n  useLayoutEffect(() => {\n    // emit onBeforeRouteMount\n    if (previousIsPagePending && !isPagePending) {\n      router.emit({\n        type: 'onBeforeRouteMount',\n        ...getLocationChangeInfo(router.state),\n      })\n    }\n  }, [isPagePending, previousIsPagePending, router])\n\n  useLayoutEffect(() => {\n    // The router was pending and now it's not\n    if (previousIsAnyPending && !isAnyPending) {\n      router.emit({\n        type: 'onResolved',\n        ...getLocationChangeInfo(router.state),\n      })\n\n      router.__store.setState((s) => ({\n        ...s,\n        status: 'idle',\n        resolvedLocation: s.location,\n      }))\n\n      handleHashScroll(router)\n    }\n  }, [isAnyPending, previousIsAnyPending, router])\n\n  return null\n}\n", "import * as React from 'react'\nimport { isNotFound } from '@tanstack/router-core'\nimport { CatchBoundary } from './CatchBoundary'\nimport { useRouterState } from './useRouterState'\nimport type { ErrorInfo } from 'react'\nimport type { NotFoundError } from '@tanstack/router-core'\n\nexport function CatchNotFound(props: {\n  fallback?: (error: NotFoundError) => React.ReactElement\n  onCatch?: (error: Error, errorInfo: ErrorInfo) => void\n  children: React.ReactNode\n}) {\n  // TODO: Some way for the user to programmatically reset the not-found boundary?\n  const resetKey = useRouterState({\n    select: (s) => `not-found-${s.location.pathname}-${s.status}`,\n  })\n\n  return (\n    <CatchBoundary\n      getResetKey={() => resetKey}\n      onCatch={(error, errorInfo) => {\n        if (isNotFound(error)) {\n          props.onCatch?.(error, errorInfo)\n        } else {\n          throw error\n        }\n      }}\n      errorComponent={({ error }: { error: Error }) => {\n        if (isNotFound(error)) {\n          return props.fallback?.(error)\n        } else {\n          throw error\n        }\n      }}\n    >\n      {props.children}\n    </CatchBoundary>\n  )\n}\n\nexport function DefaultGlobalNotFound() {\n  return <p>Not Found</p>\n}\n", "import * as React from 'react'\n\nexport function SafeFragment(props: any) {\n  return <>{props.children}</>\n}\n", "import * as React from 'react'\nimport warning from 'tiny-warning'\nimport { DefaultGlobalNotFound } from './not-found'\nimport type { AnyRoute, AnyRouter } from '@tanstack/router-core'\n\nexport function renderRouteNotFound(\n  router: AnyRouter,\n  route: AnyRoute,\n  data: any,\n) {\n  if (!route.options.notFoundComponent) {\n    if (router.options.defaultNotFoundComponent) {\n      return <router.options.defaultNotFoundComponent data={data} />\n    }\n\n    if (process.env.NODE_ENV === 'development') {\n      warning(\n        route.options.notFoundComponent,\n        `A notFoundError was encountered on the route with ID \"${route.id}\", but a notFoundComponent option was not configured, nor was a router level defaultNotFoundComponent configured. Consider configuring at least one of these to avoid TanStack Router's overly generic defaultNotFoundComponent (<div>Not Found<div>)`,\n      )\n    }\n\n    return <DefaultGlobalNotFound />\n  }\n\n  return <route.options.notFoundComponent data={data} />\n}\n", "import jsesc from 'jsesc'\n\nexport function ScriptOnce({\n  children,\n  log,\n}: {\n  children: string\n  log?: boolean\n  sync?: boolean\n}) {\n  if (typeof document !== 'undefined') {\n    return null\n  }\n\n  return (\n    <script\n      className=\"tsr-once\"\n      dangerouslySetInnerHTML={{\n        __html: [\n          children,\n          (log ?? true) && process.env.NODE_ENV === 'development'\n            ? `console.info(\\`Injected From Server:\n${jsesc(children.toString(), { quotes: 'backtick' })}\\`)`\n            : '',\n          'if (typeof __TSR_SSR__ !== \"undefined\") __TSR_SSR__.cleanScripts()',\n        ]\n          .filter(Boolean)\n          .join('\\n'),\n      }}\n    />\n  )\n}\n", "import {\n  defaultGetScrollRestorationKey,\n  restoreScroll,\n  storageKey,\n} from '@tanstack/router-core'\nimport { useRouter } from './useRouter'\nimport { ScriptOnce } from './ScriptOnce'\n\nexport function ScrollRestoration() {\n  const router = useRouter()\n  const getKey =\n    router.options.getScrollRestorationKey || defaultGetScrollRestorationKey\n  const userKey = getKey(router.latestLocation)\n  const resolvedKey =\n    userKey !== defaultGetScrollRestorationKey(router.latestLocation)\n      ? userKey\n      : null\n\n  if (!router.isScrollRestoring || !router.isServer) {\n    return null\n  }\n\n  return (\n    <ScriptOnce\n      children={`(${restoreScroll.toString()})(${JSON.stringify(storageKey)},${JSON.stringify(resolvedKey)}, undefined, true)`}\n      log={false}\n    />\n  )\n}\n", "import * as React from 'react'\nimport invariant from 'tiny-invariant'\nimport warning from 'tiny-warning'\nimport {\n  createControlledPromise,\n  getLocationChangeInfo,\n  isNotFound,\n  isRedirect,\n  pick,\n  rootRouteId,\n} from '@tanstack/router-core'\nimport { CatchBoundary, ErrorComponent } from './CatchBoundary'\nimport { useRouterState } from './useRouterState'\nimport { useRouter } from './useRouter'\nimport { CatchNotFound } from './not-found'\nimport { matchContext } from './matchContext'\nimport { SafeFragment } from './SafeFragment'\nimport { renderRouteNotFound } from './renderRouteNotFound'\nimport { ScrollRestoration } from './scroll-restoration'\nimport type { AnyRoute, ParsedLocation } from '@tanstack/router-core'\n\nexport const Match = React.memo(function MatchImpl({\n  matchId,\n}: {\n  matchId: string\n}) {\n  const router = useRouter()\n  const routeId = useRouterState({\n    select: (s) => s.matches.find((d) => d.id === matchId)?.routeId as string,\n  })\n\n  invariant(\n    routeId,\n    `Could not find routeId for matchId \"${matchId}\". Please file an issue!`,\n  )\n\n  const route: AnyRoute = router.routesById[routeId]\n\n  const PendingComponent =\n    route.options.pendingComponent ?? router.options.defaultPendingComponent\n\n  const pendingElement = PendingComponent ? <PendingComponent /> : null\n\n  const routeErrorComponent =\n    route.options.errorComponent ?? router.options.defaultErrorComponent\n\n  const routeOnCatch = route.options.onCatch ?? router.options.defaultOnCatch\n\n  const routeNotFoundComponent = route.isRoot\n    ? // If it's the root route, use the globalNotFound option, with fallback to the notFoundRoute's component\n      (route.options.notFoundComponent ??\n      router.options.notFoundRoute?.options.component)\n    : route.options.notFoundComponent\n\n  const ResolvedSuspenseBoundary =\n    // If we're on the root route, allow forcefully wrapping in suspense\n    (!route.isRoot || route.options.wrapInSuspense) &&\n    (route.options.wrapInSuspense ??\n      PendingComponent ??\n      (route.options.errorComponent as any)?.preload)\n      ? React.Suspense\n      : SafeFragment\n\n  const ResolvedCatchBoundary = routeErrorComponent\n    ? CatchBoundary\n    : SafeFragment\n\n  const ResolvedNotFoundBoundary = routeNotFoundComponent\n    ? CatchNotFound\n    : SafeFragment\n\n  const resetKey = useRouterState({\n    select: (s) => s.loadedAt,\n  })\n\n  const parentRouteId = useRouterState({\n    select: (s) => {\n      const index = s.matches.findIndex((d) => d.id === matchId)\n      return s.matches[index - 1]?.routeId as string\n    },\n  })\n\n  return (\n    <>\n      <matchContext.Provider value={matchId}>\n        <ResolvedSuspenseBoundary fallback={pendingElement}>\n          <ResolvedCatchBoundary\n            getResetKey={() => resetKey}\n            errorComponent={routeErrorComponent || ErrorComponent}\n            onCatch={(error, errorInfo) => {\n              // Forward not found errors (we don't want to show the error component for these)\n              if (isNotFound(error)) throw error\n              warning(false, `Error in route match: ${matchId}`)\n              routeOnCatch?.(error, errorInfo)\n            }}\n          >\n            <ResolvedNotFoundBoundary\n              fallback={(error) => {\n                // If the current not found handler doesn't exist or it has a\n                // route ID which doesn't match the current route, rethrow the error\n                if (\n                  !routeNotFoundComponent ||\n                  (error.routeId && error.routeId !== routeId) ||\n                  (!error.routeId && !route.isRoot)\n                )\n                  throw error\n\n                return React.createElement(routeNotFoundComponent, error as any)\n              }}\n            >\n              <MatchInner matchId={matchId} />\n            </ResolvedNotFoundBoundary>\n          </ResolvedCatchBoundary>\n        </ResolvedSuspenseBoundary>\n      </matchContext.Provider>\n      {parentRouteId === rootRouteId && router.options.scrollRestoration ? (\n        <>\n          <OnRendered />\n          <ScrollRestoration />\n        </>\n      ) : null}\n    </>\n  )\n})\n\n// On Rendered can't happen above the root layout because it actually\n// renders a dummy dom element to track the rendered state of the app.\n// We render a script tag with a key that changes based on the current\n// location state.key. Also, because it's below the root layout, it\n// allows us to fire onRendered events even after a hydration mismatch\n// error that occurred above the root layout (like bad head/link tags,\n// which is common).\nfunction OnRendered() {\n  const router = useRouter()\n\n  const prevLocationRef = React.useRef<undefined | ParsedLocation<{}>>(\n    undefined,\n  )\n\n  return (\n    <script\n      key={router.latestLocation.state.key}\n      suppressHydrationWarning\n      ref={(el) => {\n        if (\n          el &&\n          (prevLocationRef.current === undefined ||\n            prevLocationRef.current.href !== router.latestLocation.href)\n        ) {\n          router.emit({\n            type: 'onRendered',\n            ...getLocationChangeInfo(router.state),\n          })\n          prevLocationRef.current = router.latestLocation\n        }\n      }}\n    />\n  )\n}\n\nexport const MatchInner = React.memo(function MatchInnerImpl({\n  matchId,\n}: {\n  matchId: string\n}): any {\n  const router = useRouter()\n\n  const { match, key, routeId } = useRouterState({\n    select: (s) => {\n      const matchIndex = s.matches.findIndex((d) => d.id === matchId)\n      const match = s.matches[matchIndex]!\n      const routeId = match.routeId as string\n\n      const remountFn =\n        (router.routesById[routeId] as AnyRoute).options.remountDeps ??\n        router.options.defaultRemountDeps\n      const remountDeps = remountFn?.({\n        routeId,\n        loaderDeps: match.loaderDeps,\n        params: match._strictParams,\n        search: match._strictSearch,\n      })\n      const key = remountDeps ? JSON.stringify(remountDeps) : undefined\n\n      return {\n        key,\n        routeId,\n        match: pick(match, ['id', 'status', 'error']),\n      }\n    },\n    structuralSharing: true as any,\n  })\n\n  const route = router.routesById[routeId] as AnyRoute\n\n  const out = React.useMemo(() => {\n    const Comp = route.options.component ?? router.options.defaultComponent\n    if (Comp) {\n      return <Comp key={key} />\n    }\n    return <Outlet />\n  }, [key, route.options.component, router.options.defaultComponent])\n\n  const RouteErrorComponent =\n    (route.options.errorComponent ?? router.options.defaultErrorComponent) ||\n    ErrorComponent\n\n  if (match.status === 'notFound') {\n    invariant(isNotFound(match.error), 'Expected a notFound error')\n    return renderRouteNotFound(router, route, match.error)\n  }\n\n  if (match.status === 'redirected') {\n    // Redirects should be handled by the router transition. If we happen to\n    // encounter a redirect here, it's a bug. Let's warn, but render nothing.\n    invariant(isRedirect(match.error), 'Expected a redirect error')\n\n    // warning(\n    //   false,\n    //   'Tried to render a redirected route match! This is a weird circumstance, please file an issue!',\n    // )\n    throw router.getMatch(match.id)?.loadPromise\n  }\n\n  if (match.status === 'error') {\n    // If we're on the server, we need to use React's new and super\n    // wonky api for throwing errors from a server side render inside\n    // of a suspense boundary. This is the only way to get\n    // renderToPipeableStream to not hang indefinitely.\n    // We'll serialize the error and rethrow it on the client.\n    if (router.isServer) {\n      return (\n        <RouteErrorComponent\n          error={match.error as any}\n          reset={undefined as any}\n          info={{\n            componentStack: '',\n          }}\n        />\n      )\n    }\n\n    throw match.error\n  }\n\n  if (match.status === 'pending') {\n    // We're pending, and if we have a minPendingMs, we need to wait for it\n    const pendingMinMs =\n      route.options.pendingMinMs ?? router.options.defaultPendingMinMs\n\n    if (pendingMinMs && !router.getMatch(match.id)?.minPendingPromise) {\n      // Create a promise that will resolve after the minPendingMs\n      if (!router.isServer) {\n        const minPendingPromise = createControlledPromise<void>()\n\n        Promise.resolve().then(() => {\n          router.updateMatch(match.id, (prev) => ({\n            ...prev,\n            minPendingPromise,\n          }))\n        })\n\n        setTimeout(() => {\n          minPendingPromise.resolve()\n\n          // We've handled the minPendingPromise, so we can delete it\n          router.updateMatch(match.id, (prev) => ({\n            ...prev,\n            minPendingPromise: undefined,\n          }))\n        }, pendingMinMs)\n      }\n    }\n    throw router.getMatch(match.id)?.loadPromise\n  }\n\n  return out\n})\n\nexport const Outlet = React.memo(function OutletImpl() {\n  const router = useRouter()\n  const matchId = React.useContext(matchContext)\n  const routeId = useRouterState({\n    select: (s) => s.matches.find((d) => d.id === matchId)?.routeId as string,\n  })\n\n  const route = router.routesById[routeId]!\n\n  const parentGlobalNotFound = useRouterState({\n    select: (s) => {\n      const matches = s.matches\n      const parentMatch = matches.find((d) => d.id === matchId)\n      invariant(\n        parentMatch,\n        `Could not find parent match for matchId \"${matchId}\"`,\n      )\n      return parentMatch.globalNotFound\n    },\n  })\n\n  const childMatchId = useRouterState({\n    select: (s) => {\n      const matches = s.matches\n      const index = matches.findIndex((d) => d.id === matchId)\n      return matches[index + 1]?.id\n    },\n  })\n\n  if (parentGlobalNotFound) {\n    return renderRouteNotFound(router, route, undefined)\n  }\n\n  if (!childMatchId) {\n    return null\n  }\n\n  const nextMatch = <Match matchId={childMatchId} />\n\n  const pendingElement = router.options.defaultPendingComponent ? (\n    <router.options.defaultPendingComponent />\n  ) : null\n\n  if (matchId === rootRouteId) {\n    return (\n      <React.Suspense fallback={pendingElement}>{nextMatch}</React.Suspense>\n    )\n  }\n\n  return nextMatch\n})\n", "import * as React from 'react'\nimport warning from 'tiny-warning'\nimport { CatchBoundary, ErrorComponent } from './CatchBoundary'\nimport { useRouterState } from './useRouterState'\nimport { useRouter } from './useRouter'\nimport { Transitioner } from './Transitioner'\nimport { matchContext } from './matchContext'\nimport { Match } from './Match'\nimport { SafeFragment } from './SafeFragment'\nimport type {\n  StructuralSharingOption,\n  ValidateSelected,\n} from './structuralSharing'\nimport type { ReactNode } from './route'\nimport type {\n  AnyRouter,\n  DeepPartial,\n  MakeOptionalPathParams,\n  MakeOptionalSearchParams,\n  MakeRouteMatchUnion,\n  MaskOptions,\n  MatchRouteOptions,\n  NoInfer,\n  RegisteredRouter,\n  ResolveRelativePath,\n  ResolveRoute,\n  RouteByPath,\n  RouterState,\n  ToSubOptionsProps,\n} from '@tanstack/router-core'\n\ndeclare module '@tanstack/router-core' {\n  export interface RouteMatchExtensions {\n    meta?: Array<React.JSX.IntrinsicElements['meta'] | undefined>\n    links?: Array<React.JSX.IntrinsicElements['link'] | undefined>\n    scripts?: Array<React.JSX.IntrinsicElements['script'] | undefined>\n    headScripts?: Array<React.JSX.IntrinsicElements['script'] | undefined>\n  }\n}\n\nexport function Matches() {\n  const router = useRouter()\n\n  const pendingElement = router.options.defaultPendingComponent ? (\n    <router.options.defaultPendingComponent />\n  ) : null\n\n  // Do not render a root Suspense during SSR or hydrating from SSR\n  const ResolvedSuspense =\n    router.isServer || (typeof document !== 'undefined' && router.clientSsr)\n      ? SafeFragment\n      : React.Suspense\n\n  const inner = (\n    <ResolvedSuspense fallback={pendingElement}>\n      <Transitioner />\n      <MatchesInner />\n    </ResolvedSuspense>\n  )\n\n  return router.options.InnerWrap ? (\n    <router.options.InnerWrap>{inner}</router.options.InnerWrap>\n  ) : (\n    inner\n  )\n}\n\nfunction MatchesInner() {\n  const matchId = useRouterState({\n    select: (s) => {\n      return s.matches[0]?.id\n    },\n  })\n\n  const resetKey = useRouterState({\n    select: (s) => s.loadedAt,\n  })\n\n  return (\n    <matchContext.Provider value={matchId}>\n      <CatchBoundary\n        getResetKey={() => resetKey}\n        errorComponent={ErrorComponent}\n        onCatch={(error) => {\n          warning(\n            false,\n            `The following error wasn't caught by any route! At the very least, consider setting an 'errorComponent' in your RootRoute!`,\n          )\n          warning(false, error.message || error.toString())\n        }}\n      >\n        {matchId ? <Match matchId={matchId} /> : null}\n      </CatchBoundary>\n    </matchContext.Provider>\n  )\n}\n\nexport type UseMatchRouteOptions<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TFrom extends string = string,\n  TTo extends string | undefined = undefined,\n  TMaskFrom extends string = TFrom,\n  TMaskTo extends string = '',\n> = ToSubOptionsProps<TRouter, TFrom, TTo> &\n  DeepPartial<MakeOptionalSearchParams<TRouter, TFrom, TTo>> &\n  DeepPartial<MakeOptionalPathParams<TRouter, TFrom, TTo>> &\n  MaskOptions<TRouter, TMaskFrom, TMaskTo> &\n  MatchRouteOptions\n\nexport function useMatchRoute<TRouter extends AnyRouter = RegisteredRouter>() {\n  const router = useRouter()\n\n  useRouterState({\n    select: (s) => [s.location.href, s.resolvedLocation?.href, s.status],\n    structuralSharing: true as any,\n  })\n\n  return React.useCallback(\n    <\n      const TFrom extends string = string,\n      const TTo extends string | undefined = undefined,\n      const TMaskFrom extends string = TFrom,\n      const TMaskTo extends string = '',\n    >(\n      opts: UseMatchRouteOptions<TRouter, TFrom, TTo, TMaskFrom, TMaskTo>,\n    ): false | ResolveRoute<TRouter, TFrom, TTo>['types']['allParams'] => {\n      const { pending, caseSensitive, fuzzy, includeSearch, ...rest } = opts\n\n      return router.matchRoute(rest as any, {\n        pending,\n        caseSensitive,\n        fuzzy,\n        includeSearch,\n      })\n    },\n    [router],\n  )\n}\n\nexport type MakeMatchRouteOptions<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TFrom extends string = string,\n  TTo extends string | undefined = undefined,\n  TMaskFrom extends string = TFrom,\n  TMaskTo extends string = '',\n> = UseMatchRouteOptions<TRouter, TFrom, TTo, TMaskFrom, TMaskTo> & {\n  // If a function is passed as a child, it will be given the `isActive` boolean to aid in further styling on the element it returns\n  children?:\n    | ((\n        params?: RouteByPath<\n          TRouter['routeTree'],\n          ResolveRelativePath<TFrom, NoInfer<TTo>>\n        >['types']['allParams'],\n      ) => ReactNode)\n    | React.ReactNode\n}\n\nexport function MatchRoute<\n  TRouter extends AnyRouter = RegisteredRouter,\n  const TFrom extends string = string,\n  const TTo extends string | undefined = undefined,\n  const TMaskFrom extends string = TFrom,\n  const TMaskTo extends string = '',\n>(props: MakeMatchRouteOptions<TRouter, TFrom, TTo, TMaskFrom, TMaskTo>): any {\n  const matchRoute = useMatchRoute()\n  const params = matchRoute(props as any) as boolean\n\n  if (typeof props.children === 'function') {\n    return (props.children as any)(params)\n  }\n\n  return params ? props.children : null\n}\n\nexport interface UseMatchesBaseOptions<\n  TRouter extends AnyRouter,\n  TSelected,\n  TStructuralSharing,\n> {\n  select?: (\n    matches: Array<MakeRouteMatchUnion<TRouter>>,\n  ) => ValidateSelected<TRouter, TSelected, TStructuralSharing>\n}\n\nexport type UseMatchesResult<\n  TRouter extends AnyRouter,\n  TSelected,\n> = unknown extends TSelected ? Array<MakeRouteMatchUnion<TRouter>> : TSelected\n\nexport function useMatches<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts?: UseMatchesBaseOptions<TRouter, TSelected, TStructuralSharing> &\n    StructuralSharingOption<TRouter, TSelected, TStructuralSharing>,\n): UseMatchesResult<TRouter, TSelected> {\n  return useRouterState({\n    select: (state: RouterState<TRouter['routeTree']>) => {\n      const matches = state.matches\n      return opts?.select\n        ? opts.select(matches as Array<MakeRouteMatchUnion<TRouter>>)\n        : matches\n    },\n    structuralSharing: opts?.structuralSharing,\n  } as any) as UseMatchesResult<TRouter, TSelected>\n}\n\nexport function useParentMatches<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts?: UseMatchesBaseOptions<TRouter, TSelected, TStructuralSharing> &\n    StructuralSharingOption<TRouter, TSelected, TStructuralSharing>,\n): UseMatchesResult<TRouter, TSelected> {\n  const contextMatchId = React.useContext(matchContext)\n\n  return useMatches({\n    select: (matches: Array<MakeRouteMatchUnion<TRouter>>) => {\n      matches = matches.slice(\n        0,\n        matches.findIndex((d) => d.id === contextMatchId),\n      )\n      return opts?.select ? opts.select(matches) : matches\n    },\n    structuralSharing: opts?.structuralSharing,\n  } as any)\n}\n\nexport function useChildMatches<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts?: UseMatchesBaseOptions<TRouter, TSelected, TStructuralSharing> &\n    StructuralSharingOption<TRouter, TSelected, TStructuralSharing>,\n): UseMatchesResult<TRouter, TSelected> {\n  const contextMatchId = React.useContext(matchContext)\n\n  return useMatches({\n    select: (matches: Array<MakeRouteMatchUnion<TRouter>>) => {\n      matches = matches.slice(\n        matches.findIndex((d) => d.id === contextMatchId) + 1,\n      )\n      return opts?.select ? opts.select(matches) : matches\n    },\n    structuralSharing: opts?.structuralSharing,\n  } as any)\n}\n", "import * as React from 'react'\nimport { flushSync } from 'react-dom'\nimport {\n  deepEqual,\n  exactPathTest,\n  functionalUpdate,\n  preloadWarning,\n  removeTrailingSlash,\n} from '@tanstack/router-core'\nimport { useRouterState } from './useRouterState'\nimport { useRouter } from './useRouter'\n\nimport {\n  useForwardedRef,\n  useIntersectionObserver,\n  useLayoutEffect,\n} from './utils'\n\nimport { useMatches } from './Matches'\nimport type {\n  AnyRouter,\n  Constrain,\n  LinkCurrentTargetElement,\n  LinkOptions,\n  RegisteredRouter,\n  RoutePaths,\n} from '@tanstack/router-core'\nimport type { ReactNode } from 'react'\nimport type {\n  ValidateLinkOptions,\n  ValidateLinkOptionsArray,\n} from './typePrimitives'\n\nexport function useLinkProps<\n  TRouter extends AnyRouter = RegisteredRouter,\n  const TFrom extends string = string,\n  const TTo extends string | undefined = undefined,\n  const TMaskFrom extends string = TFrom,\n  const TMaskTo extends string = '',\n>(\n  options: UseLinkPropsOptions<TRouter, TFrom, TTo, TMaskFrom, TMaskTo>,\n  forwardedRef?: React.ForwardedRef<Element>,\n): React.ComponentPropsWithRef<'a'> {\n  const router = useRouter()\n  const [isTransitioning, setIsTransitioning] = React.useState(false)\n  const hasRenderFetched = React.useRef(false)\n  const innerRef = useForwardedRef(forwardedRef)\n\n  const {\n    // custom props\n    activeProps = () => ({ className: 'active' }),\n    inactiveProps = () => ({}),\n    activeOptions,\n    to,\n    preload: userPreload,\n    preloadDelay: userPreloadDelay,\n    hashScrollIntoView,\n    replace,\n    startTransition,\n    resetScroll,\n    viewTransition,\n    // element props\n    children,\n    target,\n    disabled,\n    style,\n    className,\n    onClick,\n    onFocus,\n    onMouseEnter,\n    onMouseLeave,\n    onTouchStart,\n    ignoreBlocker,\n    ...rest\n  } = options\n\n  const {\n    // prevent these from being returned\n    params: _params,\n    search: _search,\n    hash: _hash,\n    state: _state,\n    mask: _mask,\n    reloadDocument: _reloadDocument,\n    ...propsSafeToSpread\n  } = rest\n\n  // If this link simply reloads the current route,\n  // make sure it has a new key so it will trigger a data refresh\n\n  // If this `to` is a valid external URL, return\n  // null for LinkUtils\n\n  const type: 'internal' | 'external' = React.useMemo(() => {\n    try {\n      new URL(`${to}`)\n      return 'external'\n    } catch {}\n    return 'internal'\n  }, [to])\n\n  // subscribe to search params to re-build location if it changes\n  const currentSearch = useRouterState({\n    select: (s) => s.location.search,\n    structuralSharing: true as any,\n  })\n\n  // when `from` is not supplied, use the leaf route of the current matches as the `from` location\n  // so relative routing works as expected\n  const from = useMatches({\n    select: (matches) => options.from ?? matches[matches.length - 1]?.fullPath,\n  })\n  // Use it as the default `from` location\n  const _options = React.useMemo(() => ({ ...options, from }), [options, from])\n\n  const next = React.useMemo(\n    () => router.buildLocation(_options as any),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [router, _options, currentSearch],\n  )\n\n  const preload = React.useMemo(() => {\n    if (_options.reloadDocument) {\n      return false\n    }\n    return userPreload ?? router.options.defaultPreload\n  }, [router.options.defaultPreload, userPreload, _options.reloadDocument])\n  const preloadDelay =\n    userPreloadDelay ?? router.options.defaultPreloadDelay ?? 0\n\n  const isActive = useRouterState({\n    select: (s) => {\n      if (activeOptions?.exact) {\n        const testExact = exactPathTest(\n          s.location.pathname,\n          next.pathname,\n          router.basepath,\n        )\n        if (!testExact) {\n          return false\n        }\n      } else {\n        const currentPathSplit = removeTrailingSlash(\n          s.location.pathname,\n          router.basepath,\n        ).split('/')\n        const nextPathSplit = removeTrailingSlash(\n          next.pathname,\n          router.basepath,\n        ).split('/')\n\n        const pathIsFuzzyEqual = nextPathSplit.every(\n          (d, i) => d === currentPathSplit[i],\n        )\n        if (!pathIsFuzzyEqual) {\n          return false\n        }\n      }\n\n      if (activeOptions?.includeSearch ?? true) {\n        const searchTest = deepEqual(s.location.search, next.search, {\n          partial: !activeOptions?.exact,\n          ignoreUndefined: !activeOptions?.explicitUndefined,\n        })\n        if (!searchTest) {\n          return false\n        }\n      }\n\n      if (activeOptions?.includeHash) {\n        return s.location.hash === next.hash\n      }\n      return true\n    },\n  })\n\n  const doPreload = React.useCallback(() => {\n    router.preloadRoute(_options as any).catch((err) => {\n      console.warn(err)\n      console.warn(preloadWarning)\n    })\n  }, [_options, router])\n\n  const preloadViewportIoCallback = React.useCallback(\n    (entry: IntersectionObserverEntry | undefined) => {\n      if (entry?.isIntersecting) {\n        doPreload()\n      }\n    },\n    [doPreload],\n  )\n\n  useIntersectionObserver(\n    innerRef,\n    preloadViewportIoCallback,\n    { rootMargin: '100px' },\n    { disabled: !!disabled || !(preload === 'viewport') },\n  )\n\n  useLayoutEffect(() => {\n    if (hasRenderFetched.current) {\n      return\n    }\n    if (!disabled && preload === 'render') {\n      doPreload()\n      hasRenderFetched.current = true\n    }\n  }, [disabled, doPreload, preload])\n\n  if (type === 'external') {\n    return {\n      ...propsSafeToSpread,\n      ref: innerRef as React.ComponentPropsWithRef<'a'>['ref'],\n      type,\n      href: to,\n      ...(children && { children }),\n      ...(target && { target }),\n      ...(disabled && { disabled }),\n      ...(style && { style }),\n      ...(className && { className }),\n      ...(onClick && { onClick }),\n      ...(onFocus && { onFocus }),\n      ...(onMouseEnter && { onMouseEnter }),\n      ...(onMouseLeave && { onMouseLeave }),\n      ...(onTouchStart && { onTouchStart }),\n    }\n  }\n\n  // The click handler\n  const handleClick = (e: MouseEvent) => {\n    if (\n      !disabled &&\n      !isCtrlEvent(e) &&\n      !e.defaultPrevented &&\n      (!target || target === '_self') &&\n      e.button === 0\n    ) {\n      e.preventDefault()\n\n      flushSync(() => {\n        setIsTransitioning(true)\n      })\n\n      const unsub = router.subscribe('onResolved', () => {\n        unsub()\n        setIsTransitioning(false)\n      })\n\n      // All is well? Navigate!\n      // N.B. we don't call `router.commitLocation(next) here because we want to run `validateSearch` before committing\n      return router.navigate({\n        ..._options,\n        replace,\n        resetScroll,\n        hashScrollIntoView,\n        startTransition,\n        viewTransition,\n        ignoreBlocker,\n      } as any)\n    }\n  }\n\n  // The click handler\n  const handleFocus = (_: MouseEvent) => {\n    if (disabled) return\n    if (preload) {\n      doPreload()\n    }\n  }\n\n  const handleTouchStart = handleFocus\n\n  const handleEnter = (e: MouseEvent) => {\n    if (disabled) return\n    const eventTarget = (e.target || {}) as LinkCurrentTargetElement\n\n    if (preload) {\n      if (eventTarget.preloadTimeout) {\n        return\n      }\n\n      eventTarget.preloadTimeout = setTimeout(() => {\n        eventTarget.preloadTimeout = null\n        doPreload()\n      }, preloadDelay)\n    }\n  }\n\n  const handleLeave = (e: MouseEvent) => {\n    if (disabled) return\n    const eventTarget = (e.target || {}) as LinkCurrentTargetElement\n\n    if (eventTarget.preloadTimeout) {\n      clearTimeout(eventTarget.preloadTimeout)\n      eventTarget.preloadTimeout = null\n    }\n  }\n\n  const composeHandlers =\n    (handlers: Array<undefined | ((e: any) => void)>) =>\n    (e: { persist?: () => void; defaultPrevented: boolean }) => {\n      e.persist?.()\n      handlers.filter(Boolean).forEach((handler) => {\n        if (e.defaultPrevented) return\n        handler!(e)\n      })\n    }\n\n  // Get the active props\n  const resolvedActiveProps: React.HTMLAttributes<HTMLAnchorElement> = isActive\n    ? (functionalUpdate(activeProps as any, {}) ?? {})\n    : {}\n\n  // Get the inactive props\n  const resolvedInactiveProps: React.HTMLAttributes<HTMLAnchorElement> =\n    isActive ? {} : functionalUpdate(inactiveProps, {})\n\n  const resolvedClassName = [\n    className,\n    resolvedActiveProps.className,\n    resolvedInactiveProps.className,\n  ]\n    .filter(Boolean)\n    .join(' ')\n\n  const resolvedStyle = {\n    ...style,\n    ...resolvedActiveProps.style,\n    ...resolvedInactiveProps.style,\n  }\n\n  return {\n    ...propsSafeToSpread,\n    ...resolvedActiveProps,\n    ...resolvedInactiveProps,\n    href: disabled\n      ? undefined\n      : next.maskedLocation\n        ? router.history.createHref(next.maskedLocation.href)\n        : router.history.createHref(next.href),\n    ref: innerRef as React.ComponentPropsWithRef<'a'>['ref'],\n    onClick: composeHandlers([onClick, handleClick]),\n    onFocus: composeHandlers([onFocus, handleFocus]),\n    onMouseEnter: composeHandlers([onMouseEnter, handleEnter]),\n    onMouseLeave: composeHandlers([onMouseLeave, handleLeave]),\n    onTouchStart: composeHandlers([onTouchStart, handleTouchStart]),\n    disabled: !!disabled,\n    target,\n    ...(Object.keys(resolvedStyle).length && { style: resolvedStyle }),\n    ...(resolvedClassName && { className: resolvedClassName }),\n    ...(disabled && {\n      role: 'link',\n      'aria-disabled': true,\n    }),\n    ...(isActive && { 'data-status': 'active', 'aria-current': 'page' }),\n    ...(isTransitioning && { 'data-transitioning': 'transitioning' }),\n  }\n}\n\ntype UseLinkReactProps<TComp> = TComp extends keyof React.JSX.IntrinsicElements\n  ? React.JSX.IntrinsicElements[TComp]\n  : React.PropsWithoutRef<\n      TComp extends React.ComponentType<infer TProps> ? TProps : never\n    > &\n      React.RefAttributes<\n        TComp extends\n          | React.FC<{ ref: React.Ref<infer TRef> }>\n          | React.Component<{ ref: React.Ref<infer TRef> }>\n          ? TRef\n          : never\n      >\n\nexport type UseLinkPropsOptions<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TFrom extends RoutePaths<TRouter['routeTree']> | string = string,\n  TTo extends string | undefined = '.',\n  TMaskFrom extends RoutePaths<TRouter['routeTree']> | string = TFrom,\n  TMaskTo extends string = '.',\n> = ActiveLinkOptions<'a', TRouter, TFrom, TTo, TMaskFrom, TMaskTo> &\n  UseLinkReactProps<'a'>\n\nexport type ActiveLinkOptions<\n  TComp = 'a',\n  TRouter extends AnyRouter = RegisteredRouter,\n  TFrom extends string = string,\n  TTo extends string | undefined = '.',\n  TMaskFrom extends string = TFrom,\n  TMaskTo extends string = '.',\n> = LinkOptions<TRouter, TFrom, TTo, TMaskFrom, TMaskTo> &\n  ActiveLinkOptionProps<TComp>\n\ntype ActiveLinkProps<TComp> = Partial<\n  LinkComponentReactProps<TComp> & {\n    [key: `data-${string}`]: unknown\n  }\n>\n\nexport interface ActiveLinkOptionProps<TComp = 'a'> {\n  /**\n   * A function that returns additional props for the `active` state of this link.\n   * These props override other props passed to the link (`style`'s are merged, `className`'s are concatenated)\n   */\n  activeProps?: ActiveLinkProps<TComp> | (() => ActiveLinkProps<TComp>)\n  /**\n   * A function that returns additional props for the `inactive` state of this link.\n   * These props override other props passed to the link (`style`'s are merged, `className`'s are concatenated)\n   */\n  inactiveProps?: ActiveLinkProps<TComp> | (() => ActiveLinkProps<TComp>)\n}\n\nexport type LinkProps<\n  TComp = 'a',\n  TRouter extends AnyRouter = RegisteredRouter,\n  TFrom extends string = string,\n  TTo extends string | undefined = '.',\n  TMaskFrom extends string = TFrom,\n  TMaskTo extends string = '.',\n> = ActiveLinkOptions<TComp, TRouter, TFrom, TTo, TMaskFrom, TMaskTo> &\n  LinkPropsChildren\n\nexport interface LinkPropsChildren {\n  // If a function is passed as a child, it will be given the `isActive` boolean to aid in further styling on the element it returns\n  children?:\n    | React.ReactNode\n    | ((state: {\n        isActive: boolean\n        isTransitioning: boolean\n      }) => React.ReactNode)\n}\n\ntype LinkComponentReactProps<TComp> = Omit<\n  UseLinkReactProps<TComp>,\n  keyof CreateLinkProps\n>\n\nexport type LinkComponentProps<\n  TComp = 'a',\n  TRouter extends AnyRouter = RegisteredRouter,\n  TFrom extends string = string,\n  TTo extends string | undefined = '.',\n  TMaskFrom extends string = TFrom,\n  TMaskTo extends string = '.',\n> = LinkComponentReactProps<TComp> &\n  LinkProps<TComp, TRouter, TFrom, TTo, TMaskFrom, TMaskTo>\n\nexport type CreateLinkProps = LinkProps<\n  any,\n  any,\n  string,\n  string,\n  string,\n  string\n>\n\nexport type LinkComponent<\n  in out TComp,\n  in out TDefaultFrom extends string = string,\n> = <\n  TRouter extends AnyRouter = RegisteredRouter,\n  const TFrom extends string = TDefaultFrom,\n  const TTo extends string | undefined = undefined,\n  const TMaskFrom extends string = TFrom,\n  const TMaskTo extends string = '',\n>(\n  props: LinkComponentProps<TComp, TRouter, TFrom, TTo, TMaskFrom, TMaskTo>,\n) => React.ReactElement\n\nexport interface LinkComponentRoute<\n  in out TDefaultFrom extends string = string,\n> {\n  defaultFrom: TDefaultFrom\n  <\n    TRouter extends AnyRouter = RegisteredRouter,\n    const TTo extends string | undefined = undefined,\n    const TMaskTo extends string = '',\n  >(\n    props: LinkComponentProps<\n      'a',\n      TRouter,\n      this['defaultFrom'],\n      TTo,\n      this['defaultFrom'],\n      TMaskTo\n    >,\n  ): React.ReactElement\n}\n\nexport function createLink<const TComp>(\n  Comp: Constrain<TComp, any, (props: CreateLinkProps) => ReactNode>,\n): LinkComponent<TComp> {\n  return React.forwardRef(function CreatedLink(props, ref) {\n    return <Link {...(props as any)} _asChild={Comp} ref={ref} />\n  }) as any\n}\n\nexport const Link: LinkComponent<'a'> = React.forwardRef<Element, any>(\n  (props, ref) => {\n    const { _asChild, ...rest } = props\n    const {\n      type: _type,\n      ref: innerRef,\n      ...linkProps\n    } = useLinkProps(rest as any, ref)\n\n    const children =\n      typeof rest.children === 'function'\n        ? rest.children({\n            isActive: (linkProps as any)['data-status'] === 'active',\n          })\n        : rest.children\n\n    if (typeof _asChild === 'undefined') {\n      // the ReturnType of useLinkProps returns the correct type for a <a> element, not a general component that has a disabled prop\n      // @ts-expect-error\n      delete linkProps.disabled\n    }\n\n    return React.createElement(\n      _asChild ? _asChild : 'a',\n      {\n        ...linkProps,\n        ref: innerRef,\n      },\n      children,\n    )\n  },\n) as any\n\nfunction isCtrlEvent(e: MouseEvent) {\n  return !!(e.metaKey || e.altKey || e.ctrlKey || e.shiftKey)\n}\n\nexport type LinkOptionsFnOptions<\n  TOptions,\n  TComp,\n  TRouter extends AnyRouter = RegisteredRouter,\n> =\n  TOptions extends ReadonlyArray<any>\n    ? ValidateLinkOptionsArray<TRouter, TOptions, string, TComp>\n    : ValidateLinkOptions<TRouter, TOptions, string, TComp>\n\nexport type LinkOptionsFn<TComp> = <\n  const TOptions,\n  TRouter extends AnyRouter = RegisteredRouter,\n>(\n  options: LinkOptionsFnOptions<TOptions, TComp, TRouter>,\n) => TOptions\n\nexport const linkOptions: LinkOptionsFn<'a'> = (options) => {\n  return options as any\n}\n", "import {\n  BaseRootRoute,\n  <PERSON>Route,\n  BaseRouteApi,\n  notFound,\n} from '@tanstack/router-core'\nimport React from 'react'\nimport { useLoaderData } from './useLoaderData'\nimport { useLoaderDeps } from './useLoaderDeps'\nimport { useParams } from './useParams'\nimport { useSearch } from './useSearch'\nimport { useNavigate } from './useNavigate'\nimport { useMatch } from './useMatch'\nimport { useRouter } from './useRouter'\nimport { Link } from './link'\nimport type {\n  AnyContext,\n  AnyRoute,\n  AnyRouter,\n  ConstrainLiteral,\n  ErrorComponentProps,\n  NotFoundError,\n  NotFoundRouteProps,\n  RegisteredRouter,\n  ResolveFullPath,\n  ResolveId,\n  ResolveParams,\n  RootRoute as RootRouteCore,\n  RootRouteId,\n  RootRouteOptions,\n  RouteConstraints,\n  Route as RouteCore,\n  RouteIds,\n  RouteMask,\n  RouteOptions,\n  RouteTypesById,\n  RouterCore,\n  ToMaskOptions,\n  UseNavigateResult,\n} from '@tanstack/router-core'\nimport type { UseLoaderDataRoute } from './useLoaderData'\nimport type { UseMatchRoute } from './useMatch'\nimport type { UseLoaderDepsRoute } from './useLoaderDeps'\nimport type { UseParamsRoute } from './useParams'\nimport type { UseSearchRoute } from './useSearch'\nimport type { UseRouteContextRoute } from './useRouteContext'\nimport type { LinkComponentRoute } from './link'\n\ndeclare module '@tanstack/router-core' {\n  export interface UpdatableRouteOptionsExtensions {\n    component?: RouteComponent\n    errorComponent?: false | null | ErrorRouteComponent\n    notFoundComponent?: NotFoundRouteComponent\n    pendingComponent?: RouteComponent\n  }\n\n  export interface RouteExtensions<\n    in out TId extends string,\n    in out TFullPath extends string,\n  > {\n    useMatch: UseMatchRoute<TId>\n    useRouteContext: UseRouteContextRoute<TId>\n    useSearch: UseSearchRoute<TId>\n    useParams: UseParamsRoute<TId>\n    useLoaderDeps: UseLoaderDepsRoute<TId>\n    useLoaderData: UseLoaderDataRoute<TId>\n    useNavigate: () => UseNavigateResult<TFullPath>\n    Link: LinkComponentRoute<TFullPath>\n  }\n}\n\nexport function getRouteApi<\n  const TId,\n  TRouter extends AnyRouter = RegisteredRouter,\n>(id: ConstrainLiteral<TId, RouteIds<TRouter['routeTree']>>) {\n  return new RouteApi<TId, TRouter>({ id })\n}\n\nexport class RouteApi<\n  TId,\n  TRouter extends AnyRouter = RegisteredRouter,\n> extends BaseRouteApi<TId, TRouter> {\n  /**\n   * @deprecated Use the `getRouteApi` function instead.\n   */\n  constructor({ id }: { id: TId }) {\n    super({ id })\n  }\n\n  useMatch: UseMatchRoute<TId> = (opts) => {\n    return useMatch({\n      select: opts?.select,\n      from: this.id,\n      structuralSharing: opts?.structuralSharing,\n    } as any) as any\n  }\n\n  useRouteContext: UseRouteContextRoute<TId> = (opts) => {\n    return useMatch({\n      from: this.id as any,\n      select: (d) => (opts?.select ? opts.select(d.context) : d.context),\n    }) as any\n  }\n\n  useSearch: UseSearchRoute<TId> = (opts) => {\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n    return useSearch({\n      select: opts?.select,\n      structuralSharing: opts?.structuralSharing,\n      from: this.id,\n    } as any) as any\n  }\n\n  useParams: UseParamsRoute<TId> = (opts) => {\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n    return useParams({\n      select: opts?.select,\n      structuralSharing: opts?.structuralSharing,\n      from: this.id,\n    } as any) as any\n  }\n\n  useLoaderDeps: UseLoaderDepsRoute<TId> = (opts) => {\n    return useLoaderDeps({ ...opts, from: this.id, strict: false } as any)\n  }\n\n  useLoaderData: UseLoaderDataRoute<TId> = (opts) => {\n    return useLoaderData({ ...opts, from: this.id, strict: false } as any)\n  }\n\n  useNavigate = (): UseNavigateResult<\n    RouteTypesById<TRouter, TId>['fullPath']\n  > => {\n    const router = useRouter()\n    return useNavigate({ from: router.routesById[this.id as string].fullPath })\n  }\n\n  notFound = (opts?: NotFoundError) => {\n    return notFound({ routeId: this.id as string, ...opts })\n  }\n\n  Link: LinkComponentRoute<RouteTypesById<TRouter, TId>['fullPath']> =\n    React.forwardRef((props, ref: React.ForwardedRef<HTMLAnchorElement>) => {\n      const router = useRouter()\n      const fullPath = router.routesById[this.id as string].fullPath\n      return <Link ref={ref} from={fullPath as never} {...props} />\n    }) as unknown as LinkComponentRoute<\n      RouteTypesById<TRouter, TId>['fullPath']\n    >\n}\n\nexport class Route<\n    in out TParentRoute extends RouteConstraints['TParentRoute'] = AnyRoute,\n    in out TPath extends RouteConstraints['TPath'] = '/',\n    in out TFullPath extends RouteConstraints['TFullPath'] = ResolveFullPath<\n      TParentRoute,\n      TPath\n    >,\n    in out TCustomId extends RouteConstraints['TCustomId'] = string,\n    in out TId extends RouteConstraints['TId'] = ResolveId<\n      TParentRoute,\n      TCustomId,\n      TPath\n    >,\n    in out TSearchValidator = undefined,\n    in out TParams = ResolveParams<TPath>,\n    in out TRouterContext = AnyContext,\n    in out TRouteContextFn = AnyContext,\n    in out TBeforeLoadFn = AnyContext,\n    in out TLoaderDeps extends Record<string, any> = {},\n    in out TLoaderFn = undefined,\n    in out TChildren = unknown,\n    in out TFileRouteTypes = unknown,\n  >\n  extends BaseRoute<\n    TParentRoute,\n    TPath,\n    TFullPath,\n    TCustomId,\n    TId,\n    TSearchValidator,\n    TParams,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn,\n    TLoaderDeps,\n    TLoaderFn,\n    TChildren,\n    TFileRouteTypes\n  >\n  implements\n    RouteCore<\n      TParentRoute,\n      TPath,\n      TFullPath,\n      TCustomId,\n      TId,\n      TSearchValidator,\n      TParams,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn,\n      TLoaderDeps,\n      TLoaderFn,\n      TChildren,\n      TFileRouteTypes\n    >\n{\n  /**\n   * @deprecated Use the `createRoute` function instead.\n   */\n  constructor(\n    options?: RouteOptions<\n      TParentRoute,\n      TId,\n      TCustomId,\n      TFullPath,\n      TPath,\n      TSearchValidator,\n      TParams,\n      TLoaderDeps,\n      TLoaderFn,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn\n    >,\n  ) {\n    super(options)\n    ;(this as any).$$typeof = Symbol.for('react.memo')\n  }\n\n  useMatch: UseMatchRoute<TId> = (opts) => {\n    return useMatch({\n      select: opts?.select,\n      from: this.id,\n      structuralSharing: opts?.structuralSharing,\n    } as any) as any\n  }\n\n  useRouteContext: UseRouteContextRoute<TId> = (opts?) => {\n    return useMatch({\n      ...opts,\n      from: this.id,\n      select: (d) => (opts?.select ? opts.select(d.context) : d.context),\n    }) as any\n  }\n\n  useSearch: UseSearchRoute<TId> = (opts) => {\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n    return useSearch({\n      select: opts?.select,\n      structuralSharing: opts?.structuralSharing,\n      from: this.id,\n    } as any) as any\n  }\n\n  useParams: UseParamsRoute<TId> = (opts) => {\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n    return useParams({\n      select: opts?.select,\n      structuralSharing: opts?.structuralSharing,\n      from: this.id,\n    } as any) as any\n  }\n\n  useLoaderDeps: UseLoaderDepsRoute<TId> = (opts) => {\n    return useLoaderDeps({ ...opts, from: this.id } as any)\n  }\n\n  useLoaderData: UseLoaderDataRoute<TId> = (opts) => {\n    return useLoaderData({ ...opts, from: this.id } as any)\n  }\n\n  useNavigate = (): UseNavigateResult<TFullPath> => {\n    return useNavigate({ from: this.fullPath })\n  }\n\n  Link: LinkComponentRoute<TFullPath> = React.forwardRef(\n    (props, ref: React.ForwardedRef<HTMLAnchorElement>) => {\n      return <Link ref={ref} from={this.fullPath as never} {...props} />\n    },\n  ) as unknown as LinkComponentRoute<TFullPath>\n}\n\nexport function createRoute<\n  TParentRoute extends RouteConstraints['TParentRoute'] = AnyRoute,\n  TPath extends RouteConstraints['TPath'] = '/',\n  TFullPath extends RouteConstraints['TFullPath'] = ResolveFullPath<\n    TParentRoute,\n    TPath\n  >,\n  TCustomId extends RouteConstraints['TCustomId'] = string,\n  TId extends RouteConstraints['TId'] = ResolveId<\n    TParentRoute,\n    TCustomId,\n    TPath\n  >,\n  TSearchValidator = undefined,\n  TParams = ResolveParams<TPath>,\n  TRouteContextFn = AnyContext,\n  TBeforeLoadFn = AnyContext,\n  TLoaderDeps extends Record<string, any> = {},\n  TLoaderFn = undefined,\n  TChildren = unknown,\n>(\n  options: RouteOptions<\n    TParentRoute,\n    TId,\n    TCustomId,\n    TFullPath,\n    TPath,\n    TSearchValidator,\n    TParams,\n    TLoaderDeps,\n    TLoaderFn,\n    AnyContext,\n    TRouteContextFn,\n    TBeforeLoadFn\n  >,\n): Route<\n  TParentRoute,\n  TPath,\n  TFullPath,\n  TCustomId,\n  TId,\n  TSearchValidator,\n  TParams,\n  AnyContext,\n  TRouteContextFn,\n  TBeforeLoadFn,\n  TLoaderDeps,\n  TLoaderFn,\n  TChildren\n> {\n  return new Route<\n    TParentRoute,\n    TPath,\n    TFullPath,\n    TCustomId,\n    TId,\n    TSearchValidator,\n    TParams,\n    AnyContext,\n    TRouteContextFn,\n    TBeforeLoadFn,\n    TLoaderDeps,\n    TLoaderFn,\n    TChildren\n  >(options)\n}\n\nexport type AnyRootRoute = RootRoute<any, any, any, any, any, any, any, any>\n\nexport function createRootRouteWithContext<TRouterContext extends {}>() {\n  return <\n    TRouteContextFn = AnyContext,\n    TBeforeLoadFn = AnyContext,\n    TSearchValidator = undefined,\n    TLoaderDeps extends Record<string, any> = {},\n    TLoaderFn = undefined,\n  >(\n    options?: RootRouteOptions<\n      TSearchValidator,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn,\n      TLoaderDeps,\n      TLoaderFn\n    >,\n  ) => {\n    return createRootRoute<\n      TSearchValidator,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn,\n      TLoaderDeps,\n      TLoaderFn\n    >(options as any)\n  }\n}\n\n/**\n * @deprecated Use the `createRootRouteWithContext` function instead.\n */\nexport const rootRouteWithContext = createRootRouteWithContext\n\nexport class RootRoute<\n    in out TSearchValidator = undefined,\n    in out TRouterContext = {},\n    in out TRouteContextFn = AnyContext,\n    in out TBeforeLoadFn = AnyContext,\n    in out TLoaderDeps extends Record<string, any> = {},\n    in out TLoaderFn = undefined,\n    in out TChildren = unknown,\n    in out TFileRouteTypes = unknown,\n  >\n  extends BaseRootRoute<\n    TSearchValidator,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn,\n    TLoaderDeps,\n    TLoaderFn,\n    TChildren,\n    TFileRouteTypes\n  >\n  implements\n    RootRouteCore<\n      TSearchValidator,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn,\n      TLoaderDeps,\n      TLoaderFn,\n      TChildren,\n      TFileRouteTypes\n    >\n{\n  /**\n   * @deprecated `RootRoute` is now an internal implementation detail. Use `createRootRoute()` instead.\n   */\n  constructor(\n    options?: RootRouteOptions<\n      TSearchValidator,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn,\n      TLoaderDeps,\n      TLoaderFn\n    >,\n  ) {\n    super(options)\n    ;(this as any).$$typeof = Symbol.for('react.memo')\n  }\n\n  useMatch: UseMatchRoute<RootRouteId> = (opts) => {\n    return useMatch({\n      select: opts?.select,\n      from: this.id,\n      structuralSharing: opts?.structuralSharing,\n    } as any) as any\n  }\n\n  useRouteContext: UseRouteContextRoute<RootRouteId> = (opts) => {\n    return useMatch({\n      ...opts,\n      from: this.id,\n      select: (d) => (opts?.select ? opts.select(d.context) : d.context),\n    }) as any\n  }\n\n  useSearch: UseSearchRoute<RootRouteId> = (opts) => {\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n    return useSearch({\n      select: opts?.select,\n      structuralSharing: opts?.structuralSharing,\n      from: this.id,\n    } as any) as any\n  }\n\n  useParams: UseParamsRoute<RootRouteId> = (opts) => {\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n    return useParams({\n      select: opts?.select,\n      structuralSharing: opts?.structuralSharing,\n      from: this.id,\n    } as any) as any\n  }\n\n  useLoaderDeps: UseLoaderDepsRoute<RootRouteId> = (opts) => {\n    return useLoaderDeps({ ...opts, from: this.id } as any)\n  }\n\n  useLoaderData: UseLoaderDataRoute<RootRouteId> = (opts) => {\n    return useLoaderData({ ...opts, from: this.id } as any)\n  }\n\n  useNavigate = (): UseNavigateResult<'/'> => {\n    return useNavigate({ from: this.fullPath })\n  }\n\n  Link: LinkComponentRoute<'/'> = React.forwardRef(\n    (props, ref: React.ForwardedRef<HTMLAnchorElement>) => {\n      return <Link ref={ref} from={this.fullPath} {...props} />\n    },\n  ) as unknown as LinkComponentRoute<'/'>\n}\n\nexport function createRootRoute<\n  TSearchValidator = undefined,\n  TRouterContext = {},\n  TRouteContextFn = AnyContext,\n  TBeforeLoadFn = AnyContext,\n  TLoaderDeps extends Record<string, any> = {},\n  TLoaderFn = undefined,\n>(\n  options?: RootRouteOptions<\n    TSearchValidator,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn,\n    TLoaderDeps,\n    TLoaderFn\n  >,\n): RootRoute<\n  TSearchValidator,\n  TRouterContext,\n  TRouteContextFn,\n  TBeforeLoadFn,\n  TLoaderDeps,\n  TLoaderFn,\n  unknown,\n  unknown\n> {\n  return new RootRoute<\n    TSearchValidator,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn,\n    TLoaderDeps,\n    TLoaderFn\n  >(options)\n}\n\nexport function createRouteMask<\n  TRouteTree extends AnyRoute,\n  TFrom extends string,\n  TTo extends string,\n>(\n  opts: {\n    routeTree: TRouteTree\n  } & ToMaskOptions<RouterCore<TRouteTree, 'never', boolean>, TFrom, TTo>,\n): RouteMask<TRouteTree> {\n  return opts as any\n}\n\nexport type ReactNode = any\n\nexport type SyncRouteComponent<TProps> =\n  | ((props: TProps) => ReactNode)\n  | React.LazyExoticComponent<(props: TProps) => ReactNode>\n\nexport type AsyncRouteComponent<TProps> = SyncRouteComponent<TProps> & {\n  preload?: () => Promise<void>\n}\n\nexport type RouteComponent<TProps = any> = AsyncRouteComponent<TProps>\n\nexport type ErrorRouteComponent = RouteComponent<ErrorComponentProps>\n\nexport type NotFoundRouteComponent = SyncRouteComponent<NotFoundRouteProps>\n\nexport class NotFoundRoute<\n  TParentRoute extends AnyRootRoute,\n  TRouterContext = AnyContext,\n  TRouteContextFn = AnyContext,\n  TBeforeLoadFn = AnyContext,\n  TSearchValidator = undefined,\n  TLoaderDeps extends Record<string, any> = {},\n  TLoaderFn = undefined,\n  TChildren = unknown,\n> extends Route<\n  TParentRoute,\n  '/404',\n  '/404',\n  '404',\n  '404',\n  TSearchValidator,\n  {},\n  TRouterContext,\n  TRouteContextFn,\n  TBeforeLoadFn,\n  TLoaderDeps,\n  TLoaderFn,\n  TChildren\n> {\n  constructor(\n    options: Omit<\n      RouteOptions<\n        TParentRoute,\n        string,\n        string,\n        string,\n        string,\n        TSearchValidator,\n        {},\n        TLoaderDeps,\n        TLoaderFn,\n        TRouterContext,\n        TRouteContextFn,\n        TBeforeLoadFn\n      >,\n      | 'caseSensitive'\n      | 'parseParams'\n      | 'stringifyParams'\n      | 'path'\n      | 'id'\n      | 'params'\n    >,\n  ) {\n    super({\n      ...(options as any),\n      id: '404',\n    })\n  }\n}\n", "import warning from 'tiny-warning'\nimport { createRoute } from './route'\n\nimport { useMatch } from './useMatch'\nimport { useLoaderDeps } from './useLoaderDeps'\nimport { useLoaderData } from './useLoaderData'\nimport { useSearch } from './useSearch'\nimport { useParams } from './useParams'\nimport { useNavigate } from './useNavigate'\nimport { useRouter } from './useRouter'\nimport type { UseParamsRoute } from './useParams'\nimport type { UseMatchRoute } from './useMatch'\nimport type { UseSearchRoute } from './useSearch'\nimport type {\n  AnyContext,\n  AnyRoute,\n  AnyRouter,\n  Constrain,\n  ConstrainLiteral,\n  FileBaseRouteOptions,\n  FileRoutesByPath,\n  LazyRouteOptions,\n  RegisteredRouter,\n  ResolveParams,\n  Route,\n  RouteById,\n  RouteConstraints,\n  RouteIds,\n  RouteLoaderFn,\n  UpdatableRouteOptions,\n} from '@tanstack/router-core'\nimport type { UseLoaderDepsRoute } from './useLoaderDeps'\nimport type { UseLoaderDataRoute } from './useLoaderData'\nimport type { UseRouteContextRoute } from './useRouteContext'\n\nexport function createFileRoute<\n  TFilePath extends keyof FileRoutesByPath,\n  TParentRoute extends AnyRoute = FileRoutesByPath[TFilePath]['parentRoute'],\n  TId extends RouteConstraints['TId'] = FileRoutesByPath[TFilePath]['id'],\n  TPath extends RouteConstraints['TPath'] = FileRoutesByPath[TFilePath]['path'],\n  TFullPath extends\n    RouteConstraints['TFullPath'] = FileRoutesByPath[TFilePath]['fullPath'],\n>(\n  path: TFilePath,\n): FileRoute<TFilePath, TParentRoute, TId, TPath, TFullPath>['createRoute'] {\n  return new FileRoute<TFilePath, TParentRoute, TId, TPath, TFullPath>(path, {\n    silent: true,\n  }).createRoute\n}\n\n/** \n  @deprecated It's no longer recommended to use the `FileRoute` class directly.\n  Instead, use `createFileRoute('/path/to/file')(options)` to create a file route.\n*/\nexport class FileRoute<\n  TFilePath extends keyof FileRoutesByPath,\n  TParentRoute extends AnyRoute = FileRoutesByPath[TFilePath]['parentRoute'],\n  TId extends RouteConstraints['TId'] = FileRoutesByPath[TFilePath]['id'],\n  TPath extends RouteConstraints['TPath'] = FileRoutesByPath[TFilePath]['path'],\n  TFullPath extends\n    RouteConstraints['TFullPath'] = FileRoutesByPath[TFilePath]['fullPath'],\n> {\n  silent?: boolean\n\n  constructor(\n    public path: TFilePath,\n    _opts?: { silent: boolean },\n  ) {\n    this.silent = _opts?.silent\n  }\n\n  createRoute = <\n    TSearchValidator = undefined,\n    TParams = ResolveParams<TPath>,\n    TRouteContextFn = AnyContext,\n    TBeforeLoadFn = AnyContext,\n    TLoaderDeps extends Record<string, any> = {},\n    TLoaderFn = undefined,\n    TChildren = unknown,\n  >(\n    options?: FileBaseRouteOptions<\n      TParentRoute,\n      TId,\n      TPath,\n      TSearchValidator,\n      TParams,\n      TLoaderDeps,\n      TLoaderFn,\n      AnyContext,\n      TRouteContextFn,\n      TBeforeLoadFn\n    > &\n      UpdatableRouteOptions<\n        TParentRoute,\n        TId,\n        TFullPath,\n        TParams,\n        TSearchValidator,\n        TLoaderFn,\n        TLoaderDeps,\n        AnyContext,\n        TRouteContextFn,\n        TBeforeLoadFn\n      >,\n  ): Route<\n    TParentRoute,\n    TPath,\n    TFullPath,\n    TFilePath,\n    TId,\n    TSearchValidator,\n    TParams,\n    AnyContext,\n    TRouteContextFn,\n    TBeforeLoadFn,\n    TLoaderDeps,\n    TLoaderFn,\n    TChildren,\n    unknown\n  > => {\n    warning(\n      this.silent,\n      'FileRoute is deprecated and will be removed in the next major version. Use the createFileRoute(path)(options) function instead.',\n    )\n    const route = createRoute(options as any)\n    ;(route as any).isRoot = false\n    return route as any\n  }\n}\n\n/** \n  @deprecated It's recommended not to split loaders into separate files.\n  Instead, place the loader function in the the main route file, inside the\n  `createFileRoute('/path/to/file)(options)` options.\n*/\nexport function FileRouteLoader<\n  TFilePath extends keyof FileRoutesByPath,\n  TRoute extends FileRoutesByPath[TFilePath]['preLoaderRoute'],\n>(\n  _path: TFilePath,\n): <TLoaderFn>(\n  loaderFn: Constrain<\n    TLoaderFn,\n    RouteLoaderFn<\n      TRoute['parentRoute'],\n      TRoute['types']['id'],\n      TRoute['types']['params'],\n      TRoute['types']['loaderDeps'],\n      TRoute['types']['routerContext'],\n      TRoute['types']['routeContextFn'],\n      TRoute['types']['beforeLoadFn']\n    >\n  >,\n) => TLoaderFn {\n  warning(\n    false,\n    `FileRouteLoader is deprecated and will be removed in the next major version. Please place the loader function in the the main route file, inside the \\`createFileRoute('/path/to/file')(options)\\` options`,\n  )\n  return (loaderFn) => loaderFn as any\n}\n\nexport class LazyRoute<TRoute extends AnyRoute> {\n  options: {\n    id: string\n  } & LazyRouteOptions\n\n  constructor(\n    opts: {\n      id: string\n    } & LazyRouteOptions,\n  ) {\n    this.options = opts\n    ;(this as any).$$typeof = Symbol.for('react.memo')\n  }\n\n  useMatch: UseMatchRoute<TRoute['id']> = (opts) => {\n    return useMatch({\n      select: opts?.select,\n      from: this.options.id,\n      structuralSharing: opts?.structuralSharing,\n    } as any) as any\n  }\n\n  useRouteContext: UseRouteContextRoute<TRoute['id']> = (opts) => {\n    return useMatch({\n      from: this.options.id,\n      select: (d: any) => (opts?.select ? opts.select(d.context) : d.context),\n    }) as any\n  }\n\n  useSearch: UseSearchRoute<TRoute['id']> = (opts) => {\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n    return useSearch({\n      select: opts?.select,\n      structuralSharing: opts?.structuralSharing,\n      from: this.options.id,\n    } as any) as any\n  }\n\n  useParams: UseParamsRoute<TRoute['id']> = (opts) => {\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n    return useParams({\n      select: opts?.select,\n      structuralSharing: opts?.structuralSharing,\n      from: this.options.id,\n    } as any) as any\n  }\n\n  useLoaderDeps: UseLoaderDepsRoute<TRoute['id']> = (opts) => {\n    return useLoaderDeps({ ...opts, from: this.options.id } as any)\n  }\n\n  useLoaderData: UseLoaderDataRoute<TRoute['id']> = (opts) => {\n    return useLoaderData({ ...opts, from: this.options.id } as any)\n  }\n\n  useNavigate = () => {\n    const router = useRouter()\n    return useNavigate({ from: router.routesById[this.options.id].fullPath })\n  }\n}\n\nexport function createLazyRoute<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TId extends string = string,\n  TRoute extends AnyRoute = RouteById<TRouter['routeTree'], TId>,\n>(id: ConstrainLiteral<TId, RouteIds<TRouter['routeTree']>>) {\n  return (opts: LazyRouteOptions) => {\n    return new LazyRoute<TRoute>({\n      id: id,\n      ...opts,\n    })\n  }\n}\n\nexport function createLazyFileRoute<\n  TFilePath extends keyof FileRoutesByPath,\n  TRoute extends FileRoutesByPath[TFilePath]['preLoaderRoute'],\n>(id: TFilePath) {\n  return (opts: LazyRouteOptions) => new LazyRoute<TRoute>({ id, ...opts })\n}\n", "import * as React from 'react'\nimport { Outlet } from './Match'\nimport { <PERSON><PERSON>Only } from './ClientOnly'\nimport type { AsyncRouteComponent } from './route'\n\n// If the load fails due to module not found, it may mean a new version of\n// the build was deployed and the user's browser is still using an old version.\n// If this happens, the old version in the user's browser would have an outdated\n// URL to the lazy module.\n// In that case, we want to attempt one window refresh to get the latest.\nfunction isModuleNotFoundError(error: any): boolean {\n  // chrome: \"Failed to fetch dynamically imported module: http://localhost:5173/src/routes/posts.index.tsx?tsr-split\"\n  // firefox: \"error loading dynamically imported module: http://localhost:5173/src/routes/posts.index.tsx?tsr-split\"\n  // safari: \"Importing a module script failed.\"\n  if (typeof error?.message !== 'string') return false\n  return (\n    error.message.startsWith('Failed to fetch dynamically imported module') ||\n    error.message.startsWith('error loading dynamically imported module') ||\n    error.message.startsWith('Importing a module script failed')\n  )\n}\n\nexport function lazyRouteComponent<\n  T extends Record<string, any>,\n  <PERSON><PERSON><PERSON> extends keyof T = 'default',\n>(\n  importer: () => Promise<T>,\n  exportName?: TKey,\n  ssr?: () => boolean,\n): T[TKey] extends (props: infer TProps) => any\n  ? AsyncRouteComponent<TProps>\n  : never {\n  let loadPromise: Promise<any> | undefined\n  let comp: T[TKey] | T['default']\n  let error: any\n  let reload: boolean\n\n  const load = () => {\n    if (typeof document === 'undefined' && ssr?.() === false) {\n      comp = (() => null) as any\n      return Promise.resolve()\n    }\n    if (!loadPromise) {\n      loadPromise = importer()\n        .then((res) => {\n          loadPromise = undefined\n          comp = res[exportName ?? 'default']\n        })\n        .catch((err) => {\n          // We don't want an error thrown from preload in this case, because\n          // there's nothing we want to do about module not found during preload.\n          // Record the error, the rest is handled during the render path.\n          error = err\n          if (isModuleNotFoundError(error)) {\n            if (\n              error instanceof Error &&\n              typeof window !== 'undefined' &&\n              typeof sessionStorage !== 'undefined'\n            ) {\n              // Again, we want to reload one time on module not found error and not enter\n              // a reload loop if there is some other issue besides an old deploy.\n              // That's why we store our reload attempt in sessionStorage.\n              // Use error.message as key because it contains the module path that failed.\n              const storageKey = `tanstack_router_reload:${error.message}`\n              if (!sessionStorage.getItem(storageKey)) {\n                sessionStorage.setItem(storageKey, '1')\n                reload = true\n              }\n            }\n          }\n        })\n    }\n\n    return loadPromise\n  }\n\n  const lazyComp = function Lazy(props: any) {\n    // Now that we're out of preload and into actual render path,\n    if (reload) {\n      // If it was a module loading error,\n      // throw eternal suspense while we wait for window to reload\n      window.location.reload()\n      throw new Promise(() => {})\n    }\n    if (error) {\n      // Otherwise, just throw the error\n      throw error\n    }\n\n    if (!comp) {\n      throw load()\n    }\n\n    if (ssr?.() === false) {\n      return (\n        <ClientOnly fallback={<Outlet />}>\n          {React.createElement(comp, props)}\n        </ClientOnly>\n      )\n    }\n    return React.createElement(comp, props)\n  }\n\n  ;(lazyComp as any).preload = load\n\n  return lazyComp as any\n}\n", "import { RouterCore } from '@tanstack/router-core'\nimport type { RouterHistory } from '@tanstack/history'\nimport type {\n  Any<PERSON><PERSON><PERSON>,\n  CreateRouterFn,\n  RouterConstructorOptions,\n  TrailingSlashOption,\n} from '@tanstack/router-core'\n\nimport type {\n  ErrorRouteComponent,\n  NotFoundRouteComponent,\n  RouteComponent,\n} from './route'\n\ndeclare module '@tanstack/router-core' {\n  export interface RouterOptionsExtensions {\n    /**\n     * The default `component` a route should use if no component is provided.\n     *\n     * @default Outlet\n     * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#defaultcomponent-property)\n     */\n    defaultComponent?: RouteComponent\n    /**\n     * The default `errorComponent` a route should use if no error component is provided.\n     *\n     * @default ErrorComponent\n     * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#defaulterrorcomponent-property)\n     * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/data-loading#handling-errors-with-routeoptionserrorcomponent)\n     */\n    defaultErrorComponent?: ErrorRouteComponent\n    /**\n     * The default `pendingComponent` a route should use if no pending component is provided.\n     *\n     * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#defaultpendingcomponent-property)\n     * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/data-loading#showing-a-pending-component)\n     */\n    defaultPendingComponent?: RouteComponent\n    /**\n     * The default `notFoundComponent` a route should use if no notFound component is provided.\n     *\n     * @default NotFound\n     * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#defaultnotfoundcomponent-property)\n     * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/not-found-errors#default-router-wide-not-found-handling)\n     */\n    defaultNotFoundComponent?: NotFoundRouteComponent\n    /**\n     * A component that will be used to wrap the entire router.\n     *\n     * This is useful for providing a context to the entire router.\n     *\n     * Only non-DOM-rendering components like providers should be used, anything else will cause a hydration error.\n     *\n     * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#wrap-property)\n     */\n    Wrap?: (props: { children: any }) => React.JSX.Element\n    /**\n     * A component that will be used to wrap the inner contents of the router.\n     *\n     * This is useful for providing a context to the inner contents of the router where you also need access to the router context and hooks.\n     *\n     * Only non-DOM-rendering components like providers should be used, anything else will cause a hydration error.\n     *\n     * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#innerwrap-property)\n     */\n    InnerWrap?: (props: { children: any }) => React.JSX.Element\n\n    /**\n     * The default `onCatch` handler for errors caught by the Router ErrorBoundary\n     *\n     * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#defaultoncatch-property)\n     * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/data-loading#handling-errors-with-routeoptionsoncatch)\n     */\n    defaultOnCatch?: (error: Error, errorInfo: React.ErrorInfo) => void\n  }\n}\n\nexport const createRouter: CreateRouterFn = (options) => {\n  return new Router(options)\n}\n\nexport class Router<\n  in out TRouteTree extends AnyRoute,\n  in out TTrailingSlashOption extends TrailingSlashOption = 'never',\n  in out TDefaultStructuralSharingOption extends boolean = false,\n  in out TRouterHistory extends RouterHistory = RouterHistory,\n  in out TDehydrated extends Record<string, any> = Record<string, any>,\n> extends RouterCore<\n  TRouteTree,\n  TTrailingSlashOption,\n  TDefaultStructuralSharingOption,\n  TRouterHistory,\n  TDehydrated\n> {\n  constructor(\n    options: RouterConstructorOptions<\n      TRouteTree,\n      TTrailingSlashOption,\n      TDefaultStructuralSharingOption,\n      TRouterHistory,\n      TDehydrated\n    >,\n  ) {\n    super(options)\n  }\n}\n", "import * as React from 'react'\nimport { Matches } from './Matches'\nimport { getRouterContext } from './routerContext'\nimport type {\n  AnyRouter,\n  RegisteredRouter,\n  RouterOptions,\n} from '@tanstack/router-core'\n\nexport function RouterContextProvider<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TDehydrated extends Record<string, any> = Record<string, any>,\n>({\n  router,\n  children,\n  ...rest\n}: RouterProps<TRouter, TDehydrated> & {\n  children: React.ReactNode\n}) {\n  // Allow the router to update options on the router instance\n  router.update({\n    ...router.options,\n    ...rest,\n    context: {\n      ...router.options.context,\n      ...rest.context,\n    },\n  } as any)\n\n  const routerContext = getRouterContext()\n\n  const provider = (\n    <routerContext.Provider value={router as AnyRouter}>\n      {children}\n    </routerContext.Provider>\n  )\n\n  if (router.options.Wrap) {\n    return <router.options.Wrap>{provider}</router.options.Wrap>\n  }\n\n  return provider\n}\n\nexport function RouterProvider<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TDehydrated extends Record<string, any> = Record<string, any>,\n>({ router, ...rest }: RouterProps<TRouter, TDehydrated>) {\n  return (\n    <RouterContextProvider router={router} {...rest}>\n      <Matches />\n    </RouterContextProvider>\n  )\n}\n\nexport type RouterProps<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TDehydrated extends Record<string, any> = Record<string, any>,\n> = Omit<\n  RouterOptions<\n    TRouter['routeTree'],\n    NonNullable<TRouter['options']['trailingSlash']>,\n    NonNullable<TRouter['options']['defaultStructuralSharing']>,\n    TRouter['history'],\n    TDehydrated\n  >,\n  'context'\n> & {\n  router: TRouter\n  context?: Partial<\n    RouterOptions<\n      TRouter['routeTree'],\n      NonNullable<TRouter['options']['trailingSlash']>,\n      NonNullable<TRouter['options']['defaultStructuralSharing']>,\n      TRouter['history'],\n      TDehydrated\n    >['context']\n  >\n}\n", "import {\n  defaultGetScrollRestorationKey,\n  getCssSelector,\n  scrollRestorationCache,\n  setupScrollRestoration,\n} from '@tanstack/router-core'\nimport { useRouter } from './useRouter'\nimport type {\n  ParsedLocation,\n  ScrollRestorationEntry,\n  ScrollRestorationOptions,\n} from '@tanstack/router-core'\n\nfunction useScrollRestoration() {\n  const router = useRouter()\n  setupScrollRestoration(router, true)\n}\n\n/**\n * @deprecated use createRouter's `scrollRestoration` option instead\n */\nexport function ScrollRestoration(_props: ScrollRestorationOptions) {\n  useScrollRestoration()\n\n  if (process.env.NODE_ENV === 'development') {\n    console.warn(\n      \"The ScrollRestoration component is deprecated. Use createRouter's `scrollRestoration` option instead.\",\n    )\n  }\n\n  return null\n}\n\nexport function useElementScrollRestoration(\n  options: (\n    | {\n        id: string\n        getElement?: () => Window | Element | undefined | null\n      }\n    | {\n        id?: string\n        getElement: () => Window | Element | undefined | null\n      }\n  ) & {\n    getKey?: (location: ParsedLocation) => string\n  },\n): ScrollRestorationEntry | undefined {\n  useScrollRestoration()\n\n  const router = useRouter()\n  const getKey = options.getKey || defaultGetScrollRestorationKey\n\n  let elementSelector = ''\n\n  if (options.id) {\n    elementSelector = `[data-scroll-restoration-id=\"${options.id}\"]`\n  } else {\n    const element = options.getElement?.()\n    if (!element) {\n      return\n    }\n    elementSelector =\n      element instanceof Window ? 'window' : getCssSelector(element)\n  }\n\n  const restoreKey = getKey(router.latestLocation)\n  const byKey = scrollRestorationCache?.state[restoreKey]\n  return byKey?.[elementSelector]\n}\n", "import * as React from 'react'\nimport { useRouter } from './useRouter'\nimport type {\n  BlockerFnArgs,\n  HistoryAction,\n  HistoryLocation,\n} from '@tanstack/history'\nimport type {\n  AnyRoute,\n  AnyRouter,\n  ParseRoute,\n  RegisteredRouter,\n} from '@tanstack/router-core'\n\ninterface ShouldBlockFnLocation<\n  out TRouteId,\n  out TFullPath,\n  out TAllParams,\n  out TFullSearchSchema,\n> {\n  routeId: TRouteId\n  fullPath: TFullPath\n  pathname: string\n  params: TAllParams\n  search: TFullSearchSchema\n}\n\ntype AnyShouldBlockFnLocation = ShouldBlockFnLocation<any, any, any, any>\ntype MakeShouldBlockFnLocationUnion<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TRoute extends AnyRoute = ParseRoute<TRouter['routeTree']>,\n> = TRoute extends any\n  ? ShouldBlockFnLocation<\n      TRoute['id'],\n      TRoute['fullPath'],\n      TRoute['types']['allParams'],\n      TRoute['types']['fullSearchSchema']\n    >\n  : never\n\ntype BlockerResolver<TRouter extends AnyRouter = RegisteredRouter> =\n  | {\n      status: 'blocked'\n      current: MakeShouldBlockFnLocationUnion<TRouter>\n      next: MakeShouldBlockFnLocationUnion<TRouter>\n      action: HistoryAction\n      proceed: () => void\n      reset: () => void\n    }\n  | {\n      status: 'idle'\n      current: undefined\n      next: undefined\n      action: undefined\n      proceed: undefined\n      reset: undefined\n    }\n\ntype ShouldBlockFnArgs<TRouter extends AnyRouter = RegisteredRouter> = {\n  current: MakeShouldBlockFnLocationUnion<TRouter>\n  next: MakeShouldBlockFnLocationUnion<TRouter>\n  action: HistoryAction\n}\n\nexport type ShouldBlockFn<TRouter extends AnyRouter = RegisteredRouter> = (\n  args: ShouldBlockFnArgs<TRouter>,\n) => boolean | Promise<boolean>\nexport type UseBlockerOpts<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TWithResolver extends boolean = boolean,\n> = {\n  shouldBlockFn: ShouldBlockFn<TRouter>\n  enableBeforeUnload?: boolean | (() => boolean)\n  disabled?: boolean\n  withResolver?: TWithResolver\n}\n\ntype LegacyBlockerFn = () => Promise<any> | any\ntype LegacyBlockerOpts = {\n  blockerFn?: LegacyBlockerFn\n  condition?: boolean | any\n}\n\nfunction _resolveBlockerOpts(\n  opts?: UseBlockerOpts | LegacyBlockerOpts | LegacyBlockerFn,\n  condition?: boolean | any,\n): UseBlockerOpts {\n  if (opts === undefined) {\n    return {\n      shouldBlockFn: () => true,\n      withResolver: false,\n    }\n  }\n\n  if ('shouldBlockFn' in opts) {\n    return opts\n  }\n\n  if (typeof opts === 'function') {\n    const shouldBlock = Boolean(condition ?? true)\n\n    const _customBlockerFn = async () => {\n      if (shouldBlock) return await opts()\n      return false\n    }\n\n    return {\n      shouldBlockFn: _customBlockerFn,\n      enableBeforeUnload: shouldBlock,\n      withResolver: false,\n    }\n  }\n\n  const shouldBlock = Boolean(opts.condition ?? true)\n  const fn = opts.blockerFn\n\n  const _customBlockerFn = async () => {\n    if (shouldBlock && fn !== undefined) {\n      return await fn()\n    }\n    return shouldBlock\n  }\n\n  return {\n    shouldBlockFn: _customBlockerFn,\n    enableBeforeUnload: shouldBlock,\n    withResolver: fn === undefined,\n  }\n}\n\nexport function useBlocker<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TWithResolver extends boolean = false,\n>(\n  opts: UseBlockerOpts<TRouter, TWithResolver>,\n): TWithResolver extends true ? BlockerResolver<TRouter> : void\n\n/**\n * @deprecated Use the shouldBlockFn property instead\n */\nexport function useBlocker(blockerFnOrOpts?: LegacyBlockerOpts): BlockerResolver\n\n/**\n * @deprecated Use the UseBlockerOpts object syntax instead\n */\nexport function useBlocker(\n  blockerFn?: LegacyBlockerFn,\n  condition?: boolean | any,\n): BlockerResolver\n\nexport function useBlocker(\n  opts?: UseBlockerOpts | LegacyBlockerOpts | LegacyBlockerFn,\n  condition?: boolean | any,\n): BlockerResolver | void {\n  const {\n    shouldBlockFn,\n    enableBeforeUnload = true,\n    disabled = false,\n    withResolver = false,\n  } = _resolveBlockerOpts(opts, condition)\n\n  const router = useRouter()\n  const { history } = router\n\n  const [resolver, setResolver] = React.useState<BlockerResolver>({\n    status: 'idle',\n    current: undefined,\n    next: undefined,\n    action: undefined,\n    proceed: undefined,\n    reset: undefined,\n  })\n\n  React.useEffect(() => {\n    const blockerFnComposed = async (blockerFnArgs: BlockerFnArgs) => {\n      function getLocation(\n        location: HistoryLocation,\n      ): AnyShouldBlockFnLocation {\n        const parsedLocation = router.parseLocation(undefined, location)\n        const matchedRoutes = router.getMatchedRoutes(parsedLocation)\n        if (matchedRoutes.foundRoute === undefined) {\n          throw new Error(`No route found for location ${location.href}`)\n        }\n        return {\n          routeId: matchedRoutes.foundRoute.id,\n          fullPath: matchedRoutes.foundRoute.fullPath,\n          pathname: parsedLocation.pathname,\n          params: matchedRoutes.routeParams,\n          search: parsedLocation.search,\n        }\n      }\n\n      const current = getLocation(blockerFnArgs.currentLocation)\n      const next = getLocation(blockerFnArgs.nextLocation)\n\n      const shouldBlock = await shouldBlockFn({\n        action: blockerFnArgs.action,\n        current,\n        next,\n      })\n      if (!withResolver) {\n        return shouldBlock\n      }\n\n      if (!shouldBlock) {\n        return false\n      }\n\n      const promise = new Promise<boolean>((resolve) => {\n        setResolver({\n          status: 'blocked',\n          current,\n          next,\n          action: blockerFnArgs.action,\n          proceed: () => resolve(false),\n          reset: () => resolve(true),\n        })\n      })\n\n      const canNavigateAsync = await promise\n      setResolver({\n        status: 'idle',\n        current: undefined,\n        next: undefined,\n        action: undefined,\n        proceed: undefined,\n        reset: undefined,\n      })\n\n      return canNavigateAsync\n    }\n\n    return disabled\n      ? undefined\n      : history.block({ blockerFn: blockerFnComposed, enableBeforeUnload })\n  }, [\n    shouldBlockFn,\n    enableBeforeUnload,\n    disabled,\n    withResolver,\n    history,\n    router,\n  ])\n\n  return resolver\n}\n\nconst _resolvePromptBlockerArgs = (\n  props: PromptProps | LegacyPromptProps,\n): UseBlockerOpts => {\n  if ('shouldBlockFn' in props) {\n    return { ...props }\n  }\n\n  const shouldBlock = Boolean(props.condition ?? true)\n  const fn = props.blockerFn\n\n  const _customBlockerFn = async () => {\n    if (shouldBlock && fn !== undefined) {\n      return await fn()\n    }\n    return shouldBlock\n  }\n\n  return {\n    shouldBlockFn: _customBlockerFn,\n    enableBeforeUnload: shouldBlock,\n    withResolver: fn === undefined,\n  }\n}\n\nexport function Block<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TWithResolver extends boolean = boolean,\n>(opts: PromptProps<TRouter, TWithResolver>): React.ReactNode\n\n/**\n *  @deprecated Use the UseBlockerOpts property instead\n */\nexport function Block(opts: LegacyPromptProps): React.ReactNode\n\nexport function Block(opts: PromptProps | LegacyPromptProps): React.ReactNode {\n  const { children, ...rest } = opts\n  const args = _resolvePromptBlockerArgs(rest)\n\n  const resolver = useBlocker(args)\n  return children\n    ? typeof children === 'function'\n      ? children(resolver as any)\n      : children\n    : null\n}\n\ntype LegacyPromptProps = {\n  blockerFn?: LegacyBlockerFn\n  condition?: boolean | any\n  children?: React.ReactNode | ((params: BlockerResolver) => React.ReactNode)\n}\n\ntype PromptProps<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TWithResolver extends boolean = boolean,\n  TParams = TWithResolver extends true ? BlockerResolver<TRouter> : void,\n> = UseBlockerOpts<TRouter, TWithResolver> & {\n  children?: React.ReactNode | ((params: TParams) => React.ReactNode)\n}\n", "import { useMatch } from './useMatch'\nimport type {\n  AnyRout<PERSON>,\n  RegisteredRouter,\n  UseRouteContextBaseOptions,\n  UseRouteContextOptions,\n  UseRouteContextResult,\n} from '@tanstack/router-core'\n\nexport type UseRouteContextRoute<out TFrom> = <\n  TRouter extends AnyRouter = RegisteredRouter,\n  TSelected = unknown,\n>(\n  opts?: UseRouteContextBaseOptions<TRouter, TFrom, true, TSelected>,\n) => UseRouteContextResult<TRouter, TFrom, true, TSelected>\n\nexport function useRouteContext<\n  TRouter extends AnyRouter = RegisteredRouter,\n  const TFrom extends string | undefined = undefined,\n  TStrict extends boolean = true,\n  TSelected = unknown,\n>(\n  opts: UseRouteContextOptions<TRouter, TFrom, TStrict, TSelected>,\n): UseRouteContextResult<TRouter, TFrom, TStrict, TSelected> {\n  return useMatch({\n    ...(opts as any),\n    select: (match) =>\n      opts.select ? opts.select(match.context) : match.context,\n  }) as UseRouteContextResult<TRouter, TFrom, TStrict, TSelected>\n}\n", "import { useRouterState } from './useRouterState'\nimport type {\n  StructuralSharingOption,\n  ValidateSelected,\n} from './structuralSharing'\nimport type {\n  AnyRouter,\n  RegisteredRouter,\n  RouterState,\n} from '@tanstack/router-core'\n\nexport interface UseLocationBaseOptions<\n  TRouter extends AnyRouter,\n  TSelected,\n  TStructuralSharing extends boolean = boolean,\n> {\n  select?: (\n    state: RouterState<TRouter['routeTree']>['location'],\n  ) => ValidateSelected<TRouter, TSelected, TStructuralSharing>\n}\n\nexport type UseLocationResult<\n  TRouter extends AnyRouter,\n  TSelected,\n> = unknown extends TSelected\n  ? RouterState<TRouter['routeTree']>['location']\n  : TSelected\n\nexport function useLocation<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts?: UseLocationBaseOptions<TRouter, TSelected, TStructuralSharing> &\n    StructuralSharingOption<TRouter, TSelected, TStructuralSharing>,\n): UseLocationResult<TRouter, TSelected> {\n  return useRouterState({\n    select: (state: any) =>\n      opts?.select ? opts.select(state.location) : state.location,\n  } as any) as UseLocationResult<TRouter, TSelected>\n}\n", "import { useRouterState } from './useRouterState'\n\nexport function useCanGoBack() {\n  return useRouterState({ select: (s) => s.location.state.__TSR_index !== 0 })\n}\n", "import type { RouterManagedTag } from '@tanstack/router-core'\n\nexport function Asset({ tag, attrs, children }: RouterManagedTag): any {\n  switch (tag) {\n    case 'title':\n      return (\n        <title {...attrs} suppressHydrationWarning>\n          {children}\n        </title>\n      )\n    case 'meta':\n      return <meta {...attrs} suppressHydrationWarning />\n    case 'link':\n      return <link {...attrs} suppressHydrationWarning />\n    case 'style':\n      return (\n        <style\n          {...attrs}\n          dangerouslySetInnerHTML={{ __html: children as any }}\n        />\n      )\n    case 'script':\n      if ((attrs as any) && (attrs as any).src) {\n        return <script {...attrs} suppressHydrationWarning />\n      }\n      if (typeof children === 'string')\n        return (\n          <script\n            {...attrs}\n            dangerouslySetInnerHTML={{\n              __html: children,\n            }}\n            suppressHydrationWarning\n          />\n        )\n      return null\n    default:\n      return null\n  }\n}\n", "import * as React from 'react'\nimport { Asset } from './Asset'\nimport { useRouter } from './useRouter'\nimport { useRouterState } from './useRouterState'\nimport type { RouterManagedTag } from '@tanstack/router-core'\n\nexport const useTags = () => {\n  const router = useRouter()\n\n  const routeMeta = useRouterState({\n    select: (state) => {\n      return state.matches.map((match) => match.meta!).filter(Boolean)\n    },\n  })\n\n  const meta: Array<RouterManagedTag> = React.useMemo(() => {\n    const resultMeta: Array<RouterManagedTag> = []\n    const metaByAttribute: Record<string, true> = {}\n    let title: RouterManagedTag | undefined\n    ;[...routeMeta].reverse().forEach((metas) => {\n      ;[...metas].reverse().forEach((m) => {\n        if (!m) return\n\n        if (m.title) {\n          if (!title) {\n            title = {\n              tag: 'title',\n              children: m.title,\n            }\n          }\n        } else {\n          const attribute = m.name ?? m.property\n          if (attribute) {\n            if (metaByAttribute[attribute]) {\n              return\n            } else {\n              metaByAttribute[attribute] = true\n            }\n          }\n\n          resultMeta.push({\n            tag: 'meta',\n            attrs: {\n              ...m,\n            },\n          })\n        }\n      })\n    })\n\n    if (title) {\n      resultMeta.push(title)\n    }\n\n    resultMeta.reverse()\n\n    return resultMeta\n  }, [routeMeta])\n\n  const links = useRouterState({\n    select: (state) =>\n      state.matches\n        .map((match) => match.links!)\n        .filter(Boolean)\n        .flat(1)\n        .map((link) => ({\n          tag: 'link',\n          attrs: {\n            ...link,\n          },\n        })) as Array<RouterManagedTag>,\n    structuralSharing: true as any,\n  })\n\n  const preloadMeta = useRouterState({\n    select: (state) => {\n      const preloadMeta: Array<RouterManagedTag> = []\n\n      state.matches\n        .map((match) => router.looseRoutesById[match.routeId]!)\n        .forEach((route) =>\n          router.ssr?.manifest?.routes[route.id]?.preloads\n            ?.filter(Boolean)\n            .forEach((preload) => {\n              preloadMeta.push({\n                tag: 'link',\n                attrs: {\n                  rel: 'modulepreload',\n                  href: preload,\n                },\n              })\n            }),\n        )\n\n      return preloadMeta\n    },\n    structuralSharing: true as any,\n  })\n\n  const headScripts = useRouterState({\n    select: (state) =>\n      (\n        state.matches\n          .map((match) => match.headScripts!)\n          .flat(1)\n          .filter(Boolean) as Array<RouterManagedTag>\n      ).map(({ children, ...script }) => ({\n        tag: 'script',\n        attrs: {\n          ...script,\n        },\n        children,\n      })),\n    structuralSharing: true as any,\n  })\n\n  return uniqBy(\n    [\n      ...meta,\n      ...preloadMeta,\n      ...links,\n      ...headScripts,\n    ] as Array<RouterManagedTag>,\n    (d) => {\n      return JSON.stringify(d)\n    },\n  )\n}\n\n/**\n * @description The `HeadContent` component is used to render meta tags, links, and scripts for the current route.\n * It should be rendered in the `<head>` of your document.\n */\nexport function HeadContent() {\n  const tags = useTags()\n  return tags.map((tag) => (\n    <Asset {...tag} key={`tsr-meta-${JSON.stringify(tag)}`} />\n  ))\n}\n\nfunction uniqBy<T>(arr: Array<T>, fn: (item: T) => string) {\n  const seen = new Set<string>()\n  return arr.filter((item) => {\n    const key = fn(item)\n    if (seen.has(key)) {\n      return false\n    }\n    seen.add(key)\n    return true\n  })\n}\n", "import { Asset } from './Asset'\nimport { useRouterState } from './useRouterState'\nimport { useRouter } from './useRouter'\nimport type { RouterManagedTag } from '@tanstack/router-core'\n\nexport const Scripts = () => {\n  const router = useRouter()\n\n  const assetScripts = useRouterState({\n    select: (state) => {\n      const assetScripts: Array<RouterManagedTag> = []\n      const manifest = router.ssr?.manifest\n\n      if (!manifest) {\n        return []\n      }\n\n      state.matches\n        .map((match) => router.looseRoutesById[match.routeId]!)\n        .forEach((route) =>\n          manifest.routes[route.id]?.assets\n            ?.filter((d) => d.tag === 'script')\n            .forEach((asset) => {\n              assetScripts.push({\n                tag: 'script',\n                attrs: asset.attrs,\n                children: asset.children,\n              } as any)\n            }),\n        )\n\n      return assetScripts\n    },\n    structuralSharing: true as any,\n  })\n\n  const { scripts } = useRouterState({\n    select: (state) => ({\n      scripts: (\n        state.matches\n          .map((match) => match.scripts!)\n          .flat(1)\n          .filter(Boolean) as Array<RouterManagedTag>\n      ).map(({ children, ...script }) => ({\n        tag: 'script',\n        attrs: {\n          ...script,\n          suppressHydrationWarning: true,\n        },\n        children,\n      })),\n    }),\n  })\n\n  const allScripts = [...scripts, ...assetScripts] as Array<RouterManagedTag>\n\n  return (\n    <>\n      {allScripts.map((asset, i) => (\n        <Asset {...asset} key={`tsr-scripts-${asset.tag}-${i}`} />\n      ))}\n    </>\n  )\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAWA,KACG,WAAY;AACX,eAAS,GAAG,GAAG,GAAG;AAChB,eAAQ,MAAM,MAAM,MAAM,KAAK,IAAI,MAAM,IAAI,MAAQ,MAAM,KAAK,MAAM;AAAA,MACxE;AACA,eAAS,uBAAuBA,YAAW,aAAa;AACtD,6BACE,WAAWC,QAAM,oBACf,oBAAoB,MACtB,QAAQ;AAAA,UACN;AAAA,QACF;AACF,YAAI,QAAQ,YAAY;AACxB,YAAI,CAAC,4BAA4B;AAC/B,cAAI,cAAc,YAAY;AAC9B,mBAAS,OAAO,WAAW,MACxB,QAAQ;AAAA,YACP;AAAA,UACF,GACC,6BAA6B;AAAA,QAClC;AACA,sBAAcC,UAAS;AAAA,UACrB,MAAM,EAAE,OAAc,YAAyB;AAAA,QACjD,CAAC;AACD,YAAI,OAAO,YAAY,CAAC,EAAE,MACxB,cAAc,YAAY,CAAC;AAC7B,QAAAC;AAAA,UACE,WAAY;AACV,iBAAK,QAAQ;AACb,iBAAK,cAAc;AACnB,mCAAuB,IAAI,KAAK,YAAY,EAAE,KAAW,CAAC;AAAA,UAC5D;AAAA,UACA,CAACH,YAAW,OAAO,WAAW;AAAA,QAChC;AACA,QAAAI;AAAA,UACE,WAAY;AACV,mCAAuB,IAAI,KAAK,YAAY,EAAE,KAAW,CAAC;AAC1D,mBAAOJ,WAAU,WAAY;AAC3B,qCAAuB,IAAI,KAAK,YAAY,EAAE,KAAW,CAAC;AAAA,YAC5D,CAAC;AAAA,UACH;AAAA,UACA,CAACA,UAAS;AAAA,QACZ;AACA,sBAAc,KAAK;AACnB,eAAO;AAAA,MACT;AACA,eAAS,uBAAuB,MAAM;AACpC,YAAI,oBAAoB,KAAK;AAC7B,eAAO,KAAK;AACZ,YAAI;AACF,cAAI,YAAY,kBAAkB;AAClC,iBAAO,CAAC,SAAS,MAAM,SAAS;AAAA,QAClC,SAAS,OAAO;AACd,iBAAO;AAAA,QACT;AAAA,MACF;AACA,eAAS,uBAAuBA,YAAW,aAAa;AACtD,eAAO,YAAY;AAAA,MACrB;AACA,sBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,+BACxC,+BAA+B,4BAA4B,MAAM,CAAC;AACpE,UAAIC,UAAQ,iBACV,WAAW,eAAe,OAAO,OAAO,KAAK,OAAO,KAAK,IACzDC,YAAWD,QAAM,UACjBG,aAAYH,QAAM,WAClBE,mBAAkBF,QAAM,iBACxB,gBAAgBA,QAAM,eACtB,oBAAoB,OACpB,6BAA6B,OAC7B,OACE,gBAAgB,OAAO,UACvB,gBAAgB,OAAO,OAAO,YAC9B,gBAAgB,OAAO,OAAO,SAAS,gBACnC,yBACA;AACR,cAAQ,uBACN,WAAWA,QAAM,uBAAuBA,QAAM,uBAAuB;AACvE,sBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,8BACxC,+BAA+B,2BAA2B,MAAM,CAAC;AAAA,IACrE,GAAG;AAAA;AAAA;;;AC9FL;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACNA;AAAA;AAAA;AAWA,KACG,WAAY;AACX,eAAS,GAAG,GAAG,GAAG;AAChB,eAAQ,MAAM,MAAM,MAAM,KAAK,IAAI,MAAM,IAAI,MAAQ,MAAM,KAAK,MAAM;AAAA,MACxE;AACA,sBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,+BACxC,+BAA+B,4BAA4B,MAAM,CAAC;AACpE,UAAII,UAAQ,iBACV,OAAO,gBACP,WAAW,eAAe,OAAO,OAAO,KAAK,OAAO,KAAK,IACzD,uBAAuB,KAAK,sBAC5BC,UAASD,QAAM,QACfE,aAAYF,QAAM,WAClBG,WAAUH,QAAM,SAChB,gBAAgBA,QAAM;AACxB,cAAQ,mCAAmC,SACzCI,YACA,aACA,mBACA,UACA,SACA;AACA,YAAI,UAAUH,QAAO,IAAI;AACzB,YAAI,SAAS,QAAQ,SAAS;AAC5B,cAAI,OAAO,EAAE,UAAU,OAAI,OAAO,KAAK;AACvC,kBAAQ,UAAU;AAAA,QACpB,MAAO,QAAO,QAAQ;AACtB,kBAAUE;AAAA,UACR,WAAY;AACV,qBAAS,iBAAiB,cAAc;AACtC,kBAAI,CAAC,SAAS;AACZ,0BAAU;AACV,mCAAmB;AACnB,+BAAe,SAAS,YAAY;AACpC,oBAAI,WAAW,WAAW,KAAK,UAAU;AACvC,sBAAI,mBAAmB,KAAK;AAC5B,sBAAI,QAAQ,kBAAkB,YAAY;AACxC,2BAAQ,oBAAoB;AAAA,gBAChC;AACA,uBAAQ,oBAAoB;AAAA,cAC9B;AACA,iCAAmB;AACnB,kBAAI,SAAS,kBAAkB,YAAY;AACzC,uBAAO;AACT,kBAAI,gBAAgB,SAAS,YAAY;AACzC,kBAAI,WAAW,WAAW,QAAQ,kBAAkB,aAAa;AAC/D,uBAAQ,mBAAmB,cAAe;AAC5C,iCAAmB;AACnB,qBAAQ,oBAAoB;AAAA,YAC9B;AACA,gBAAI,UAAU,OACZ,kBACA,mBACA,yBACE,WAAW,oBAAoB,OAAO;AAC1C,mBAAO;AAAA,cACL,WAAY;AACV,uBAAO,iBAAiB,YAAY,CAAC;AAAA,cACvC;AAAA,cACA,SAAS,yBACL,SACA,WAAY;AACV,uBAAO,iBAAiB,uBAAuB,CAAC;AAAA,cAClD;AAAA,YACN;AAAA,UACF;AAAA,UACA,CAAC,aAAa,mBAAmB,UAAU,OAAO;AAAA,QACpD;AACA,YAAI,QAAQ,qBAAqBC,YAAW,QAAQ,CAAC,GAAG,QAAQ,CAAC,CAAC;AAClE,QAAAF;AAAA,UACE,WAAY;AACV,iBAAK,WAAW;AAChB,iBAAK,QAAQ;AAAA,UACf;AAAA,UACA,CAAC,KAAK;AAAA,QACR;AACA,sBAAc,KAAK;AACnB,eAAO;AAAA,MACT;AACA,sBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,8BACxC,+BAA+B,2BAA2B,MAAM,CAAC;AAAA,IACrE,GAAG;AAAA;AAAA;;;AChGL;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACNA;AAAA;AAAA;AAEA,QAAM,SAAS,CAAC;AAChB,QAAM,iBAAiB,OAAO;AAC9B,QAAM,SAAS,CAACG,SAAQ,aAAa;AACpC,iBAAW,OAAOA,SAAQ;AACzB,YAAI,eAAe,KAAKA,SAAQ,GAAG,GAAG;AACrC,mBAAS,KAAKA,QAAO,GAAG,CAAC;AAAA,QAC1B;AAAA,MACD;AAAA,IACD;AAEA,QAAM,SAAS,CAAC,aAAa,WAAW;AACvC,UAAI,CAAC,QAAQ;AACZ,eAAO;AAAA,MACR;AACA,aAAO,QAAQ,CAAC,KAAK,UAAU;AAC9B,oBAAY,GAAG,IAAI;AAAA,MACpB,CAAC;AACD,aAAO;AAAA,IACR;AAEA,QAAM,UAAU,CAAC,OAAO,aAAa;AACpC,YAAM,SAAS,MAAM;AACrB,UAAI,QAAQ;AACZ,aAAO,EAAE,QAAQ,QAAQ;AACxB,iBAAS,MAAM,KAAK,CAAC;AAAA,MACtB;AAAA,IACD;AAEA,QAAM,gBAAgB,CAAC,QAAQ;AAC9B,aAAO,SAAS,SAAS,KAAK,MAAM,EAAE;AAAA,IACvC;AAEA,QAAM,cAAc,CAAC,MAAM,cAAc;AACxC,UAAIC,eAAc,KAAK,SAAS,EAAE;AAClC,UAAI,UAAW,QAAOA;AACtB,aAAOA,aAAY,YAAY;AAAA,IAChC;AAEA,QAAM,WAAW,OAAO;AACxB,QAAM,UAAU,MAAM;AACtB,QAAM,WAAW,CAAC,UAAU;AAC3B,aAAO,OAAO,WAAW,cAAc,OAAO,SAAS,KAAK;AAAA,IAC7D;AACA,QAAM,WAAW,CAAC,UAAU;AAE3B,aAAO,SAAS,KAAK,KAAK,KAAK;AAAA,IAChC;AACA,QAAM,WAAW,CAAC,UAAU;AAC3B,aAAO,OAAO,SAAS,YACtB,SAAS,KAAK,KAAK,KAAK;AAAA,IAC1B;AACA,QAAM,WAAW,CAAC,UAAU;AAC3B,aAAO,OAAO,SAAS,YACtB,SAAS,KAAK,KAAK,KAAK;AAAA,IAC1B;AACA,QAAM,WAAW,CAAC,UAAU;AAC1B,aAAO,OAAO,SAAS;AAAA,IACzB;AACA,QAAM,aAAa,CAAC,UAAU;AAC7B,aAAO,OAAO,SAAS;AAAA,IACxB;AACA,QAAM,QAAQ,CAAC,UAAU;AACxB,aAAO,SAAS,KAAK,KAAK,KAAK;AAAA,IAChC;AACA,QAAM,QAAQ,CAAC,UAAU;AACxB,aAAO,SAAS,KAAK,KAAK,KAAK;AAAA,IAChC;AAKA,QAAM,gBAAgB;AAAA,MACrB,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,KAAM;AAAA;AAAA;AAAA,IAGP;AACA,QAAM,oBAAoB;AAE1B,QAAM,aAAa;AACnB,QAAM,kBAAkB;AAExB,QAAM,wBAAwB;AAC9B,QAAM,sBAAsB;AAE5B,QAAMC,SAAQ,CAAC,UAAU,YAAY;AACpC,YAAM,sBAAsB,MAAM;AACjC,oBAAY;AACZ,UAAE,QAAQ;AACV,iBAAS,QAAQ,OAAO,OAAO,QAAQ,WAAW;AAAA,MACnD;AAEA,YAAM,WAAW;AAAA,QAChB,oBAAoB;AAAA,QACpB,WAAW;AAAA,QACX,mBAAmB;AAAA,QACnB,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,gBAAgB;AAAA,QAChB,WAAW;AAAA,QACX,UAAU;AAAA,QACV,eAAe;AAAA,QACf,eAAe;AAAA,QACf,eAAe;AAAA,MAChB;AACA,YAAM,OAAO,WAAW,QAAQ;AAChC,UAAI,MAAM;AACT,iBAAS,SAAS;AAClB,iBAAS,OAAO;AAAA,MACjB;AACA,gBAAU,OAAO,UAAU,OAAO;AAClC,UACC,QAAQ,UAAU,YAClB,QAAQ,UAAU,YAClB,QAAQ,UAAU,YACjB;AACD,gBAAQ,SAAS;AAAA,MAClB;AACA,YAAM,QAAQ,QAAQ,UAAU,WAC/B,MACC,QAAQ,UAAU,aAClB,MACA;AAEF,YAAM,UAAU,QAAQ;AACxB,YAAM,eAAe,QAAQ;AAC7B,UAAI,SAAS,QAAQ,OAAO,OAAO,QAAQ,WAAW;AACtD,UAAI,YAAY;AAChB,YAAM,UAAU,QAAQ;AACxB,YAAM,UAAU,QAAQ;AACxB,YAAM,UAAU,UAAU,KAAK;AAC/B,UAAI;AACJ,UAAI,UAAU;AACd,YAAM,gBAAgB,QAAQ,WAAW;AACzC,YAAM,gBAAgB,QAAQ,WAAW;AACzC,YAAM,gBAAgB,QAAQ,WAAW;AACzC,YAAM,gBAAgB,QAAQ,WAAW;AAEzC,UAAI,QAAQ,YAAY,WAAW,SAAS,MAAM,GAAG;AACpD,mBAAW,SAAS,OAAO;AAAA,MAC5B;AAEA,UAAI,CAAC,SAAS,QAAQ,GAAG;AACxB,YAAI,MAAM,QAAQ,GAAG;AACpB,cAAI,SAAS,QAAQ,GAAG;AACvB,mBAAO;AAAA,UACR;AACA,cAAI,CAAC,SAAS;AACb,oBAAQ,cAAc;AACtB,oBAAQ,cAAc;AAAA,UACvB;AACA,iBAAO,aAAaA,OAAM,MAAM,KAAK,QAAQ,GAAG,OAAO,IAAI;AAAA,QAC5D;AACA,YAAI,MAAM,QAAQ,GAAG;AACpB,cAAI,SAAS,QAAQ,GAAG;AACvB,mBAAO;AAAA,UACR;AACA,iBAAO,aAAaA,OAAM,MAAM,KAAK,QAAQ,GAAG,OAAO,IAAI;AAAA,QAC5D;AACA,YAAI,SAAS,QAAQ,GAAG;AACvB,cAAI,SAAS,UAAU,GAAG;AACzB,mBAAO;AAAA,UACR;AACA,iBAAO,iBAAiBA,OAAM,MAAM,KAAK,QAAQ,GAAG,OAAO,IAAI;AAAA,QAChE;AACA,YAAI,QAAQ,QAAQ,GAAG;AACtB,mBAAS,CAAC;AACV,kBAAQ,OAAO;AACf,cAAI,SAAS;AACZ,oBAAQ,cAAc;AACtB,oBAAQ,cAAc;AAAA,UACvB;AACA,cAAI,CAAC,SAAS;AACb,gCAAoB;AAAA,UACrB;AACA,kBAAQ,UAAU,CAAC,UAAU;AAC5B,sBAAU;AACV,gBAAI,SAAS;AACZ,sBAAQ,cAAc;AAAA,YACvB;AACA,mBAAO;AAAA,eACL,WAAW,UAAU,KAAK,UAC3BA,OAAM,OAAO,OAAO;AAAA,YACrB;AAAA,UACD,CAAC;AACD,cAAI,SAAS;AACZ,mBAAO;AAAA,UACR;AACA,cAAI,SAAS;AACZ,mBAAO,MAAM,OAAO,KAAK,IAAI,IAAI;AAAA,UAClC;AACA,iBAAO,MAAM,UAAU,OAAO,KAAK,MAAM,OAAO,IAAI,WAClD,UAAU,KAAK,aAAa;AAAA,QAC/B,WAAW,SAAS,QAAQ,KAAK,SAAS,QAAQ,GAAG;AACpD,cAAI,MAAM;AAMT,mBAAO,KAAK,UAAU,OAAO,QAAQ,CAAC;AAAA,UACvC;AAEG,cAAIC;AACP,cAAI,eAAe;AAClB,YAAAA,UAAS,OAAO,QAAQ;AAAA,UACzB,WAAW,eAAe;AACzB,gBAAIF,eAAc,SAAS,SAAS,EAAE;AACtC,gBAAI,CAAC,cAAc;AAClB,cAAAA,eAAcA,aAAY,YAAY;AAAA,YACvC;AACA,YAAAE,UAAS,OAAOF;AAAA,UACjB,WAAW,eAAe;AACzB,YAAAE,UAAS,OAAO,SAAS,SAAS,CAAC;AAAA,UACpC,WAAW,eAAe;AACzB,YAAAA,UAAS,OAAO,SAAS,SAAS,CAAC;AAAA,UACpC;AAEG,cAAI,SAAS,QAAQ,GAAG;AACtB,mBAAOA,UAAS;AAAA,UAClB;AACA,iBAAOA;AAAA,QACX,WAAW,SAAS,QAAQ,GAAG;AAC9B,cAAI,MAAM;AAIT,mBAAO,KAAK,UAAU,OAAO,QAAQ,CAAC;AAAA,UACvC;AACG,iBAAO,WAAW;AAAA,QACpB,WAAW,CAAC,SAAS,QAAQ,GAAG;AACjC,cAAI,MAAM;AAIT,mBAAO,KAAK,UAAU,QAAQ,KAAK;AAAA,UACpC;AACA,iBAAO,OAAO,QAAQ;AAAA,QACvB,OAAO;AACN,mBAAS,CAAC;AACV,kBAAQ,OAAO;AACf,8BAAoB;AACpB,iBAAO,UAAU,CAAC,KAAK,UAAU;AAChC,sBAAU;AACV,mBAAO;AAAA,eACL,UAAU,KAAK,UAChBD,OAAM,KAAK,OAAO,IAAI,OACrB,UAAU,KAAK,OAChBA,OAAM,OAAO,OAAO;AAAA,YACrB;AAAA,UACD,CAAC;AACD,cAAI,SAAS;AACZ,mBAAO;AAAA,UACR;AACA,iBAAO,MAAM,UAAU,OAAO,KAAK,MAAM,OAAO,IAAI,WAClD,UAAU,KAAK,aAAa;AAAA,QAC/B;AAAA,MACD;AAEA,YAAM,QAAQ,QAAQ,mBAAmB,wBAAwB;AACjE,eAAS,SAAS,QAAQ,OAAO,CAAC,MAAM,MAAM,MAAM,WAAW,OAAO,WAAW;AAChF,YAAI,MAAM;AACT,cAAI,QAAQ,QAAS,QAAO;AAC5B,gBAAM,QAAQ,KAAK,WAAW,CAAC;AAC/B,gBAAM,SAAS,KAAK,WAAW,CAAC;AAChC,cAAI,QAAQ,KAAK;AAEhB,kBAAM,aAAa,QAAQ,SAAU,OAAQ,SAAS,QAAS;AAC/D,kBAAME,OAAM,YAAY,WAAW,YAAY;AAC/C,mBAAO,SAASA,OAAM;AAAA,UACvB;AACA,iBAAO,cAAc,YAAY,OAAO,YAAY,CAAC,IAAI,cAAc,YAAY,QAAQ,YAAY,CAAC;AAAA,QACzG;AAEA,YAAI,MAAM;AACT,iBAAO,cAAc,YAAY,KAAK,WAAW,CAAC,GAAG,YAAY,CAAC;AAAA,QACnE;AAEA,YACC,QAAQ,QACR,CAAC,QACD,CAAC,WAAW,KAAK,OAAO,OAAO,QAAQ,CAAC,CAAC,GACxC;AACD,iBAAO;AAAA,QACR;AAEA,YAAI,WAAW;AACd,cAAI,aAAa,SAAS,QAAQ,kBAAkB;AACnD,mBAAO,OAAO;AAAA,UACf;AACA,iBAAO;AAAA,QACR;AAEA,YAAI,kBAAkB,KAAK,IAAI,GAAG;AAEjC,iBAAO,cAAc,IAAI;AAAA,QAC1B;AAEA,YAAI,QAAQ,WAAW,CAAC,gBAAgB,KAAK,IAAI,GAAG;AACnD,iBAAO;AAAA,QACR;AAEA,cAAM,MAAM,YAAY,KAAK,WAAW,CAAC,GAAG,YAAY;AACxD,YAAI,QAAQ,IAAI,SAAS,GAAG;AAC3B,iBAAO,cAAc,GAAG;AAAA,QACzB;AAEA,eAAO,SAAS,OAAO,KAAK,MAAM,EAAE;AAAA,MACrC,CAAC;AAED,UAAI,SAAS,KAAK;AACjB,iBAAS,OAAO,QAAQ,SAAS,MAAM;AAAA,MACxC;AACA,UAAI,QAAQ,iBAAiB;AAE5B,iBAAS,OACP,QAAQ,uBAAuB,QAAQ,EACvC,QAAQ,SAAS,OAAO,eAAe,UAAU;AAAA,MACpD;AACA,UAAI,QAAQ,MAAM;AACjB,iBAAS,QAAQ,SAAS;AAAA,MAC3B;AACA,aAAO;AAAA,IACR;AAEA,IAAAF,OAAM,UAAU;AAEhB,WAAO,UAAUA;AAAA;AAAA;A;;;;ACvUV,SAAS,WAAc;EAC5B,SAAS;AACX,GAA6C;AACrC,QAAA,UAAU,MAAM,QAAQ;AAE9B,MAAI,QAAQ,oBAAoB,EAAE,WAAW,WAAW;AAChD,UAAA;EAAA;AAGR,MAAI,QAAQ,oBAAoB,EAAE,WAAW,SAAS;AAC9C,UAAA,QAAQ,oBAAoB,EAAE;EAAA;AAGtC,SAAO,CAAC,QAAQ,oBAAoB,EAAE,MAAM,OAAO;AACrD;AAEO,SAAS,MACd,OAIA;AACA,QAAM,YAAQ,wBAAC,YAAY,EAAA,GAAG,MAAO,CAAA;AACrC,MAAI,MAAM,UAAU;AAClB,eAAA,wBAAc,gBAAN,EAAe,UAAU,MAAM,UAAW,UAAM,MAAA,CAAA;EAAA;AAEnD,SAAA;AACT;AAEA,SAAS,WACP,OAImB;AACnB,QAAM,CAAC,IAAI,IAAI,WAAW,KAAK;AAExB,SAAA,MAAM,SAAS,IAAI;AAC5B;;;;;AC3CO,SAAS,cAAc,OAK3B;AACK,QAAA,iBAAiB,MAAM,kBAAkB;AAG7C,aAAA;IAAC;IAAA;MACC,aAAa,MAAM;MACnB,SAAS,MAAM;MACf,UAAU,CAAC,EAAE,OAAO,MAAA,MAAY;AAC9B,YAAI,OAAO;AACF,iBAAM,qBAAc,gBAAgB;YACzC;YACA;UAAA,CACD;QAAA;AAGH,eAAO,MAAM;MAAA;IACf;EACF;AAEJ;AAEA,IAAM,oBAAN,cAAsC,iBAOnC;EAPH,cAAA;AAAA,UAAA,GAAA,SAAA;AAQU,SAAA,QAAA,EAAE,OAAO,KAAK;EAAA;EACtB,OAAO,yBAAyB,OAAY;AAC1C,WAAO,EAAE,UAAU,MAAM,YAAA,EAAc;EAAA;EAEzC,OAAO,yBAAyB,OAAc;AAC5C,WAAO,EAAE,MAAM;EAAA;EAEjB,QAAQ;AACN,SAAK,SAAS,EAAE,OAAO,KAAA,CAAM;EAAA;EAE/B,mBACE,WAKA,WACM;AACN,QAAI,UAAU,SAAS,UAAU,aAAa,KAAK,MAAM,UAAU;AACjE,WAAK,MAAM;IAAA;EACb;EAEF,kBAAkB,OAAc,WAAsB;AAChD,QAAA,KAAK,MAAM,SAAS;AACjB,WAAA,MAAM,QAAQ,OAAO,SAAS;IAAA;EACrC;EAEF,SAAS;AAEA,WAAA,KAAK,MAAM,SAAS;MACzB,OACE,KAAK,MAAM,aAAa,KAAK,MAAM,YAAA,IAC/B,OACA,KAAK,MAAM;MACjB,OAAO,MAAM;AACX,aAAK,MAAM;MAAA;IACb,CACD;EAAA;AAEL;AAEgB,SAAA,eAAe,EAAE,MAAA,GAAyB;AAClD,QAAA,CAAC,MAAM,OAAO,IAAU,gBAAS,IAAqC;AAG1E,aAAA,0BAAC,OAAA,EAAI,OAAO,EAAE,SAAS,SAAS,UAAU,OACxC,GAAA,UAAA;QAAC,0BAAA,OAAA,EAAI,OAAO,EAAE,SAAS,QAAQ,YAAY,UAAU,KAAK,QAAA,GACxD,UAAA;UAAA,yBAAC,UAAA,EAAO,OAAO,EAAE,UAAU,OAAA,GAAU,UAAqB,wBAAA,CAAA;UAC1D;QAAC;QAAA;UACC,OAAO;YACL,YAAY;YACZ,UAAU;YACV,QAAQ;YACR,SAAS;YACT,YAAY;YACZ,cAAc;UAChB;UACA,SAAS,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAC;UAE/B,UAAA,OAAO,eAAe;QAAA;MAAA;IACzB,EAAA,CACF;QAAA,yBACC,OAAI,EAAA,OAAO,EAAE,QAAQ,SAAA,EAAA,CAAY;IACjC,WAAA,yBACE,OACC,EAAA,cAAA;MAAC;MAAA;QACC,OAAO;UACL,UAAU;UACV,QAAQ;UACR,cAAc;UACd,SAAS;UACT,OAAO;UACP,UAAU;QACZ;QAEC,UAAA,MAAM,cAAU,yBAAC,QAAM,EAAA,UAAA,MAAM,QAAA,CAAQ,IAAU;MAAA;IAAA,EAAA,CAEpD,IACE;EAAA,EAAA,CACN;AAEJ;;;;;ACzFO,SAAS,WAAW,EAAE,UAAU,WAAW,KAAA,GAAyB;AACzE,SAAO,YAAY,QAChB,yBAAAG,aAAAA,QAAM,UAAN,EAAgB,SAAS,CAAA,QAEzB,yBAAAA,aAAAA,QAAM,UAAN,EAAgB,UAAS,SAAA,CAAA;AAE9B;AAqBA,SAAS,cAAuB;AAC9B,SAAOA,aAAAA,QAAM;IACX;IACA,MAAM;IACN,MAAM;EACR;AACF;AAEA,SAAS,YAAY;AACnB,SAAO,MAAM;EAAC;AAChB;A;;;;;;;;;;ACjDO,SAAS,SACd,OACA,WAAkD,CAAC,MAAM,GAC9C;AACX,QAAM,YAAQ;IACZ,MAAM;IACN,MAAM,MAAM;IACZ,MAAM,MAAM;IACZ;IACA;EACF;AAEO,SAAA;AACT;AAEgB,SAAA,QAAW,MAAS,MAAS;AAC3C,MAAI,OAAO,GAAG,MAAM,IAAI,GAAG;AAClB,WAAA;EAAA;AAIP,MAAA,OAAO,SAAS,YAChB,SAAS,QACT,OAAO,SAAS,YAChB,SAAS,MACT;AACO,WAAA;EAAA;AAGL,MAAA,gBAAgB,OAAO,gBAAgB,KAAK;AAC9C,QAAI,KAAK,SAAS,KAAK,KAAa,QAAA;AACpC,eAAW,CAAC,GAAG,CAAC,KAAK,MAAM;AACzB,UAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,KAAK,IAAI,CAAC,CAAC,EAAU,QAAA;IAAA;AAElD,WAAA;EAAA;AAGL,MAAA,gBAAgB,OAAO,gBAAgB,KAAK;AAC9C,QAAI,KAAK,SAAS,KAAK,KAAa,QAAA;AACpC,eAAW,KAAK,MAAM;AACpB,UAAI,CAAC,KAAK,IAAI,CAAC,EAAU,QAAA;IAAA;AAEpB,WAAA;EAAA;AAGH,QAAA,QAAQ,OAAO,KAAK,IAAI;AAC9B,MAAI,MAAM,WAAW,OAAO,KAAK,IAAI,EAAE,QAAQ;AACtC,WAAA;EAAA;AAGT,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAEnC,QAAA,CAAC,OAAO,UAAU,eAAe,KAAK,MAAM,MAAM,CAAC,CAAW,KAC9D,CAAC,OAAO,GAAG,KAAK,MAAM,CAAC,CAAY,GAAG,KAAK,MAAM,CAAC,CAAY,CAAC,GAC/D;AACO,aAAA;IAAA;EACT;AAEK,SAAA;AACT;A;;;;;;;;;ACpEA,IAAM,gBAAsB,qBAAyB,IAAK;AAEnD,SAAS,mBAAmB;AAC7B,MAAA,OAAO,aAAa,aAAa;AAC5B,WAAA;EAAA;AAGT,MAAI,OAAO,wBAAwB;AACjC,WAAO,OAAO;EAAA;AAGhB,SAAO,yBAAyB;AAEzB,SAAA;AACT;;;AClBO,SAAS,UAAwD,MAE5D;AACV,QAAM,QAAc,kBAAW,iBAAA,CAAkB;AACjD;IACE,IAAG,QAAA,OAAA,SAAA,KAAM,SAAQ,SAAS,CAAC;IAC3B;EACF;AACO,SAAA;AACT;;;ACgBO,SAAS,eAKd,MAC0C;AAC1C,QAAM,gBAAgB,UAAmB;IACvC,OAAM,QAAA,OAAA,SAAA,KAAM,YAAW;EAAA,CACxB;AACK,QAAA,UAAS,QAAA,OAAA,SAAA,KAAM,WAAU;AACzB,QAAA,qBACJ,sBAAiE,MAAS;AAE5E,SAAO,SAAS,OAAO,SAAS,CAAC,UAAU;AACzC,QAAI,QAAA,OAAA,SAAA,KAAM,QAAQ;AAChB,UAAI,KAAK,qBAAqB,OAAO,QAAQ,0BAA0B;AACrE,cAAM,WAAW;UACf,eAAe;UACf,KAAK,OAAO,KAAK;QACnB;AACA,uBAAe,UAAU;AAClB,eAAA;MAAA;AAEF,aAAA,KAAK,OAAO,KAAK;IAAA;AAEnB,WAAA;EAAA,CACR;AACH;;;;ACxDa,IAAA,eAAqB,qBAAkC,MAAS;AAGtE,IAAM,oBAA0B;EACrC;AACF;;;ACsEO,SAAS,SAQd,MAQ6E;AAC7E,QAAM,iBAAuB;IAC3B,KAAK,OAAO,oBAAoB;EAClC;AAEA,QAAM,iBAAiB,eAAe;IACpC,QAAQ,CAAC,UAAe;AAChB,YAAA,QAAQ,MAAM,QAAQ;QAAK,CAAC,MAChC,KAAK,OAAO,KAAK,SAAS,EAAE,UAAU,EAAE,OAAO;MACjD;AACA;QACE,GAAG,KAAK,eAAe,SAAS,CAAC;QACjC,kBAAkB,KAAK,OAAO,yBAAyB,KAAK,IAAI,MAAM,kBAAkB;MAC1F;AAEA,UAAI,UAAU,QAAW;AAChB,eAAA;MAAA;AAGT,aAAO,KAAK,SAAS,KAAK,OAAO,KAAK,IAAI;IAC5C;IACA,mBAAmB,KAAK;EAAA,CAClB;AAED,SAAA;AACT;;;AC9DO,SAAS,cAOd,MAOyD;AACzD,SAAO,SAAS;IACd,MAAM,KAAK;IACX,QAAQ,KAAK;IACb,mBAAmB,KAAK;IACxB,QAAQ,CAAC,MAAW;AAClB,aAAO,KAAK,SAAS,KAAK,OAAO,EAAE,UAAU,IAAI,EAAE;IAAA;EACrD,CACM;AACV;;;ACrCO,SAAS,cAMd,MACgD;AAChD,QAAM,EAAE,QAAQ,GAAG,KAAA,IAAS;AAC5B,SAAO,SAAS;IACd,GAAG;IACH,QAAQ,CAAC,MAAM;AACb,aAAO,SAAS,OAAO,EAAE,UAAU,IAAI,EAAE;IAAA;EAC3C,CACD;AACH;;;ACMO,SAAS,UAQd,MAWA;AACA,SAAO,SAAS;IACd,MAAM,KAAK;IACX,QAAQ,KAAK;IACb,aAAa,KAAK;IAClB,mBAAmB,KAAK;IACxB,QAAQ,CAAC,UAAe;AACtB,aAAO,KAAK,SAAS,KAAK,OAAO,MAAM,MAAM,IAAI,MAAM;IAAA;EACzD,CACD;AACH;;;AC7BO,SAAS,UAQd,MAWA;AACA,SAAO,SAAS;IACd,MAAM,KAAK;IACX,QAAQ,KAAK;IACb,aAAa,KAAK;IAClB,mBAAmB,KAAK;IACxB,QAAQ,CAAC,UAAe;AACtB,aAAO,KAAK,SAAS,KAAK,OAAO,MAAM,MAAM,IAAI,MAAM;IAAA;EACzD,CACD;AACH;A;;;AClFO,SAAS,YAGd,cAEkC;AAC5B,QAAA,EAAE,SAAS,IAAI,UAAU;AAE/B,SAAa;IACX,CAAC,YAA6B;AAC5B,aAAO,SAAS;QACd,MAAM,gBAAA,OAAA,SAAA,aAAc;QACpB,GAAG;MAAA,CACJ;IACH;IACA,CAAC,gBAAA,OAAA,SAAA,aAAc,MAAM,QAAQ;EAC/B;AACF;AAEO,SAAS,SAMd,OAAuE;AACvE,QAAM,SAAS,UAAU;AAEnB,QAAA,mBAAyB,cAMrB,IAAI;AACd,EAAM,iBAAU,MAAM;AAChB,QAAA,iBAAiB,YAAY,OAAO;AACtC,aAAO,SAAS;QACd,GAAG;MAAA,CACJ;AACD,uBAAiB,UAAU;IAAA;EAC7B,GACC,CAAC,QAAQ,KAAK,CAAC;AACX,SAAA;AACT;A;;;;;;;;ACpDO,SAAS,kBACd,IACG;AACG,QAAA,QAAc,cAAO,EAAE;AAC7B,QAAM,UAAU;AAEV,QAAA,MAAY,cAAO,IAAI,SAAqB,MAAM,QAAQ,GAAG,IAAI,CAAC;AACxE,SAAO,IAAI;AACb;AAEO,IAAMC,mBACX,OAAO,WAAW,cAAoB,yBAAwB;AAKzD,SAAS,YAAe,OAAoB;AAE3C,QAAA,MAAY,cAAqC;IACrD;IACA,MAAM;EAAA,CACP;AAEK,QAAA,UAAU,IAAI,QAAQ;AAK5B,MAAI,UAAU,SAAS;AACrB,QAAI,UAAU;MACZ;MACA,MAAM;IACR;EAAA;AAIF,SAAO,IAAI,QAAQ;AACrB;AA2BgB,SAAA,wBACd,KACA,UACA,8BAAwD,CACxD,GAAA,UAAkC,CAAA,GACL;AAC7B,QAAM,kCAAwC;IAC5C,OAAO,yBAAyB;EAClC;AAEM,QAAA,cAAoB,cAAoC,IAAI;AAElE,EAAM,iBAAU,MAAM;AACpB,QACE,CAAC,IAAI,WACL,CAAC,gCAAgC,WACjC,QAAQ,UACR;AACA;IAAA;AAGF,gBAAY,UAAU,IAAI,qBAAqB,CAAC,CAAC,KAAK,MAAM;AAC1D,eAAS,KAAK;IAAA,GACb,2BAA2B;AAElB,gBAAA,QAAQ,QAAQ,IAAI,OAAO;AAEvC,WAAO,MAAM;;AACX,OAAA,KAAA,YAAY,YAAZ,OAAA,SAAA,GAAqB,WAAA;IACvB;EAAA,GACC,CAAC,UAAU,6BAA6B,QAAQ,UAAU,GAAG,CAAC;AAEjE,SAAO,YAAY;AACrB;AAeO,SAAS,gBAAmB,KAA6B;AACxD,QAAA,WAAiB,cAAU,IAAI;AACrC,EAAM,2BAAoB,KAAK,MAAM,SAAS,SAAU,CAAA,CAAE;AACnD,SAAA;AACT;A;;;;;;;AC5GO,SAAS,eAAe;AAC7B,QAAM,SAAS,UAAU;AACzB,QAAM,qBAA2B,cAAO,EAAE,QAAQ,SAAS,MAAA,CAAO;AAClE,QAAM,YAAY,eAAe;IAC/B,QAAQ,CAAC,EAAE,WAAAC,WAAAA,MAAgBA;EAAA,CAC5B;AAED,QAAM,CAAC,iBAAiB,kBAAkB,IAAU,gBAAS,KAAK;AAElE,QAAM,oBAAoB,eAAe;IACvC,QAAQ,CAAC,MAAM,EAAE,QAAQ,KAAK,CAAC,MAAM,EAAE,WAAW,SAAS;IAC3D,mBAAmB;EAAA,CACpB;AAEK,QAAA,oBAAoB,YAAY,SAAS;AAEzC,QAAA,eAAe,aAAa,mBAAmB;AAC/C,QAAA,uBAAuB,YAAY,YAAY;AAErD,QAAM,gBAAgB,aAAa;AAC7B,QAAA,wBAAwB,YAAY,aAAa;AAEnD,MAAA,CAAC,OAAO,UAAU;AACb,WAAA,kBAAkB,CAAC,OAAmB;AAC3C,yBAAmB,IAAI;AACvB,MAAM,uBAAgB,MAAM;AACvB,WAAA;AACH,2BAAmB,KAAK;MAAA,CACzB;IACH;EAAA;AAKF,EAAM,iBAAU,MAAM;AACpB,UAAM,QAAQ,OAAO,QAAQ,UAAU,OAAO,IAAI;AAE5C,UAAA,eAAe,OAAO,cAAc;MACxC,IAAI,OAAO,eAAe;MAC1B,QAAQ;MACR,QAAQ;MACR,MAAM;MACN,OAAO;MACP,wBAAwB;IAAA,CACzB;AAGC,QAAA,cAAc,OAAO,eAAe,IAAI,MACxC,cAAc,aAAa,IAAI,GAC/B;AACA,aAAO,eAAe,EAAE,GAAG,cAAc,SAAS,KAAA,CAAM;IAAA;AAG1D,WAAO,MAAM;AACL,YAAA;IACR;EACC,GAAA,CAAC,QAAQ,OAAO,OAAO,CAAC;AAG3B,EAAAC,iBAAgB,MAAM;AAEjB,QAAA,OAAO,WAAW,eAAe,OAAO,aACxC,mBAAmB,QAAQ,WAAW,UACrC,mBAAmB,QAAQ,SAC7B;AACA;IAAA;AAEF,uBAAmB,UAAU,EAAE,QAAQ,SAAS,KAAK;AAErD,UAAM,UAAU,YAAY;AACtB,UAAA;AACF,cAAM,OAAO,KAAK;MAAA,SACX,KAAK;AACZ,gBAAQ,MAAM,GAAG;MAAA;IAErB;AAEQ,YAAA;EAAA,GACP,CAAC,MAAM,CAAC;AAEX,EAAAA,iBAAgB,MAAM;AAEhB,QAAA,qBAAqB,CAAC,WAAW;AACnC,aAAO,KAAK;QACV,MAAM;;QACN,GAAG,sBAAsB,OAAO,KAAK;MAAA,CACtC;IAAA;EAEF,GAAA,CAAC,mBAAmB,QAAQ,SAAS,CAAC;AAEzC,EAAAA,iBAAgB,MAAM;AAEhB,QAAA,yBAAyB,CAAC,eAAe;AAC3C,aAAO,KAAK;QACV,MAAM;QACN,GAAG,sBAAsB,OAAO,KAAK;MAAA,CACtC;IAAA;EAEF,GAAA,CAAC,eAAe,uBAAuB,MAAM,CAAC;AAEjD,EAAAA,iBAAgB,MAAM;AAEhB,QAAA,wBAAwB,CAAC,cAAc;AACzC,aAAO,KAAK;QACV,MAAM;QACN,GAAG,sBAAsB,OAAO,KAAK;MAAA,CACtC;AAEM,aAAA,QAAQ,SAAS,CAAC,OAAO;QAC9B,GAAG;QACH,QAAQ;QACR,kBAAkB,EAAE;MAAA,EACpB;AAEF,uBAAiB,MAAM;IAAA;EAExB,GAAA,CAAC,cAAc,sBAAsB,MAAM,CAAC;AAExC,SAAA;AACT;A;;;;;;;AC1HO,SAAS,cAAc,OAI3B;AAED,QAAM,WAAW,eAAe;IAC9B,QAAQ,CAAC,MAAM,aAAa,EAAE,SAAS,QAAQ,IAAI,EAAE,MAAM;EAAA,CAC5D;AAGC,aAAA;IAAC;IAAA;MACC,aAAa,MAAM;MACnB,SAAS,CAAC,OAAO,cAAc;;AACzB,YAAA,WAAW,KAAK,GAAG;AACf,WAAA,KAAA,MAAA,YAAA,OAAA,SAAA,GAAA,KAAA,OAAU,OAAO,SAAA;QAAS,OAC3B;AACC,gBAAA;QAAA;MAEV;MACA,gBAAgB,CAAC,EAAE,MAAA,MAA8B;;AAC3C,YAAA,WAAW,KAAK,GAAG;AACd,kBAAA,KAAA,MAAM,aAAN,OAAA,SAAA,GAAA,KAAA,OAAiB,KAAA;QAAK,OACxB;AACC,gBAAA;QAAA;MAEV;MAEC,UAAM,MAAA;IAAA;EACT;AAEJ;AAEO,SAAS,wBAAwB;AAC/B,aAAA,yBAAC,KAAA,EAAE,UAAS,YAAA,CAAA;AACrB;;;;ACxCO,SAAS,aAAa,OAAY;AAChC,aAAA,yBAAA,8BAAA,EAAG,UAAA,MAAM,SAAS,CAAA;AAC3B;A;;;ACCgB,SAAA,oBACd,QACA,OACA,MACA;AACI,MAAA,CAAC,MAAM,QAAQ,mBAAmB;AAChC,QAAA,OAAO,QAAQ,0BAA0B;AAC3C,iBAAQ,yBAAA,OAAO,QAAQ,0BAAf,EAAwC,KAAY,CAAA;IAAA;AAG1D,QAAA,MAAwC;AAC1C;QACE,MAAM,QAAQ;QACd,yDAAyD,MAAM,EAAE;MACnE;IAAA;AAGF,eAAA,yBAAQ,uBAAsB,CAAA,CAAA;EAAA;AAGhC,aAAQ,yBAAA,MAAM,QAAQ,mBAAd,EAAgC,KAAY,CAAA;AACtD;A;;;;;;;ACxBO,SAAS,WAAW;EACzB;EACA;AACF,GAIG;AACG,MAAA,OAAO,aAAa,aAAa;AAC5B,WAAA;EAAA;AAIP,aAAA;IAAC;IAAA;MACC,WAAU;MACV,yBAAyB;QACvB,QAAQ;UACN;WACC,OAAO,SAAS,OACb;MACZ,aAAAC,SAAM,SAAS,SAAA,GAAY,EAAE,QAAQ,WAAY,CAAA,CAAC,QACtC;UACJ;QAEC,EAAA,OAAO,OAAO,EACd,KAAK,IAAI;MAAA;IACd;EACF;AAEJ;;;ACvBO,SAAS,oBAAoB;AAClC,QAAM,SAAS,UAAU;AACnB,QAAA,SACJ,OAAO,QAAQ,2BAA2B;AACtC,QAAA,UAAU,OAAO,OAAO,cAAc;AAC5C,QAAM,cACJ,YAAY,+BAA+B,OAAO,cAAc,IAC5D,UACA;AAEN,MAAI,CAAC,OAAO,qBAAqB,CAAC,OAAO,UAAU;AAC1C,WAAA;EAAA;AAIP,aAAA;IAAC;IAAA;MACC,UAAU,IAAI,cAAc,SAAU,CAAA,KAAK,KAAK,UAAU,UAAU,CAAC,IAAI,KAAK,UAAU,WAAW,CAAC;MACpG,KAAK;IAAA;EACP;AAEJ;;;ACPO,IAAM,QAAc,aAAK,SAAS,UAAU;EACjD;AACF,GAEG;;AACD,QAAM,SAAS,UAAU;AACzB,QAAM,UAAU,eAAe;IAC7B,QAAQ,CAAC,MAAA;;AAAM,cAAAC,MAAA,EAAE,QAAQ,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO,MAAtC,OAAA,SAAAA,IAAyC;IAAA;EAAA,CACzD;AAED;IACE;IACA,uCAAuC,OAAO;EAChD;AAEM,QAAA,QAAkB,OAAO,WAAW,OAAO;AAEjD,QAAM,mBACJ,MAAM,QAAQ,oBAAoB,OAAO,QAAQ;AAEnD,QAAM,iBAAiB,uBAAoB,yBAAA,kBAAA,CAAA,CAAiB,IAAK;AAEjE,QAAM,sBACJ,MAAM,QAAQ,kBAAkB,OAAO,QAAQ;AAEjD,QAAM,eAAe,MAAM,QAAQ,WAAW,OAAO,QAAQ;AAE7D,QAAM,yBAAyB,MAAM;;IAEhC,MAAM,QAAQ,uBACf,KAAA,OAAO,QAAQ,kBAAf,OAAA,SAAA,GAA8B,QAAQ;MACtC,MAAM,QAAQ;AAEZ,QAAA;;KAEH,CAAC,MAAM,UAAU,MAAM,QAAQ,oBAC/B,MAAM,QAAQ,kBACb,sBACC,KAAA,MAAM,QAAQ,mBAAd,OAAA,SAAA,GAAsC,YAC/B,mBACN;;AAEA,QAAA,wBAAwB,sBAC1B,gBACA;AAEE,QAAA,2BAA2B,yBAC7B,gBACA;AAEJ,QAAM,WAAW,eAAe;IAC9B,QAAQ,CAAC,MAAM,EAAE;EAAA,CAClB;AAED,QAAM,gBAAgB,eAAe;IACnC,QAAQ,CAAC,MAAM;;AACP,YAAA,QAAQ,EAAE,QAAQ,UAAU,CAAC,MAAM,EAAE,OAAO,OAAO;AACzD,cAAOA,MAAA,EAAE,QAAQ,QAAQ,CAAC,MAAnB,OAAA,SAAAA,IAAsB;IAAA;EAC/B,CACD;AAED,aAEI,0BAAA,8BAAA,EAAA,UAAA;QAAC,yBAAA,aAAa,UAAb,EAAsB,OAAO,SAC5B,cAAC,yBAAA,0BAAA,EAAyB,UAAU,gBAClC,cAAA;MAAC;MAAA;QACC,aAAa,MAAM;QACnB,gBAAgB,uBAAuB;QACvC,SAAS,CAAC,OAAO,cAAc;AAEzB,cAAA,WAAW,KAAK,EAAS,OAAA;AACrB,mCAAA,OAAO,yBAAyB,OAAO,EAAE;AACjD,0BAAA,OAAA,SAAA,aAAe,OAAO,SAAA;QACxB;QAEA,cAAA;UAAC;UAAA;YACC,UAAU,CAAC,UAAU;AAIjB,kBAAA,CAAC,0BACA,MAAM,WAAW,MAAM,YAAY,WACnC,CAAC,MAAM,WAAW,CAAC,MAAM;AAEpB,sBAAA;AAED,qBAAM,sBAAc,wBAAwB,KAAY;YACjE;YAEA,cAAA,yBAAC,YAAA,EAAW,QAAkB,CAAA;UAAA;QAAA;MAChC;IAAA,EAAA,CAEJ,EACF,CAAA;IACC,kBAAkB,eAAe,OAAO,QAAQ,wBAE7C,0BAAA,8BAAA,EAAA,UAAA;UAAA,yBAAC,YAAW,CAAA,CAAA;UAAA,yBACX,mBAAkB,CAAA,CAAA;IAAA,EAAA,CACrB,IACE;EAAA,EAAA,CACN;AAEJ,CAAC;AASD,SAAS,aAAa;AACpB,QAAM,SAAS,UAAU;AAEzB,QAAM,kBAAwB;IAC5B;EACF;AAGE,aAAA;IAAC;IAAA;MAEC,0BAAwB;MACxB,KAAK,CAAC,OAAO;AAET,YAAA,OACC,gBAAgB,YAAY,UAC3B,gBAAgB,QAAQ,SAAS,OAAO,eAAe,OACzD;AACA,iBAAO,KAAK;YACV,MAAM;YACN,GAAG,sBAAsB,OAAO,KAAK;UAAA,CACtC;AACD,0BAAgB,UAAU,OAAO;QAAA;MACnC;IACF;IAdK,OAAO,eAAe,MAAM;EAenC;AAEJ;AAEO,IAAM,aAAmB,aAAK,SAAS,eAAe;EAC3D;AACF,GAEQ;;AACN,QAAM,SAAS,UAAU;AAEzB,QAAM,EAAE,OAAO,KAAK,QAAA,IAAY,eAAe;IAC7C,QAAQ,CAAC,MAAM;AACP,YAAA,aAAa,EAAE,QAAQ,UAAU,CAAC,MAAM,EAAE,OAAO,OAAO;AACxDC,YAAAA,SAAQ,EAAE,QAAQ,UAAU;AAClC,YAAMC,WAAUD,OAAM;AAEhB,YAAA,YACH,OAAO,WAAWC,QAAO,EAAe,QAAQ,eACjD,OAAO,QAAQ;AACjB,YAAM,cAAc,aAAA,OAAA,SAAA,UAAY;QAC9B,SAAAA;QACA,YAAYD,OAAM;QAClB,QAAQA,OAAM;QACd,QAAQA,OAAM;MAAA,CAAA;AAEhB,YAAME,OAAM,cAAc,KAAK,UAAU,WAAW,IAAI;AAEjD,aAAA;QACL,KAAAA;QACA,SAAAD;QACA,OAAO,KAAKD,QAAO,CAAC,MAAM,UAAU,OAAO,CAAC;MAC9C;IACF;IACA,mBAAmB;EAAA,CACpB;AAEK,QAAA,QAAQ,OAAO,WAAW,OAAO;AAEjC,QAAA,MAAY,gBAAQ,MAAM;AAC9B,UAAM,OAAO,MAAM,QAAQ,aAAa,OAAO,QAAQ;AACvD,QAAI,MAAM;AACD,iBAAA,yBAAC,MAAA,CAAA,GAAU,GAAK;IAAA;AAEzB,eAAA,yBAAQ,QAAO,CAAA,CAAA;EAAA,GACd,CAAC,KAAK,MAAM,QAAQ,WAAW,OAAO,QAAQ,gBAAgB,CAAC;AAElE,QAAM,uBACH,MAAM,QAAQ,kBAAkB,OAAO,QAAQ,0BAChD;AAEE,MAAA,MAAM,WAAW,YAAY;AAC/B,cAAU,WAAW,MAAM,KAAK,GAAG,2BAA2B;AAC9D,WAAO,oBAAoB,QAAQ,OAAO,MAAM,KAAK;EAAA;AAGnD,MAAA,MAAM,WAAW,cAAc;AAGjC,cAAU,WAAW,MAAM,KAAK,GAAG,2BAA2B;AAM9D,WAAM,KAAA,OAAO,SAAS,MAAM,EAAE,MAAxB,OAAA,SAAA,GAA2B;EAAA;AAG/B,MAAA,MAAM,WAAW,SAAS;AAM5B,QAAI,OAAO,UAAU;AAEjB,iBAAA;QAAC;QAAA;UACC,OAAO,MAAM;UACb,OAAO;UACP,MAAM;YACJ,gBAAgB;UAAA;QAClB;MACF;IAAA;AAIJ,UAAM,MAAM;EAAA;AAGV,MAAA,MAAM,WAAW,WAAW;AAE9B,UAAM,eACJ,MAAM,QAAQ,gBAAgB,OAAO,QAAQ;AAE/C,QAAI,gBAAgB,GAAC,KAAA,OAAO,SAAS,MAAM,EAAE,MAAxB,OAAA,SAAA,GAA2B,oBAAmB;AAE7D,UAAA,CAAC,OAAO,UAAU;AACpB,cAAM,oBAAoB,wBAA8B;AAEhD,gBAAA,QAAA,EAAU,KAAK,MAAM;AAC3B,iBAAO,YAAY,MAAM,IAAI,CAAC,UAAU;YACtC,GAAG;YACH;UAAA,EACA;QAAA,CACH;AAED,mBAAW,MAAM;AACf,4BAAkB,QAAQ;AAG1B,iBAAO,YAAY,MAAM,IAAI,CAAC,UAAU;YACtC,GAAG;YACH,mBAAmB;UAAA,EACnB;QAAA,GACD,YAAY;MAAA;IACjB;AAEF,WAAM,KAAA,OAAO,SAAS,MAAM,EAAE,MAAxB,OAAA,SAAA,GAA2B;EAAA;AAG5B,SAAA;AACT,CAAC;AAEM,IAAM,SAAe,aAAK,SAAS,aAAa;AACrD,QAAM,SAAS,UAAU;AACnB,QAAA,UAAgB,mBAAW,YAAY;AAC7C,QAAM,UAAU,eAAe;IAC7B,QAAQ,CAAC,MAAA;;AAAM,cAAA,KAAA,EAAE,QAAQ,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO,MAAtC,OAAA,SAAA,GAAyC;IAAA;EAAA,CACzD;AAEK,QAAA,QAAQ,OAAO,WAAW,OAAO;AAEvC,QAAM,uBAAuB,eAAe;IAC1C,QAAQ,CAAC,MAAM;AACb,YAAM,UAAU,EAAE;AAClB,YAAM,cAAc,QAAQ,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO;AACxD;QACE;QACA,4CAA4C,OAAO;MACrD;AACA,aAAO,YAAY;IAAA;EACrB,CACD;AAED,QAAM,eAAe,eAAe;IAClC,QAAQ,CAAC,MAAM;;AACb,YAAM,UAAU,EAAE;AAClB,YAAM,QAAQ,QAAQ,UAAU,CAAC,MAAM,EAAE,OAAO,OAAO;AAChD,cAAA,KAAA,QAAQ,QAAQ,CAAC,MAAjB,OAAA,SAAA,GAAoB;IAAA;EAC7B,CACD;AAED,MAAI,sBAAsB;AACjB,WAAA,oBAAoB,QAAQ,OAAO,MAAS;EAAA;AAGrD,MAAI,CAAC,cAAc;AACV,WAAA;EAAA;AAGT,QAAM,gBAAY,yBAAC,OAAM,EAAA,SAAS,aAAc,CAAA;AAE1C,QAAA,iBAAiB,OAAO,QAAQ,8BAAA,yBACnC,OAAO,QAAQ,yBAAf,CAAuC,CAAA,IACtC;AAEJ,MAAI,YAAY,aAAa;AAC3B,eAAA,yBACS,kBAAN,EAAe,UAAU,gBAAiB,UAAU,UAAA,CAAA;EAAA;AAIlD,SAAA;AACT,CAAC;;;ACjSM,SAAS,UAAU;AACxB,QAAM,SAAS,UAAU;AAEnB,QAAA,iBAAiB,OAAO,QAAQ,8BAAA,0BACnC,OAAO,QAAQ,yBAAf,CAAuC,CAAA,IACtC;AAGE,QAAA,mBACJ,OAAO,YAAa,OAAO,aAAa,eAAe,OAAO,YAC1D,eACM;AAEZ,QAAM,YACJ,2BAAC,kBAAiB,EAAA,UAAU,gBAC1B,UAAA;QAAA,0BAAC,cAAa,CAAA,CAAA;QAAA,0BACb,cAAa,CAAA,CAAA;EAAA,EAAA,CAChB;AAGK,SAAA,OAAO,QAAQ,gBACpB,0BAAC,OAAO,QAAQ,WAAf,EAA0B,UAAA,MAAA,CAAM,IAEjC;AAEJ;AAEA,SAAS,eAAe;AACtB,QAAM,UAAU,eAAe;IAC7B,QAAQ,CAAC,MAAM;;AACN,cAAA,KAAA,EAAE,QAAQ,CAAC,MAAX,OAAA,SAAA,GAAc;IAAA;EACvB,CACD;AAED,QAAM,WAAW,eAAe;IAC9B,QAAQ,CAAC,MAAM,EAAE;EAAA,CAClB;AAED,aACG,0BAAA,aAAa,UAAb,EAAsB,OAAO,SAC5B,cAAA;IAAC;IAAA;MACC,aAAa,MAAM;MACnB,gBAAgB;MAChB,SAAS,CAAC,UAAU;AAClB;UACE;UACA;QACF;AACA,iCAAQ,OAAO,MAAM,WAAW,MAAM,SAAA,CAAU;MAClD;MAEC,UAAU,cAAA,0BAAC,OAAM,EAAA,QAAkB,CAAA,IAAK;IAAA;EAAA,EAAA,CAE7C;AAEJ;AAcO,SAAS,gBAA8D;AAC5E,QAAM,SAAS,UAAU;AAEV,iBAAA;IACb,QAAQ,CAAC,MAAA;;AAAM,aAAA,CAAC,EAAE,SAAS,OAAM,KAAA,EAAE,qBAAF,OAAA,SAAA,GAAoB,MAAM,EAAE,MAAM;IAAA;IACnE,mBAAmB;EAAA,CACpB;AAED,SAAa;IACX,CAME,SACoE;AACpE,YAAM,EAAE,SAAS,eAAe,OAAO,eAAe,GAAG,KAAA,IAAS;AAE3D,aAAA,OAAO,WAAW,MAAa;QACpC;QACA;QACA;QACA;MAAA,CACD;IACH;IACA,CAAC,MAAM;EACT;AACF;AAoBO,SAAS,WAMd,OAA4E;AAC5E,QAAM,aAAa,cAAc;AAC3B,QAAA,SAAS,WAAW,KAAY;AAElC,MAAA,OAAO,MAAM,aAAa,YAAY;AAChC,WAAA,MAAM,SAAiB,MAAM;EAAA;AAGhC,SAAA,SAAS,MAAM,WAAW;AACnC;AAiBO,SAAS,WAKd,MAEsC;AACtC,SAAO,eAAe;IACpB,QAAQ,CAAC,UAA6C;AACpD,YAAM,UAAU,MAAM;AACtB,cAAO,QAAA,OAAA,SAAA,KAAM,UACT,KAAK,OAAO,OAA8C,IAC1D;IACN;IACA,mBAAmB,QAAA,OAAA,SAAA,KAAM;EAAA,CACnB;AACV;AAEO,SAAS,iBAKd,MAEsC;AAChC,QAAA,iBAAuB,mBAAW,YAAY;AAEpD,SAAO,WAAW;IAChB,QAAQ,CAAC,YAAiD;AACxD,gBAAU,QAAQ;QAChB;QACA,QAAQ,UAAU,CAAC,MAAM,EAAE,OAAO,cAAc;MAClD;AACA,cAAO,QAAA,OAAA,SAAA,KAAM,UAAS,KAAK,OAAO,OAAO,IAAI;IAC/C;IACA,mBAAmB,QAAA,OAAA,SAAA,KAAM;EAAA,CACnB;AACV;AAEO,SAAS,gBAKd,MAEsC;AAChC,QAAA,iBAAuB,mBAAW,YAAY;AAEpD,SAAO,WAAW;IAChB,QAAQ,CAAC,YAAiD;AACxD,gBAAU,QAAQ;QAChB,QAAQ,UAAU,CAAC,MAAM,EAAE,OAAO,cAAc,IAAI;MACtD;AACA,cAAO,QAAA,OAAA,SAAA,KAAM,UAAS,KAAK,OAAO,OAAO,IAAI;IAC/C;IACA,mBAAmB,QAAA,OAAA,SAAA,KAAM;EAAA,CACnB;AACV;;;ACxNgB,SAAA,aAOd,SACA,cACkC;AAClC,QAAM,SAAS,UAAU;AACzB,QAAM,CAAC,iBAAiB,kBAAkB,IAAU,iBAAS,KAAK;AAC5D,QAAA,mBAAyB,eAAO,KAAK;AACrC,QAAA,WAAW,gBAAgB,YAAY;AAEvC,QAAA;;IAEJ,cAAc,OAAO,EAAE,WAAW,SAAA;IAClC,gBAAgB,OAAO,CAAA;IACvB;IACA;IACA,SAAS;IACT,cAAc;IACd;IACA;IACA,iBAAAG;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,GAAG;EAAA,IACD;AAEE,QAAA;;IAEJ,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,OAAO;IACP,MAAM;IACN,gBAAgB;IAChB,GAAG;EAAA,IACD;AAQE,QAAA,OAAsC,gBAAQ,MAAM;AACpD,QAAA;AACE,UAAA,IAAI,GAAG,EAAE,EAAE;AACR,aAAA;IAAA,QACD;IAAA;AACD,WAAA;EAAA,GACN,CAAC,EAAE,CAAC;AAGP,QAAM,gBAAgB,eAAe;IACnC,QAAQ,CAAC,MAAM,EAAE,SAAS;IAC1B,mBAAmB;EAAA,CACpB;AAID,QAAM,OAAO,WAAW;IACtB,QAAQ,CAAC,YAAY;;AAAA,aAAA,QAAQ,UAAQ,KAAA,QAAQ,QAAQ,SAAS,CAAC,MAA1B,OAAA,SAAA,GAA6B;IAAA;EAAA,CACnE;AAED,QAAM,WAAiB,gBAAQ,OAAO,EAAE,GAAG,SAAS,KAAA,IAAS,CAAC,SAAS,IAAI,CAAC;AAE5E,QAAM,OAAa;IACjB,MAAM,OAAO,cAAc,QAAe;;IAE1C,CAAC,QAAQ,UAAU,aAAa;EAClC;AAEM,QAAA,UAAgB,gBAAQ,MAAM;AAClC,QAAI,SAAS,gBAAgB;AACpB,aAAA;IAAA;AAEF,WAAA,eAAe,OAAO,QAAQ;EAAA,GACpC,CAAC,OAAO,QAAQ,gBAAgB,aAAa,SAAS,cAAc,CAAC;AACxE,QAAM,eACJ,oBAAoB,OAAO,QAAQ,uBAAuB;AAE5D,QAAM,WAAW,eAAe;IAC9B,QAAQ,CAAC,MAAM;AACb,UAAI,iBAAA,OAAA,SAAA,cAAe,OAAO;AACxB,cAAM,YAAY;UAChB,EAAE,SAAS;UACX,KAAK;UACL,OAAO;QACT;AACA,YAAI,CAAC,WAAW;AACP,iBAAA;QAAA;MACT,OACK;AACL,cAAM,mBAAmB;UACvB,EAAE,SAAS;UACX,OAAO;QAAA,EACP,MAAM,GAAG;AACX,cAAM,gBAAgB;UACpB,KAAK;UACL,OAAO;QAAA,EACP,MAAM,GAAG;AAEX,cAAM,mBAAmB,cAAc;UACrC,CAAC,GAAG,MAAM,MAAM,iBAAiB,CAAC;QACpC;AACA,YAAI,CAAC,kBAAkB;AACd,iBAAA;QAAA;MACT;AAGE,WAAA,iBAAA,OAAA,SAAA,cAAe,kBAAiB,MAAM;AACxC,cAAM,aAAa,UAAU,EAAE,SAAS,QAAQ,KAAK,QAAQ;UAC3D,SAAS,EAAC,iBAAA,OAAA,SAAA,cAAe;UACzB,iBAAiB,EAAC,iBAAA,OAAA,SAAA,cAAe;QAAA,CAClC;AACD,YAAI,CAAC,YAAY;AACR,iBAAA;QAAA;MACT;AAGF,UAAI,iBAAA,OAAA,SAAA,cAAe,aAAa;AACvB,eAAA,EAAE,SAAS,SAAS,KAAK;MAAA;AAE3B,aAAA;IAAA;EACT,CACD;AAEK,QAAA,YAAkB,oBAAY,MAAM;AACxC,WAAO,aAAa,QAAe,EAAE,MAAM,CAAC,QAAQ;AAClD,cAAQ,KAAK,GAAG;AAChB,cAAQ,KAAK,cAAc;IAAA,CAC5B;EAAA,GACA,CAAC,UAAU,MAAM,CAAC;AAErB,QAAM,4BAAkC;IACtC,CAAC,UAAiD;AAChD,UAAI,SAAA,OAAA,SAAA,MAAO,gBAAgB;AACf,kBAAA;MAAA;IAEd;IACA,CAAC,SAAS;EACZ;AAEA;IACE;IACA;IACA,EAAE,YAAY,QAAQ;IACtB,EAAE,UAAU,CAAC,CAAC,YAAY,EAAE,YAAY,YAAY;EACtD;AAEA,EAAAC,iBAAgB,MAAM;AACpB,QAAI,iBAAiB,SAAS;AAC5B;IAAA;AAEE,QAAA,CAAC,YAAY,YAAY,UAAU;AAC3B,gBAAA;AACV,uBAAiB,UAAU;IAAA;EAE5B,GAAA,CAAC,UAAU,WAAW,OAAO,CAAC;AAEjC,MAAI,SAAS,YAAY;AAChB,WAAA;MACL,GAAG;MACH,KAAK;MACL;MACA,MAAM;MACN,GAAI,YAAY,EAAE,SAAS;MAC3B,GAAI,UAAU,EAAE,OAAO;MACvB,GAAI,YAAY,EAAE,SAAS;MAC3B,GAAI,SAAS,EAAE,MAAM;MACrB,GAAI,aAAa,EAAE,UAAU;MAC7B,GAAI,WAAW,EAAE,QAAQ;MACzB,GAAI,WAAW,EAAE,QAAQ;MACzB,GAAI,gBAAgB,EAAE,aAAa;MACnC,GAAI,gBAAgB,EAAE,aAAa;MACnC,GAAI,gBAAgB,EAAE,aAAa;IACrC;EAAA;AAII,QAAA,cAAc,CAAC,MAAkB;AACrC,QACE,CAAC,YACD,CAAC,YAAY,CAAC,KACd,CAAC,EAAE,qBACF,CAAC,UAAU,WAAW,YACvB,EAAE,WAAW,GACb;AACA,QAAE,eAAe;AAEjB,sCAAU,MAAM;AACd,2BAAmB,IAAI;MAAA,CACxB;AAED,YAAM,QAAQ,OAAO,UAAU,cAAc,MAAM;AAC3C,cAAA;AACN,2BAAmB,KAAK;MAAA,CACzB;AAID,aAAO,OAAO,SAAS;QACrB,GAAG;QACH;QACA;QACA;QACA,iBAAAD;QACA;QACA;MAAA,CACM;IAAA;EAEZ;AAGM,QAAA,cAAc,CAAC,MAAkB;AACrC,QAAI,SAAU;AACd,QAAI,SAAS;AACD,gBAAA;IAAA;EAEd;AAEA,QAAM,mBAAmB;AAEnB,QAAA,cAAc,CAAC,MAAkB;AACrC,QAAI,SAAU;AACR,UAAA,cAAe,EAAE,UAAU,CAAC;AAElC,QAAI,SAAS;AACX,UAAI,YAAY,gBAAgB;AAC9B;MAAA;AAGU,kBAAA,iBAAiB,WAAW,MAAM;AAC5C,oBAAY,iBAAiB;AACnB,kBAAA;MAAA,GACT,YAAY;IAAA;EAEnB;AAEM,QAAA,cAAc,CAAC,MAAkB;AACrC,QAAI,SAAU;AACR,UAAA,cAAe,EAAE,UAAU,CAAC;AAElC,QAAI,YAAY,gBAAgB;AAC9B,mBAAa,YAAY,cAAc;AACvC,kBAAY,iBAAiB;IAAA;EAEjC;AAEA,QAAM,kBACJ,CAAC,aACD,CAAC,MAA2D;;AAC1D,KAAA,KAAA,EAAE,YAAF,OAAA,SAAA,GAAA,KAAA,CAAA;AACA,aAAS,OAAO,OAAO,EAAE,QAAQ,CAAC,YAAY;AAC5C,UAAI,EAAE,iBAAkB;AACxB,cAAS,CAAC;IAAA,CACX;EACH;AAGI,QAAA,sBAA+D,WAChE,iBAAiB,aAAoB,CAAE,CAAA,KAAK,CAAA,IAC7C,CAAC;AAGL,QAAM,wBACJ,WAAW,CAAA,IAAK,iBAAiB,eAAe,CAAA,CAAE;AAEpD,QAAM,oBAAoB;IACxB;IACA,oBAAoB;IACpB,sBAAsB;EAErB,EAAA,OAAO,OAAO,EACd,KAAK,GAAG;AAEX,QAAM,gBAAgB;IACpB,GAAG;IACH,GAAG,oBAAoB;IACvB,GAAG,sBAAsB;EAC3B;AAEO,SAAA;IACL,GAAG;IACH,GAAG;IACH,GAAG;IACH,MAAM,WACF,SACA,KAAK,iBACH,OAAO,QAAQ,WAAW,KAAK,eAAe,IAAI,IAClD,OAAO,QAAQ,WAAW,KAAK,IAAI;IACzC,KAAK;IACL,SAAS,gBAAgB,CAAC,SAAS,WAAW,CAAC;IAC/C,SAAS,gBAAgB,CAAC,SAAS,WAAW,CAAC;IAC/C,cAAc,gBAAgB,CAAC,cAAc,WAAW,CAAC;IACzD,cAAc,gBAAgB,CAAC,cAAc,WAAW,CAAC;IACzD,cAAc,gBAAgB,CAAC,cAAc,gBAAgB,CAAC;IAC9D,UAAU,CAAC,CAAC;IACZ;IACA,GAAI,OAAO,KAAK,aAAa,EAAE,UAAU,EAAE,OAAO,cAAc;IAChE,GAAI,qBAAqB,EAAE,WAAW,kBAAkB;IACxD,GAAI,YAAY;MACd,MAAM;MACN,iBAAiB;IACnB;IACA,GAAI,YAAY,EAAE,eAAe,UAAU,gBAAgB,OAAO;IAClE,GAAI,mBAAmB,EAAE,sBAAsB,gBAAgB;EACjE;AACF;AAkIO,SAAS,WACd,MACsB;AACtB,SAAa,mBAAW,SAAS,YAAY,OAAO,KAAK;AACvD,eAAA,0BAAQ,MAAM,EAAA,GAAI,OAAe,UAAU,MAAM,IAAA,CAAU;EAAA,CAC5D;AACH;AAEO,IAAM,OAAiC;EAC5C,CAAC,OAAO,QAAQ;AACd,UAAM,EAAE,UAAU,GAAG,KAAA,IAAS;AACxB,UAAA;MACJ,MAAM;MACN,KAAK;MACL,GAAG;IAAA,IACD,aAAa,MAAa,GAAG;AAEjC,UAAM,WACJ,OAAO,KAAK,aAAa,aACrB,KAAK,SAAS;MACZ,UAAW,UAAkB,aAAa,MAAM;IAAA,CACjD,IACD,KAAK;AAEP,QAAA,OAAO,aAAa,aAAa;AAGnC,aAAO,UAAU;IAAA;AAGnB,WAAa;MACX,WAAW,WAAW;MACtB;QACE,GAAG;QACH,KAAK;MACP;MACA;IACF;EAAA;AAEJ;AAEA,SAAS,YAAY,GAAe;AAC3B,SAAA,CAAC,EAAE,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE;AACpD;AAkBa,IAAA,cAAkC,CAAC,YAAY;AACnD,SAAA;AACT;;;AC/dO,SAAS,YAGd,IAA2D;AAC3D,SAAO,IAAI,SAAuB,EAAE,GAAA,CAAI;AAC1C;AAEO,IAAM,WAAN,cAGG,aAA2B;;;;EAInC,YAAY,EAAE,GAAA,GAAmB;AACzB,UAAA,EAAE,GAAA,CAAI;AAGd,SAAA,WAA+B,CAAC,SAAS;AACvC,aAAO,SAAS;QACd,QAAQ,QAAA,OAAA,SAAA,KAAM;QACd,MAAM,KAAK;QACX,mBAAmB,QAAA,OAAA,SAAA,KAAM;MAAA,CACnB;IACV;AAEA,SAAA,kBAA6C,CAAC,SAAS;AACrD,aAAO,SAAS;QACd,MAAM,KAAK;QACX,QAAQ,CAAC,OAAO,QAAA,OAAA,SAAA,KAAM,UAAS,KAAK,OAAO,EAAE,OAAO,IAAI,EAAE;MAAA,CAC3D;IACH;AAEA,SAAA,YAAiC,CAAC,SAAS;AAEzC,aAAO,UAAU;QACf,QAAQ,QAAA,OAAA,SAAA,KAAM;QACd,mBAAmB,QAAA,OAAA,SAAA,KAAM;QACzB,MAAM,KAAK;MAAA,CACL;IACV;AAEA,SAAA,YAAiC,CAAC,SAAS;AAEzC,aAAO,UAAU;QACf,QAAQ,QAAA,OAAA,SAAA,KAAM;QACd,mBAAmB,QAAA,OAAA,SAAA,KAAM;QACzB,MAAM,KAAK;MAAA,CACL;IACV;AAEA,SAAA,gBAAyC,CAAC,SAAS;AAC1C,aAAA,cAAc,EAAE,GAAG,MAAM,MAAM,KAAK,IAAI,QAAQ,MAAA,CAAc;IACvE;AAEA,SAAA,gBAAyC,CAAC,SAAS;AAC1C,aAAA,cAAc,EAAE,GAAG,MAAM,MAAM,KAAK,IAAI,QAAQ,MAAA,CAAc;IACvE;AAEA,SAAA,cAAc,MAET;AACH,YAAM,SAAS,UAAU;AAClB,aAAA,YAAY,EAAE,MAAM,OAAO,WAAW,KAAK,EAAY,EAAE,SAAA,CAAU;IAC5E;AAEA,SAAA,WAAW,CAAC,SAAyB;AACnC,aAAO,SAAS,EAAE,SAAS,KAAK,IAAc,GAAG,KAAA,CAAM;IACzD;AAEA,SAAA,OACEE,cAAAA,QAAM,WAAW,CAAC,OAAO,QAA+C;AACtE,YAAM,SAAS,UAAU;AACzB,YAAM,WAAW,OAAO,WAAW,KAAK,EAAY,EAAE;AACtD,iBAAA,0BAAQ,MAAK,EAAA,KAAU,MAAM,UAAoB,GAAG,MAAA,CAAO;IAAA,CAC5D;EAAA;AAGL;AAEO,IAAM,QAAN,cAuBG,UAiCV;;;;EAIE,YACE,SAcA;AACA,UAAM,OAAO;AAIf,SAAA,WAA+B,CAAC,SAAS;AACvC,aAAO,SAAS;QACd,QAAQ,QAAA,OAAA,SAAA,KAAM;QACd,MAAM,KAAK;QACX,mBAAmB,QAAA,OAAA,SAAA,KAAM;MAAA,CACnB;IACV;AAEA,SAAA,kBAA6C,CAAC,SAAU;AACtD,aAAO,SAAS;QACd,GAAG;QACH,MAAM,KAAK;QACX,QAAQ,CAAC,OAAO,QAAA,OAAA,SAAA,KAAM,UAAS,KAAK,OAAO,EAAE,OAAO,IAAI,EAAE;MAAA,CAC3D;IACH;AAEA,SAAA,YAAiC,CAAC,SAAS;AAEzC,aAAO,UAAU;QACf,QAAQ,QAAA,OAAA,SAAA,KAAM;QACd,mBAAmB,QAAA,OAAA,SAAA,KAAM;QACzB,MAAM,KAAK;MAAA,CACL;IACV;AAEA,SAAA,YAAiC,CAAC,SAAS;AAEzC,aAAO,UAAU;QACf,QAAQ,QAAA,OAAA,SAAA,KAAM;QACd,mBAAmB,QAAA,OAAA,SAAA,KAAM;QACzB,MAAM,KAAK;MAAA,CACL;IACV;AAEA,SAAA,gBAAyC,CAAC,SAAS;AACjD,aAAO,cAAc,EAAE,GAAG,MAAM,MAAM,KAAK,GAAA,CAAW;IACxD;AAEA,SAAA,gBAAyC,CAAC,SAAS;AACjD,aAAO,cAAc,EAAE,GAAG,MAAM,MAAM,KAAK,GAAA,CAAW;IACxD;AAEA,SAAA,cAAc,MAAoC;AAChD,aAAO,YAAY,EAAE,MAAM,KAAK,SAAA,CAAU;IAC5C;AAEA,SAAA,OAAsCA,cAAAA,QAAM;MAC1C,CAAC,OAAO,QAA+C;AACrD,mBAAA,0BAAQ,MAAK,EAAA,KAAU,MAAM,KAAK,UAAoB,GAAG,MAAA,CAAO;MAAA;IAEpE;AArDI,SAAa,WAAW,OAAO,IAAI,YAAY;EAAA;AAsDrD;AAEO,SAAS,YAqBd,SA4BA;AACO,SAAA,IAAI,MAcT,OAAO;AACX;AAIO,SAAS,6BAAwD;AACtE,SAAO,CAOL,YAQG;AACH,WAAO,gBAOL,OAAc;EAClB;AACF;AAKO,IAAM,uBAAuB;AAE7B,IAAM,YAAN,cAUG,cAqBV;;;;EAIE,YACE,SAQA;AACA,UAAM,OAAO;AAIf,SAAA,WAAuC,CAAC,SAAS;AAC/C,aAAO,SAAS;QACd,QAAQ,QAAA,OAAA,SAAA,KAAM;QACd,MAAM,KAAK;QACX,mBAAmB,QAAA,OAAA,SAAA,KAAM;MAAA,CACnB;IACV;AAEA,SAAA,kBAAqD,CAAC,SAAS;AAC7D,aAAO,SAAS;QACd,GAAG;QACH,MAAM,KAAK;QACX,QAAQ,CAAC,OAAO,QAAA,OAAA,SAAA,KAAM,UAAS,KAAK,OAAO,EAAE,OAAO,IAAI,EAAE;MAAA,CAC3D;IACH;AAEA,SAAA,YAAyC,CAAC,SAAS;AAEjD,aAAO,UAAU;QACf,QAAQ,QAAA,OAAA,SAAA,KAAM;QACd,mBAAmB,QAAA,OAAA,SAAA,KAAM;QACzB,MAAM,KAAK;MAAA,CACL;IACV;AAEA,SAAA,YAAyC,CAAC,SAAS;AAEjD,aAAO,UAAU;QACf,QAAQ,QAAA,OAAA,SAAA,KAAM;QACd,mBAAmB,QAAA,OAAA,SAAA,KAAM;QACzB,MAAM,KAAK;MAAA,CACL;IACV;AAEA,SAAA,gBAAiD,CAAC,SAAS;AACzD,aAAO,cAAc,EAAE,GAAG,MAAM,MAAM,KAAK,GAAA,CAAW;IACxD;AAEA,SAAA,gBAAiD,CAAC,SAAS;AACzD,aAAO,cAAc,EAAE,GAAG,MAAM,MAAM,KAAK,GAAA,CAAW;IACxD;AAEA,SAAA,cAAc,MAA8B;AAC1C,aAAO,YAAY,EAAE,MAAM,KAAK,SAAA,CAAU;IAC5C;AAEA,SAAA,OAAgCA,cAAAA,QAAM;MACpC,CAAC,OAAO,QAA+C;AACrD,mBAAA,0BAAQ,MAAK,EAAA,KAAU,MAAM,KAAK,UAAW,GAAG,MAAA,CAAO;MAAA;IAE3D;AArDI,SAAa,WAAW,OAAO,IAAI,YAAY;EAAA;AAsDrD;AAEO,SAAS,gBAQd,SAiBA;AACO,SAAA,IAAI,UAOT,OAAO;AACX;AAEO,SAAS,gBAKd,MAGuB;AAChB,SAAA;AACT;AAkBO,IAAM,gBAAN,cASG,MAcR;EACA,YACE,SAsBA;AACM,UAAA;MACJ,GAAI;MACJ,IAAI;IAAA,CACL;EAAA;AAEL;;;AC1jBO,SAAS,gBAQd,MAC0E;AACnE,SAAA,IAAI,UAA0D,MAAM;IACzE,QAAQ;EACT,CAAA,EAAE;AACL;AAMO,IAAM,YAAN,MAOL;EAGA,YACS,MACP,OACA;AAFO,SAAA,OAAA;AAMT,SAAA,cAAc,CASZ,YAuCG;AACH;QACE,KAAK;QACL;MACF;AACM,YAAA,QAAQ,YAAY,OAAc;AACtC,YAAc,SAAS;AAClB,aAAA;IACT;AA3DE,SAAK,SAAS,SAAA,OAAA,SAAA,MAAO;EAAA;AA4DzB;AAOO,SAAS,gBAId,OAca;AACb;IACE;IACA;EACF;AACA,SAAO,CAAC,aAAa;AACvB;AAEO,IAAM,YAAN,MAAyC;EAK9C,YACE,MAGA;AAKF,SAAA,WAAwC,CAACC,UAAS;AAChD,aAAO,SAAS;QACd,QAAQA,SAAA,OAAA,SAAAA,MAAM;QACd,MAAM,KAAK,QAAQ;QACnB,mBAAmBA,SAAA,OAAA,SAAAA,MAAM;MAAA,CACnB;IACV;AAEA,SAAA,kBAAsD,CAACA,UAAS;AAC9D,aAAO,SAAS;QACd,MAAM,KAAK,QAAQ;QACnB,QAAQ,CAAC,OAAYA,SAAA,OAAA,SAAAA,MAAM,UAASA,MAAK,OAAO,EAAE,OAAO,IAAI,EAAE;MAAA,CAChE;IACH;AAEA,SAAA,YAA0C,CAACA,UAAS;AAElD,aAAO,UAAU;QACf,QAAQA,SAAA,OAAA,SAAAA,MAAM;QACd,mBAAmBA,SAAA,OAAA,SAAAA,MAAM;QACzB,MAAM,KAAK,QAAQ;MAAA,CACb;IACV;AAEA,SAAA,YAA0C,CAACA,UAAS;AAElD,aAAO,UAAU;QACf,QAAQA,SAAA,OAAA,SAAAA,MAAM;QACd,mBAAmBA,SAAA,OAAA,SAAAA,MAAM;QACzB,MAAM,KAAK,QAAQ;MAAA,CACb;IACV;AAEA,SAAA,gBAAkD,CAACA,UAAS;AACnD,aAAA,cAAc,EAAE,GAAGA,OAAM,MAAM,KAAK,QAAQ,GAAA,CAAW;IAChE;AAEA,SAAA,gBAAkD,CAACA,UAAS;AACnD,aAAA,cAAc,EAAE,GAAGA,OAAM,MAAM,KAAK,QAAQ,GAAA,CAAW;IAChE;AAEA,SAAA,cAAc,MAAM;AAClB,YAAM,SAAS,UAAU;AAClB,aAAA,YAAY,EAAE,MAAM,OAAO,WAAW,KAAK,QAAQ,EAAE,EAAE,SAAA,CAAU;IAC1E;AAhDE,SAAK,UAAU;AACb,SAAa,WAAW,OAAO,IAAI,YAAY;EAAA;AAgDrD;AAEO,SAAS,gBAId,IAA2D;AAC3D,SAAO,CAAC,SAA2B;AACjC,WAAO,IAAI,UAAkB;MAC3B;MACA,GAAG;IAAA,CACJ;EACH;AACF;AAEO,SAAS,oBAGd,IAAe;AACR,SAAA,CAAC,SAA2B,IAAI,UAAkB,EAAE,IAAI,GAAG,KAAA,CAAM;AAC1E;A;;;;ACtOA,SAAS,sBAAsB,OAAqB;AAIlD,MAAI,QAAO,SAAA,OAAA,SAAA,MAAO,aAAY,SAAiB,QAAA;AAC/C,SACE,MAAM,QAAQ,WAAW,6CAA6C,KACtE,MAAM,QAAQ,WAAW,2CAA2C,KACpE,MAAM,QAAQ,WAAW,kCAAkC;AAE/D;AAEgB,SAAA,mBAId,UACA,YACA,KAGQ;AACJ,MAAA;AACA,MAAA;AACA,MAAA;AACA,MAAA;AAEJ,QAAM,OAAO,MAAM;AACjB,QAAI,OAAO,aAAa,gBAAe,OAAA,OAAA,SAAA,IAAA,OAAY,OAAO;AACxD,aAAQ,MAAM;AACd,aAAO,QAAQ,QAAQ;IAAA;AAEzB,QAAI,CAAC,aAAa;AAChB,oBAAc,SAAS,EACpB,KAAK,CAAC,QAAQ;AACC,sBAAA;AACP,eAAA,IAAI,cAAc,SAAS;MAAA,CACnC,EACA,MAAM,CAAC,QAAQ;AAIN,gBAAA;AACJ,YAAA,sBAAsB,KAAK,GAAG;AAChC,cACE,iBAAiB,SACjB,OAAO,WAAW,eAClB,OAAO,mBAAmB,aAC1B;AAKM,kBAAAC,cAAa,0BAA0B,MAAM,OAAO;AAC1D,gBAAI,CAAC,eAAe,QAAQA,WAAU,GAAG;AACxB,6BAAA,QAAQA,aAAY,GAAG;AAC7B,uBAAA;YAAA;UACX;QACF;MACF,CACD;IAAA;AAGE,WAAA;EACT;AAEM,QAAA,WAAW,SAAS,KAAK,OAAY;AAEzC,QAAI,QAAQ;AAGV,aAAO,SAAS,OAAO;AACjB,YAAA,IAAI,QAAQ,MAAM;MAAA,CAAE;IAAA;AAE5B,QAAI,OAAO;AAEH,YAAA;IAAA;AAGR,QAAI,CAAC,MAAM;AACT,YAAM,KAAK;IAAA;AAGT,SAAA,OAAA,OAAA,SAAA,IAAA,OAAY,OAAO;AAEnB,iBAAA,0BAAC,YAAW,EAAA,cAAW,0BAAA,QAAA,CAAA,CAAO,GAC3B,UAAM,sBAAc,MAAM,KAAK,EAClC,CAAA;IAAA;AAGG,WAAM,sBAAc,MAAM,KAAK;EACxC;AAEE,WAAiB,UAAU;AAEtB,SAAA;AACT;;;AC5Ba,IAAA,eAA+B,CAAC,YAAY;AAChD,SAAA,IAAI,OAAO,OAAO;AAC3B;AAEO,IAAM,SAAN,cAMG,WAMR;EACA,YACE,SAOA;AACA,UAAM,OAAO;EAAA;AAEjB;A;;;ACjGO,SAAS,sBAGd;EACA;EACA;EACA,GAAG;AACL,GAEG;AAED,SAAO,OAAO;IACZ,GAAG,OAAO;IACV,GAAG;IACH,SAAS;MACP,GAAG,OAAO,QAAQ;MAClB,GAAG,KAAK;IAAA;EACV,CACM;AAER,QAAMC,iBAAgB,iBAAiB;AAEvC,QAAM,eACH,0BAAAA,eAAc,UAAd,EAAuB,OAAO,QAC5B,SAAA,CACH;AAGE,MAAA,OAAO,QAAQ,MAAM;AACvB,eAAQ,0BAAA,OAAO,QAAQ,MAAf,EAAqB,UAAS,SAAA,CAAA;EAAA;AAGjC,SAAA;AACT;AAEO,SAAS,eAGd,EAAE,QAAQ,GAAG,KAAA,GAA2C;AACxD,aAAA,0BACG,uBAAsB,EAAA,QAAiB,GAAG,MACzC,cAAA,0BAAC,SAAA,CAAQ,CAAA,EAAA,CACX;AAEJ;;;ACxCA,SAAS,uBAAuB;AAC9B,QAAM,SAAS,UAAU;AACzB,yBAAuB,QAAQ,IAAI;AACrC;AAKO,SAASC,mBAAkB,QAAkC;AAC7C,uBAAA;AAEjB,MAAA,MAAwC;AAClC,YAAA;MACN;IACF;EAAA;AAGK,SAAA;AACT;AAEO,SAAS,4BACd,SAYoC;;AACf,uBAAA;AAErB,QAAM,SAAS,UAAU;AACnB,QAAA,SAAS,QAAQ,UAAU;AAEjC,MAAI,kBAAkB;AAEtB,MAAI,QAAQ,IAAI;AACI,sBAAA,gCAAgC,QAAQ,EAAE;EAAA,OACvD;AACC,UAAA,WAAU,KAAA,QAAQ,eAAR,OAAA,SAAA,GAAA,KAAA,OAAA;AAChB,QAAI,CAAC,SAAS;AACZ;IAAA;AAEF,sBACE,mBAAmB,SAAS,WAAW,eAAe,OAAO;EAAA;AAG3D,QAAA,aAAa,OAAO,OAAO,cAAc;AACzC,QAAA,SAAQ,KAAA,2BAAA,OAAA,SAAA,GAAwB,MAAM,UAAA;AAC5C,SAAO,SAAA,OAAA,SAAA,MAAQ,eAAA;AACjB;A;;;ACeA,SAAS,oBACP,MACA,WACgB;AAChB,MAAI,SAAS,QAAW;AACf,WAAA;MACL,eAAe,MAAM;MACrB,cAAc;IAChB;EAAA;AAGF,MAAI,mBAAmB,MAAM;AACpB,WAAA;EAAA;AAGL,MAAA,OAAO,SAAS,YAAY;AACxBC,UAAAA,eAAc,QAAQ,aAAa,IAAI;AAE7C,UAAMC,oBAAmB,YAAY;AAC/BD,UAAAA,aAAoB,QAAA,MAAM,KAAK;AAC5B,aAAA;IACT;AAEO,WAAA;MACL,eAAeC;MACf,oBAAoBD;MACpB,cAAc;IAChB;EAAA;AAGF,QAAM,cAAc,QAAQ,KAAK,aAAa,IAAI;AAClD,QAAM,KAAK,KAAK;AAEhB,QAAM,mBAAmB,YAAY;AAC/B,QAAA,eAAe,OAAO,QAAW;AACnC,aAAO,MAAM,GAAG;IAAA;AAEX,WAAA;EACT;AAEO,SAAA;IACL,eAAe;IACf,oBAAoB;IACpB,cAAc,OAAO;EACvB;AACF;AAsBgB,SAAA,WACd,MACA,WACwB;AAClB,QAAA;IACJ;IACA,qBAAqB;IACrB,WAAW;IACX,eAAe;EAAA,IACb,oBAAoB,MAAM,SAAS;AAEvC,QAAM,SAAS,UAAU;AACnB,QAAA,EAAE,QAAA,IAAY;AAEpB,QAAM,CAAC,UAAU,WAAW,IAAU,iBAA0B;IAC9D,QAAQ;IACR,SAAS;IACT,MAAM;IACN,QAAQ;IACR,SAAS;IACT,OAAO;EAAA,CACR;AAED,EAAM,kBAAU,MAAM;AACd,UAAA,oBAAoB,OAAO,kBAAiC;AAChE,eAAS,YACP,UAC0B;AAC1B,cAAM,iBAAiB,OAAO,cAAc,QAAW,QAAQ;AACzD,cAAA,gBAAgB,OAAO,iBAAiB,cAAc;AACxD,YAAA,cAAc,eAAe,QAAW;AAC1C,gBAAM,IAAI,MAAM,+BAA+B,SAAS,IAAI,EAAE;QAAA;AAEzD,eAAA;UACL,SAAS,cAAc,WAAW;UAClC,UAAU,cAAc,WAAW;UACnC,UAAU,eAAe;UACzB,QAAQ,cAAc;UACtB,QAAQ,eAAe;QACzB;MAAA;AAGI,YAAA,UAAU,YAAY,cAAc,eAAe;AACnD,YAAA,OAAO,YAAY,cAAc,YAAY;AAE7C,YAAA,cAAc,MAAM,cAAc;QACtC,QAAQ,cAAc;QACtB;QACA;MAAA,CACD;AACD,UAAI,CAAC,cAAc;AACV,eAAA;MAAA;AAGT,UAAI,CAAC,aAAa;AACT,eAAA;MAAA;AAGT,YAAM,UAAU,IAAI,QAAiB,CAAC,YAAY;AACpC,oBAAA;UACV,QAAQ;UACR;UACA;UACA,QAAQ,cAAc;UACtB,SAAS,MAAM,QAAQ,KAAK;UAC5B,OAAO,MAAM,QAAQ,IAAI;QAAA,CAC1B;MAAA,CACF;AAED,YAAM,mBAAmB,MAAM;AACnB,kBAAA;QACV,QAAQ;QACR,SAAS;QACT,MAAM;QACN,QAAQ;QACR,SAAS;QACT,OAAO;MAAA,CACR;AAEM,aAAA;IACT;AAEO,WAAA,WACH,SACA,QAAQ,MAAM,EAAE,WAAW,mBAAmB,mBAAA,CAAoB;EAAA,GACrE;IACD;IACA;IACA;IACA;IACA;IACA;EAAA,CACD;AAEM,SAAA;AACT;AAEA,IAAM,4BAA4B,CAChC,UACmB;AACnB,MAAI,mBAAmB,OAAO;AACrB,WAAA,EAAE,GAAG,MAAM;EAAA;AAGpB,QAAM,cAAc,QAAQ,MAAM,aAAa,IAAI;AACnD,QAAM,KAAK,MAAM;AAEjB,QAAM,mBAAmB,YAAY;AAC/B,QAAA,eAAe,OAAO,QAAW;AACnC,aAAO,MAAM,GAAG;IAAA;AAEX,WAAA;EACT;AAEO,SAAA;IACL,eAAe;IACf,oBAAoB;IACpB,cAAc,OAAO;EACvB;AACF;AAYO,SAAS,MAAM,MAAwD;AAC5E,QAAM,EAAE,UAAU,GAAG,KAAA,IAAS;AACxB,QAAA,OAAO,0BAA0B,IAAI;AAErC,QAAA,WAAW,WAAW,IAAI;AAChC,SAAO,WACH,OAAO,aAAa,aAClB,SAAS,QAAe,IACxB,WACF;AACN;;;ACnRO,SAAS,gBAMd,MAC2D;AAC3D,SAAO,SAAS;IACd,GAAI;IACJ,QAAQ,CAAC,UACP,KAAK,SAAS,KAAK,OAAO,MAAM,OAAO,IAAI,MAAM;EAAA,CACpD;AACH;;;ACDO,SAAS,YAKd,MAEuC;AACvC,SAAO,eAAe;IACpB,QAAQ,CAAC,WACP,QAAA,OAAA,SAAA,KAAM,UAAS,KAAK,OAAO,MAAM,QAAQ,IAAI,MAAM;EAAA,CAC/C;AACV;;;ACtCO,SAAS,eAAe;AACtB,SAAA,eAAe,EAAE,QAAQ,CAAC,MAAM,EAAE,SAAS,MAAM,gBAAgB,EAAA,CAAG;AAC7E;;;;ACFO,SAAS,MAAM,EAAE,KAAK,OAAO,SAAA,GAAmC;AACrE,UAAQ,KAAK;IACX,KAAK;AACH,iBAAA,0BACG,SAAO,EAAA,GAAG,OAAO,0BAAwB,MACvC,SAAA,CACH;IAEJ,KAAK;AACH,iBAAQ,0BAAA,QAAA,EAAM,GAAG,OAAO,0BAAwB,KAAA,CAAC;IACnD,KAAK;AACH,iBAAQ,0BAAA,QAAA,EAAM,GAAG,OAAO,0BAAwB,KAAA,CAAC;IACnD,KAAK;AAED,iBAAA;QAAC;QAAA;UACE,GAAG;UACJ,yBAAyB,EAAE,QAAQ,SAAgB;QAAA;MACrD;IAEJ,KAAK;AACE,UAAA,SAAkB,MAAc,KAAK;AACxC,mBAAQ,0BAAA,UAAA,EAAQ,GAAG,OAAO,0BAAwB,KAAA,CAAC;MAAA;AAErD,UAAI,OAAO,aAAa;AAEpB,mBAAA;UAAC;UAAA;YACE,GAAG;YACJ,yBAAyB;cACvB,QAAQ;YACV;YACA,0BAAwB;UAAA;QAC1B;AAEG,aAAA;IACT;AACS,aAAA;EAAA;AAEb;A;;;;ACjCO,IAAM,UAAU,MAAM;AAC3B,QAAM,SAAS,UAAU;AAEzB,QAAM,YAAY,eAAe;IAC/B,QAAQ,CAAC,UAAU;AACV,aAAA,MAAM,QAAQ,IAAI,CAAC,UAAU,MAAM,IAAK,EAAE,OAAO,OAAO;IAAA;EACjE,CACD;AAEK,QAAA,OAAsC,gBAAQ,MAAM;AACxD,UAAM,aAAsC,CAAC;AAC7C,UAAM,kBAAwC,CAAC;AAC3C,QAAA;AACH,KAAC,GAAG,SAAS,EAAE,QAAU,EAAA,QAAQ,CAAC,UAAU;AAC1C,OAAC,GAAG,KAAK,EAAE,QAAU,EAAA,QAAQ,CAAC,MAAM;AACnC,YAAI,CAAC,EAAG;AAER,YAAI,EAAE,OAAO;AACX,cAAI,CAAC,OAAO;AACF,oBAAA;cACN,KAAK;cACL,UAAU,EAAE;YACd;UAAA;QACF,OACK;AACC,gBAAA,YAAY,EAAE,QAAQ,EAAE;AAC9B,cAAI,WAAW;AACT,gBAAA,gBAAgB,SAAS,GAAG;AAC9B;YAAA,OACK;AACL,8BAAgB,SAAS,IAAI;YAAA;UAC/B;AAGF,qBAAW,KAAK;YACd,KAAK;YACL,OAAO;cACL,GAAG;YAAA;UACL,CACD;QAAA;MACH,CACD;IAAA,CACF;AAED,QAAI,OAAO;AACT,iBAAW,KAAK,KAAK;IAAA;AAGvB,eAAW,QAAQ;AAEZ,WAAA;EAAA,GACN,CAAC,SAAS,CAAC;AAEd,QAAM,QAAQ,eAAe;IAC3B,QAAQ,CAAC,UACP,MAAM,QACH,IAAI,CAAC,UAAU,MAAM,KAAM,EAC3B,OAAO,OAAO,EACd,KAAK,CAAC,EACN,IAAI,CAAC,UAAU;MACd,KAAK;MACL,OAAO;QACL,GAAG;MAAA;IACL,EACA;IACN,mBAAmB;EAAA,CACpB;AAED,QAAM,cAAc,eAAe;IACjC,QAAQ,CAAC,UAAU;AACjB,YAAME,eAAuC,CAAC;AAExC,YAAA,QACH,IAAI,CAAC,UAAU,OAAO,gBAAgB,MAAM,OAAO,CAAE,EACrD;QAAQ,CAAC,UAAA;;AACR,kBAAA,MAAA,MAAA,MAAA,KAAA,OAAO,QAAP,OAAA,SAAA,GAAY,aAAZ,OAAA,SAAA,GAAsB,OAAO,MAAM,EAAA,MAAnC,OAAA,SAAA,GAAwC,aAAxC,OAAA,SAAA,GACI,OAAO,OAAA,EACR,QAAQ,CAAC,YAAY;AACpBA,yBAAY,KAAK;cACf,KAAK;cACL,OAAO;gBACL,KAAK;gBACL,MAAM;cAAA;YACR,CACD;UACF,CAAA;QAAA;MACL;AAEKA,aAAAA;IACT;IACA,mBAAmB;EAAA,CACpB;AAED,QAAM,cAAc,eAAe;IACjC,QAAQ,CAAC,UAEL,MAAM,QACH,IAAI,CAAC,UAAU,MAAM,WAAY,EACjC,KAAK,CAAC,EACN,OAAO,OAAO,EACjB,IAAI,CAAC,EAAE,UAAU,GAAG,OAAA,OAAc;MAClC,KAAK;MACL,OAAO;QACL,GAAG;MACL;MACA;IAAA,EACA;IACJ,mBAAmB;EAAA,CACpB;AAEM,SAAA;IACL;MACE,GAAG;MACH,GAAG;MACH,GAAG;MACH,GAAG;IACL;IACA,CAAC,MAAM;AACE,aAAA,KAAK,UAAU,CAAC;IAAA;EAE3B;AACF;AAMO,SAAS,cAAc;AAC5B,QAAM,OAAO,QAAQ;AACrB,SAAO,KAAK,IAAI,CAAC,YAAA,6BACd,OAAO,EAAA,GAAG,KAAK,KAAK,YAAY,KAAK,UAAU,GAAG,CAAC,GAAA,CAAI,CACzD;AACH;AAEA,SAAS,OAAU,KAAe,IAAyB;AACnD,QAAA,OAAA,oBAAW,IAAY;AACtB,SAAA,IAAI,OAAO,CAAC,SAAS;AACpB,UAAA,MAAM,GAAG,IAAI;AACf,QAAA,KAAK,IAAI,GAAG,GAAG;AACV,aAAA;IAAA;AAET,SAAK,IAAI,GAAG;AACL,WAAA;EAAA,CACR;AACH;A;;;;ACjJO,IAAM,UAAU,MAAM;AAC3B,QAAM,SAAS,UAAU;AAEzB,QAAM,eAAe,eAAe;IAClC,QAAQ,CAAC,UAAU;;AACjB,YAAMC,gBAAwC,CAAC;AACzC,YAAA,YAAW,KAAA,OAAO,QAAP,OAAA,SAAA,GAAY;AAE7B,UAAI,CAAC,UAAU;AACb,eAAO,CAAC;MAAA;AAGJ,YAAA,QACH,IAAI,CAAC,UAAU,OAAO,gBAAgB,MAAM,OAAO,CAAE,EACrD;QAAQ,CAAC,UACR;;AAAA,kBAAA,MAAAC,MAAA,SAAS,OAAO,MAAM,EAAE,MAAxB,OAAA,SAAAA,IAA2B,WAA3B,OAAA,SAAA,GACI,OAAO,CAAC,MAAM,EAAE,QAAQ,QAAA,EACzB,QAAQ,CAAC,UAAU;AAClBD,0BAAa,KAAK;cAChB,KAAK;cACL,OAAO,MAAM;cACb,UAAU,MAAM;YAAA,CACV;UACT,CAAA;QAAA;MACL;AAEKA,aAAAA;IACT;IACA,mBAAmB;EAAA,CACpB;AAEK,QAAA,EAAE,QAAQ,IAAI,eAAe;IACjC,QAAQ,CAAC,WAAW;MAClB,SACE,MAAM,QACH,IAAI,CAAC,UAAU,MAAM,OAAQ,EAC7B,KAAK,CAAC,EACN,OAAO,OAAO,EACjB,IAAI,CAAC,EAAE,UAAU,GAAG,OAAA,OAAc;QAClC,KAAK;QACL,OAAO;UACL,GAAG;UACH,0BAA0B;QAC5B;QACA;MAAA,EACA;IACJ;EAAA,CACD;AAED,QAAM,aAAa,CAAC,GAAG,SAAS,GAAG,YAAY;AAE/C,aAAA,0BAAA,+BAAA,EAEK,UAAW,WAAA,IAAI,CAAC,OAAO,UAAA,6BACrB,OAAO,EAAA,GAAG,OAAO,KAAK,eAAe,MAAM,GAAG,IAAI,CAAC,GAAA,CAAI,CACzD,EAAA,CACH;AAEJ;", "names": ["subscribe", "React", "useState", "useLayoutEffect", "useEffect", "React", "useRef", "useEffect", "useMemo", "subscribe", "object", "hexadecimal", "jsesc", "result", "hex", "React", "useLayoutEffect", "isLoading", "useLayoutEffect", "jsesc", "_a", "match", "routeId", "key", "startTransition", "useLayoutEffect", "React", "opts", "storageKey", "routerContext", "ScrollRestoration", "shouldBlock", "_customBlockerFn", "preloadMeta", "assetScripts", "_a"]}
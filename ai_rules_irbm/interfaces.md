type user {
ecNumber:string,
name:string,
email:string,
role: "Admin" | "Appraiser" | "Appraisee",
id:string,
departmentId: string,
sectionId: string,
grade:string,
title:string,
reportsTo:string,
}
​
Departments
type department {
headId:string,
title:string,
sections: sections,
id:string,
}

type sections {
headId:string,
title:string,
parentId:string,
parentType: "Department" | "Section",
id:string,
}
​
Contracts
Contract Grades 19–23

type Contract {
departmentId:string,
appraiserId: string,
appraiseeId: string,
evidence: string,
contractType: "Self"|"Other"
status: "Draft" | "Completed"
id:string,
title:string,
}

type Programme {
title: string,
outcome: Outcome[],
contractId: string,
id: string,
}

type Outcome {
title: string,
weight: string,
output: Output[],
contractId: string,
programmeId: string
}

type Output {
unit: string,
weight: string,
prevYearPerf: string,
target: string,
allowableVariance: string,
actualPerformance: string,
rawScore: string,
weightedScore: string,
contractId: string,
outcomeId: string,
programmeId: string,
title: string
}

type BudgetPerformance {
weight: string,
allowableVariance: string,
actualUtilization: string,
actualVariance: string,
rating: string,
weightedScore: string,
contractId: string,
title: string
}
​
Contract Grades 11–18

type Contract {
departmentId:string,
appraiserId: string,
appraises: User[],
evidence: string,
contractType: "Self"|"Other",
status: "Draft" | "Submitted" | "Filled" | "Completed"
id:string,
title:string,
}

type IndividualOutput {
unit: string,
weight: string,
title: string ,
measure:string,
target: string,
allowableVariance: string,
allowableVariance: string,
actualVariance: string,
Score: string,
weightedScore: string,
contractId: string,  
actions: string,
evidenceOfResults: string,
}
​
Contract Grades 6–10

type Contract {
departmentId:string,
appraiserId: string,
appraiseId:string,
evidence: string,
contractType: "Self"|"Other",
status: "Draft" | "Submitted" | "Filled" | "Completed"
id:string,
title:string,
}

type IndividualActivity {
unit: string,
weight: string,
title: string ,
measure:string,
target: string,
allowableVariance: string,
allowableVariance: string,
actualVariance: string,
Score: string,
weightedScore: string,
contractId: string,  
actions: string,
evidenceOfResults: string,
}
​

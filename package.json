{"name": "irbm_web", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"dev": "vinxi dev", "build": "vinxi build", "start": "vinxi start"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/postcss": "^4.1.8", "@tanstack/react-router": "^1.120.20", "@tanstack/react-start": "^1.120.20", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.513.0", "next-themes": "^0.4.6", "postcss": "^8.5.4", "react": "^19.1.0", "react-dom": "^19.1.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.8", "tw-animate-css": "^1.3.4", "vinxi": "^0.5.6"}, "devDependencies": {"@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.1", "typescript": "^5.8.3", "vite-tsconfig-paths": "^5.1.4"}}